#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Vista semanal del calendario
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QScrollArea, QFrame, QGridLayout)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont


class WeekDayWidget(QFrame):
    """Widget para un día de la semana"""
    
    def __init__(self, date, events=None, parent=None):
        super().__init__(parent)
        self.date = date
        self.events = events or []
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurar la interfaz del día"""
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(1)
        self.setMinimumHeight(120)
        self.setStyleSheet("""
            WeekDayWidget {
                background-color: #FFFFFF;
                border: 1px solid #D0D0D0;
                margin: 1px;
            }
            WeekDayWidget:hover {
                background-color: #F0F8FF;
                border: 2px solid #3498DB;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(3)
        
        # Cabecera del día
        header_layout = QHBoxLayout()
        
        # Día de la semana
        day_name = self.date.toString("dddd")
        day_label = QLabel(day_name)
        day_label.setFont(QFont("Arial", 10, QFont.Bold))
        day_label.setStyleSheet("color: #2C3E50;")
        header_layout.addWidget(day_label)
        
        header_layout.addStretch()
        
        # Número del día
        day_number = self.date.toString("d")
        number_label = QLabel(day_number)
        number_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        # Resaltar si es hoy
        if self.date == QDate.currentDate():
            number_label.setStyleSheet("""
                color: white;
                background-color: #3498DB;
                border-radius: 12px;
                padding: 2px 6px;
            """)
        else:
            number_label.setStyleSheet("color: #2C3E50;")
        
        header_layout.addWidget(number_label)
        
        layout.addLayout(header_layout)
        
        # Eventos del día
        events_container = QWidget()
        events_layout = QVBoxLayout(events_container)
        events_layout.setContentsMargins(0, 0, 0, 0)
        events_layout.setSpacing(2)
        
        if self.events:
            # Mostrar hasta 3 eventos, el resto como "..."
            events_to_show = self.events[:3]
            
            for event in events_to_show:
                event_widget = self.create_event_widget(event)
                events_layout.addWidget(event_widget)
            
            # Si hay más eventos, mostrar contador
            if len(self.events) > 3:
                more_label = QLabel(f"... y {len(self.events) - 3} más")
                more_label.setFont(QFont("Arial", 8))
                more_label.setStyleSheet("color: #7F8C8D; font-style: italic;")
                events_layout.addWidget(more_label)
        
        events_layout.addStretch()
        layout.addWidget(events_container)
    
    def create_event_widget(self, event):
        """Crear widget para un evento"""
        event_frame = QFrame()
        event_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {event.get('category_color', '#E8F4FD')}30;
                border-left: 3px solid {event.get('category_color', '#3498DB')};
                border-radius: 3px;
                padding: 2px;
                margin: 1px;
            }}
        """)
        
        layout = QVBoxLayout(event_frame)
        layout.setContentsMargins(3, 2, 3, 2)
        layout.setSpacing(1)
        
        # Título del evento
        title_label = QLabel(event.get('title', 'Sin título'))
        title_label.setFont(QFont("Arial", 8, QFont.Bold))
        title_label.setStyleSheet("color: #2C3E50;")
        title_label.setWordWrap(True)
        layout.addWidget(title_label)
        
        # Hora (si no es todo el día)
        if not event.get('all_day', 0):
            start_time = event.get('time_start', '')
            if start_time:
                time_str = start_time[:5]  # HH:MM
                time_label = QLabel(time_str)
                time_label.setFont(QFont("Arial", 7))
                time_label.setStyleSheet("color: #7F8C8D;")
                layout.addWidget(time_label)
        
        return event_frame


class WeekViewWidget(QWidget):
    """Vista semanal del calendario"""
    
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.current_date = QDate.currentDate()
        
        self.setup_ui()
        self.load_week_events()
    
    def setup_ui(self):
        """Configurar la interfaz de la vista semanal"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # Cabecera con la semana
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #27AE60;
                color: white;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 10, 10, 10)
        
        self.week_label = QLabel()
        self.week_label.setAlignment(Qt.AlignCenter)
        self.week_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.week_label.setStyleSheet("color: white;")
        header_layout.addWidget(self.week_label)
        
        layout.addWidget(header_frame)
        
        # Grid de días de la semana
        self.days_grid = QGridLayout()
        self.days_grid.setSpacing(2)
        
        # Widget contenedor para el grid
        grid_container = QWidget()
        grid_container.setLayout(self.days_grid)
        
        # Área de scroll
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(grid_container)
        
        layout.addWidget(scroll_area)
        
        # Crear widgets de días
        self.create_week_days()
        
        # Actualizar etiqueta de semana
        self.update_week_label()
    
    def create_week_days(self):
        """Crear widgets para los días de la semana"""
        # Limpiar grid existente
        while self.days_grid.count():
            child = self.days_grid.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        # Obtener el lunes de la semana actual
        days_to_monday = self.current_date.dayOfWeek() - 1
        monday = self.current_date.addDays(-days_to_monday)
        
        # Crear widgets para cada día de la semana
        for i in range(7):
            day_date = monday.addDays(i)
            
            # Obtener eventos para este día
            events = self.get_events_for_date(day_date)
            
            # Crear widget del día
            day_widget = WeekDayWidget(day_date, events)
            
            # Agregar al grid (una fila, 7 columnas)
            self.days_grid.addWidget(day_widget, 0, i)
    
    def get_events_for_date(self, date):
        """Obtener eventos para una fecha específica"""
        date_str = date.toString("yyyy-MM-dd")
        events = self.data_manager.get_events_by_date(date_str)
        
        # Ordenar eventos por hora
        events.sort(key=lambda x: (
            not x.get('all_day', 0),  # Todo el día primero
            x.get('time_start', '00:00:00')
        ))
        
        return events
    
    def set_date(self, date):
        """Establecer la fecha (se mostrará la semana que contiene esta fecha)"""
        self.current_date = date
        self.update_week_label()
        self.load_week_events()
    
    def update_week_label(self):
        """Actualizar la etiqueta de la semana"""
        # Obtener el lunes y domingo de la semana
        days_to_monday = self.current_date.dayOfWeek() - 1
        monday = self.current_date.addDays(-days_to_monday)
        sunday = monday.addDays(6)
        
        # Formatear texto de la semana
        if monday.month() == sunday.month():
            week_text = f"Semana del {monday.day()} al {sunday.day()} de {monday.toString('MMMM yyyy')}"
        else:
            week_text = f"Semana del {monday.toString('d MMM')} al {sunday.toString('d MMM yyyy')}"
        
        self.week_label.setText(week_text)
    
    def load_week_events(self):
        """Cargar eventos de la semana y actualizar vista"""
        self.create_week_days()
    
    def go_to_previous_week(self):
        """Ir a la semana anterior"""
        self.current_date = self.current_date.addDays(-7)
        self.update_week_label()
        self.load_week_events()
    
    def go_to_next_week(self):
        """Ir a la semana siguiente"""
        self.current_date = self.current_date.addDays(7)
        self.update_week_label()
        self.load_week_events()

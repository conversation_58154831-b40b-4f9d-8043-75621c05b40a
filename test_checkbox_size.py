#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba específico para verificar el tamaño y alineación de checkboxes:
- Checkboxes el doble de grandes (20x20px)
- Alineación perfecta con el texto del evento
- Diferentes tipos de eventos para probar la alineación
"""

import sys
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QTextEdit, QSplitter)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from event_item_widget import EventListWidget
from data_manager import DataManager
from styles import apply_styles


class TestCheckboxSizeWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Tamaño y Alineación de Checkboxes")
        self.setGeometry(100, 100, 1100, 800)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Checkboxes Grandes y Alineados")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 10px; color: #2C3E50;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "🎯 ESPECIFICACIONES DE CHECKBOXES:\n\n"
            "✅ TAMAÑO DOBLE: 20x20px (antes 10x10px)\n"
            "✅ ALINEACIÓN: A la altura del texto principal del evento\n"
            "✅ ESPACIADO: 8px entre checkbox y contenido\n"
            "✅ VISUAL: Marca ✓ visible cuando está marcado\n"
            "✅ HOVER: Efecto visual al pasar el mouse\n\n"
            "🔧 PRUEBA: Observa la alineación y tamaño en diferentes tipos de eventos"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #2980B9; font-size: 11pt; background-color: #EBF5FB; padding: 15px; border-radius: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Splitter para dividir la vista
        splitter = QSplitter(Qt.Horizontal)
        
        # Panel izquierdo: Lista de eventos
        left_panel = QWidget()
        left_panel.setMaximumWidth(600)
        left_layout = QVBoxLayout(left_panel)
        
        events_title = QLabel("📋 Eventos con Checkboxes Grandes:")
        events_title.setFont(QFont("Arial", 14, QFont.Bold))
        events_title.setStyleSheet("color: #2C3E50; margin: 10px 0;")
        left_layout.addWidget(events_title)
        
        # Lista de eventos
        self.events_list = EventListWidget()
        self.events_list.event_completed_changed.connect(self.on_event_completed_changed)
        self.events_list.event_edit_requested.connect(self.on_event_edit_requested)
        left_layout.addWidget(self.events_list)
        
        splitter.addWidget(left_panel)
        
        # Panel derecho: Controles y información
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Controles
        controls_title = QLabel("🔧 Controles de Prueba:")
        controls_title.setFont(QFont("Arial", 12, QFont.Bold))
        controls_title.setStyleSheet("color: #2C3E50; margin: 10px 0;")
        right_layout.addWidget(controls_title)
        
        # Botones de prueba
        btn_create_short = QPushButton("Crear Eventos Cortos")
        btn_create_short.setMinimumHeight(35)
        btn_create_short.setStyleSheet("background-color: #3498DB; color: white; font-weight: bold;")
        btn_create_short.clicked.connect(self.create_short_events)
        right_layout.addWidget(btn_create_short)
        
        btn_create_long = QPushButton("Crear Eventos Largos")
        btn_create_long.setMinimumHeight(35)
        btn_create_long.setStyleSheet("background-color: #9B59B6; color: white; font-weight: bold;")
        btn_create_long.clicked.connect(self.create_long_events)
        right_layout.addWidget(btn_create_long)
        
        btn_create_mixed = QPushButton("Crear Eventos Mixtos")
        btn_create_mixed.setMinimumHeight(35)
        btn_create_mixed.setStyleSheet("background-color: #E67E22; color: white; font-weight: bold;")
        btn_create_mixed.clicked.connect(self.create_mixed_events)
        right_layout.addWidget(btn_create_mixed)
        
        btn_toggle_all = QPushButton("Alternar Todos")
        btn_toggle_all.setMinimumHeight(35)
        btn_toggle_all.setStyleSheet("background-color: #27AE60; color: white; font-weight: bold;")
        btn_toggle_all.clicked.connect(self.toggle_all_events)
        right_layout.addWidget(btn_toggle_all)
        
        btn_clear = QPushButton("🗑️ Limpiar Todo")
        btn_clear.setMinimumHeight(30)
        btn_clear.setStyleSheet("background-color: #E74C3C; color: white;")
        btn_clear.clicked.connect(self.clear_all_events)
        right_layout.addWidget(btn_clear)
        
        right_layout.addWidget(QLabel(""))  # Espaciador
        
        # Información técnica
        tech_title = QLabel("📐 Especificaciones Técnicas:")
        tech_title.setFont(QFont("Arial", 11, QFont.Bold))
        tech_title.setStyleSheet("color: #2C3E50; margin: 10px 0;")
        right_layout.addWidget(tech_title)
        
        tech_info = QLabel(
            "🔲 CHECKBOX:\n"
            "• Tamaño: 20x20px (doble del estándar)\n"
            "• Borde: 2px sólido\n"
            "• Radio: 4px (esquinas redondeadas)\n"
            "• Marca: ✓ verde cuando marcado\n\n"
            "📏 ALINEACIÓN:\n"
            "• Checkbox: AlignTop\n"
            "• Texto principal: altura mínima 20px\n"
            "• Margen superior: 2px\n"
            "• Espaciado horizontal: 8px\n\n"
            "🎨 COLORES:\n"
            "• Sin marcar: borde #BDC3C7\n"
            "• Marcado: fondo #27AE60\n"
            "• Hover: borde #3498DB\n\n"
            "⚡ INTERACCIÓN:\n"
            "• Clic en checkbox: cambiar estado\n"
            "• Clic en evento: editar\n"
            "• Tooltip informativo"
        )
        tech_info.setAlignment(Qt.AlignLeft)
        tech_info.setStyleSheet("color: #34495E; font-size: 9pt; background-color: #F8F9FA; padding: 10px; border-radius: 5px; border-left: 3px solid #3498DB;")
        tech_info.setWordWrap(True)
        right_layout.addWidget(tech_info)
        
        right_layout.addStretch()
        
        splitter.addWidget(right_panel)
        layout.addWidget(splitter)
        
        # Área de resultados
        self.results_area = QTextEdit()
        self.results_area.setMaximumHeight(100)
        self.results_area.setPlaceholderText("Los resultados de las pruebas aparecerán aquí...")
        self.results_area.setStyleSheet("background-color: #FAFAFA; border: 1px solid #BDC3C7; border-radius: 3px;")
        layout.addWidget(self.results_area)
        
        # Cargar eventos existentes
        self.refresh_events()
    
    def create_short_events(self):
        """Crear eventos con títulos cortos para probar alineación"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        events_data = [
            {
                'title': 'Café',
                'date': today,
                'time_start': '08:00:00',
                'time_end': '08:15:00',
                'priority': 1,
                'color': '#27AE60',
                'description': '',
                'completed': 0
            },
            {
                'title': 'Llamada',
                'date': today,
                'time_start': '09:00:00',
                'time_end': '09:30:00',
                'priority': 3,
                'color': '#E67E22',
                'description': '',
                'completed': 1
            },
            {
                'title': 'Email',
                'date': today,
                'time_start': '10:00:00',
                'time_end': '10:15:00',
                'priority': 2,
                'color': '#F39C12',
                'description': '',
                'completed': 0
            }
        ]
        
        self.create_events(events_data, "eventos cortos")
    
    def create_long_events(self):
        """Crear eventos con títulos largos y descripciones para probar alineación"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        events_data = [
            {
                'title': 'Reunión de planificación estratégica del proyecto Q4',
                'date': today,
                'time_start': '11:00:00',
                'time_end': '12:30:00',
                'priority': 4,
                'color': '#E74C3C',
                'description': 'Reunión muy importante para definir los objetivos del último trimestre del año y establecer las prioridades del equipo',
                'completed': 0
            },
            {
                'title': 'Presentación de resultados mensuales a la dirección',
                'date': today,
                'time_start': '14:00:00',
                'time_end': '15:30:00',
                'priority': 3,
                'color': '#E67E22',
                'description': 'Presentar los KPIs del mes anterior y proponer mejoras para el próximo período',
                'completed': 1
            },
            {
                'title': 'Sesión de brainstorming para nuevas funcionalidades',
                'date': today,
                'time_start': '16:00:00',
                'time_end': '17:00:00',
                'priority': 2,
                'color': '#F39C12',
                'description': 'Generar ideas creativas para mejorar la experiencia del usuario',
                'completed': 0
            }
        ]
        
        self.create_events(events_data, "eventos largos")
    
    def create_mixed_events(self):
        """Crear una mezcla de eventos para prueba completa"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        events_data = [
            {
                'title': 'Stand-up',
                'date': today,
                'time_start': '09:15:00',
                'time_end': '09:30:00',
                'priority': 2,
                'color': '#F39C12',
                'description': 'Daily scrum meeting',
                'completed': 1
            },
            {
                'title': 'Revisión de código con el equipo de desarrollo backend',
                'date': today,
                'time_start': '10:30:00',
                'time_end': '11:30:00',
                'priority': 3,
                'color': '#E67E22',
                'description': '',
                'completed': 0
            },
            {
                'title': 'Almuerzo de trabajo',
                'date': today,
                'time_start': '13:00:00',
                'time_end': '14:00:00',
                'priority': 1,
                'color': '#27AE60',
                'description': 'Almuerzo con el cliente para discutir nuevos requerimientos y establecer una mejor relación comercial',
                'completed': 0
            },
            {
                'title': 'Demo',
                'date': today,
                'time_start': '17:00:00',
                'time_end': '17:30:00',
                'priority': 4,
                'color': '#E74C3C',
                'description': '',
                'completed': 1
            }
        ]
        
        self.create_events(events_data, "eventos mixtos")
    
    def create_events(self, events_data, event_type):
        """Crear eventos en la base de datos"""
        created_count = 0
        for event_data in events_data:
            event_id = self.data_manager.add_event(event_data)
            if event_id:
                created_count += 1
        
        self.results_area.append(f"✅ Creados {created_count} {event_type}")
        self.refresh_events()
    
    def toggle_all_events(self):
        """Alternar el estado de todos los eventos"""
        today = datetime.now().strftime("%Y-%m-%d")
        events = self.data_manager.get_events_by_date(today)
        
        updated_count = 0
        for event in events:
            new_state = 1 - event.get('completed', 0)  # Alternar estado
            if self.data_manager.update_event_completed(event['id'], new_state):
                updated_count += 1
        
        self.results_area.append(f"🔄 Alternados {updated_count} eventos")
        self.refresh_events()
    
    def clear_all_events(self):
        """Limpiar todos los eventos"""
        today = datetime.now().strftime("%Y-%m-%d")
        events = self.data_manager.get_events_by_date(today)
        
        deleted_count = 0
        for event in events:
            if self.data_manager.delete_event(event['id']):
                deleted_count += 1
        
        self.results_area.append(f"🗑️ Eliminados {deleted_count} eventos")
        self.refresh_events()
    
    def refresh_events(self):
        """Actualizar la lista de eventos"""
        today = datetime.now().strftime("%Y-%m-%d")
        events = self.data_manager.get_events_by_date(today)
        self.events_list.set_events(events)
        
        self.results_area.append(f"🔄 Lista actualizada: {len(events)} eventos")
    
    def on_event_completed_changed(self, event_id, completed):
        """Manejar cambio en el estado de completado"""
        success = self.data_manager.update_event_completed(event_id, completed)
        if success:
            status = "✅ completado" if completed else "⭕ activo"
            self.results_area.append(f"Evento {event_id} marcado como {status}")
    
    def on_event_edit_requested(self, event_id):
        """Manejar solicitud de edición de evento"""
        self.results_area.append(f"✏️ Edición solicitada para evento {event_id}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestCheckboxSizeWindow()
    window.show()
    
    sys.exit(app.exec_())

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import threading
import time
import datetime
from PyQt5.QtCore import QDate, QTime, QDateTime


class NotificationService:
    def __init__(self, data_manager, app_signals):
        self.data_manager = data_manager
        self.app_signals = app_signals
        self.running = False
        self.thread = None
    
    def start(self):
        """Iniciar el servicio de notificaciones"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._monitor_events)
        self.thread.daemon = True
        self.thread.start()
    
    def stop(self):
        """Detener el servicio de notificaciones"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(1)
    
    def update_settings(self):
        """Actualizar configuraciones del servicio"""
        # No es necesario hacer nada especial aquí porque
        # leemos la configuración en cada ciclo de verificación
        pass
    
    def _monitor_events(self):
        """Monitorear eventos con recordatorios pendientes"""
        while self.running:
            try:
                self._check_reminders()
            except Exception as e:
                print(f"Error en servicio de notificaciones: {e}")
            
            # Verificar cada 30 segundos
            time.sleep(30)
    
    def _check_reminders(self):
        """Verificar recordatorios pendientes"""
        # Obtener eventos con recordatorios
        events = self.data_manager.get_events_with_reminders()
        if not events:
            return
        
        now = datetime.datetime.now()
        
        for event in events:
            # Verificar fecha y hora del evento
            try:
                event_date = datetime.datetime.strptime(event['date'], "%Y-%m-%d").date()
                
                if event.get('all_day', 0) == 1:
                    event_time = datetime.time(0, 0, 0)
                else:
                    time_str = event.get('time_start', '00:00:00')
                    event_time = datetime.datetime.strptime(time_str, "%H:%M:%S").time()
                
                event_datetime = datetime.datetime.combine(event_date, event_time)
                
                # Calcular el tiempo para el recordatorio
                reminder_minutes = int(event.get('reminder', '0'))
                reminder_time = event_datetime - datetime.timedelta(minutes=reminder_minutes)
                
                # Si es hora de mostrar el recordatorio
                if now >= reminder_time and now <= event_datetime:
                    # Emitir señal de recordatorio
                    self.app_signals.reminder_triggered.emit(event['id'])
                    
                    # Marcar como enviado
                    self.data_manager.mark_reminder_sent(event['id'])
            except Exception as e:
                print(f"Error al procesar recordatorio para evento {event['id']}: {e}")

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para las nuevas vistas del calendario
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QLabel, QPushButton, QFrame, QTextEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

from data_manager import DataManager
from calendar_view_selector import CalendarViewSelector
from day_view_widget import DayViewWidget
from week_view_widget import WeekViewWidget
from year_view_widget import YearViewWidget
from custom_calendar import CustomCalendarWidget


class CalendarViewsTestWindow(QMainWindow):
    """Ventana de prueba para las vistas del calendario"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🗓️ Prueba de Vistas del Calendario")
        self.setGeometry(100, 100, 1200, 800)
        
        # Inicializar data manager
        self.data_manager = DataManager()
        
        self.setup_ui()
        self.create_test_events()
    
    def setup_ui(self):
        """Configurar la interfaz de prueba"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Título
        title_label = QLabel("🗓️ PRUEBA DE VISTAS DEL CALENDARIO")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                background-color: #3498DB;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # Layout principal horizontal
        content_layout = QHBoxLayout()
        
        # Panel izquierdo: Selector de vistas
        left_panel = QFrame()
        left_panel.setFrameStyle(QFrame.StyledPanel)
        left_panel.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        left_layout = QVBoxLayout(left_panel)
        
        # Selector de vistas
        self.view_selector = CalendarViewSelector()
        self.view_selector.view_changed.connect(self.on_view_changed)
        left_layout.addWidget(self.view_selector)
        
        # Información de la vista actual
        info_label = QLabel("ℹ️ INFORMACIÓN")
        info_label.setFont(QFont("Arial", 10, QFont.Bold))
        info_label.setStyleSheet("color: #2C3E50; margin: 10px 0px 5px 0px;")
        left_layout.addWidget(info_label)
        
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(150)
        self.info_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #D0D0D0;
                border-radius: 5px;
                padding: 5px;
                font-size: 9pt;
            }
        """)
        left_layout.addWidget(self.info_text)
        
        # Botones de prueba
        test_label = QLabel("🧪 PRUEBAS")
        test_label.setFont(QFont("Arial", 10, QFont.Bold))
        test_label.setStyleSheet("color: #2C3E50; margin: 10px 0px 5px 0px;")
        left_layout.addWidget(test_label)
        
        self.create_events_btn = QPushButton("Crear Eventos de Prueba")
        self.create_events_btn.clicked.connect(self.create_test_events)
        self.create_events_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        left_layout.addWidget(self.create_events_btn)
        
        self.clear_events_btn = QPushButton("Limpiar Eventos")
        self.clear_events_btn.clicked.connect(self.clear_test_events)
        self.clear_events_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        left_layout.addWidget(self.clear_events_btn)
        
        left_layout.addStretch()
        
        left_panel.setMaximumWidth(200)
        content_layout.addWidget(left_panel)
        
        # Panel derecho: Vistas del calendario
        right_panel = QFrame()
        right_panel.setFrameStyle(QFrame.StyledPanel)
        right_panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        right_layout = QVBoxLayout(right_panel)
        
        # Navegación
        nav_layout = QHBoxLayout()
        
        self.prev_btn = QPushButton("◀ Anterior")
        self.prev_btn.clicked.connect(self.previous_period)
        nav_layout.addWidget(self.prev_btn)
        
        self.period_label = QLabel()
        self.period_label.setAlignment(Qt.AlignCenter)
        self.period_label.setFont(QFont("Arial", 12, QFont.Bold))
        nav_layout.addWidget(self.period_label)
        
        self.next_btn = QPushButton("Siguiente ▶")
        self.next_btn.clicked.connect(self.next_period)
        nav_layout.addWidget(self.next_btn)
        
        right_layout.addLayout(nav_layout)
        
        # Contenedor de vistas
        from PyQt5.QtWidgets import QStackedWidget
        self.views_stack = QStackedWidget()
        
        # Vista diaria
        self.day_view = DayViewWidget(self.data_manager)
        self.views_stack.addWidget(self.day_view)
        
        # Vista semanal
        self.week_view = WeekViewWidget(self.data_manager)
        self.views_stack.addWidget(self.week_view)
        
        # Vista mensual
        self.month_view = CustomCalendarWidget(self.data_manager)
        self.views_stack.addWidget(self.month_view)
        
        # Vista anual
        self.year_view = YearViewWidget(self.data_manager)
        self.views_stack.addWidget(self.year_view)
        
        right_layout.addWidget(self.views_stack)
        
        content_layout.addWidget(right_panel)
        
        main_layout.addLayout(content_layout)
        
        # Variables de estado
        self.current_view = 'month'
        self.current_date = QDate.currentDate()
        
        # Configurar vista inicial
        self.update_view_info()
        self.update_period_label()
    
    def on_view_changed(self, view_type):
        """Manejar cambio de vista"""
        self.current_view = view_type
        
        if view_type == 'day':
            self.views_stack.setCurrentWidget(self.day_view)
            self.day_view.set_date(self.current_date)
        elif view_type == 'week':
            self.views_stack.setCurrentWidget(self.week_view)
            self.week_view.set_date(self.current_date)
        elif view_type == 'month':
            self.views_stack.setCurrentWidget(self.month_view)
            self.month_view.setSelectedDate(self.current_date)
        elif view_type == 'year':
            self.views_stack.setCurrentWidget(self.year_view)
            self.year_view.set_year(self.current_date.year())
        
        self.update_view_info()
        self.update_period_label()
    
    def update_view_info(self):
        """Actualizar información de la vista actual"""
        info_texts = {
            'day': """📆 VISTA DIARIA:
• Muestra intervalos de 30 minutos
• Eventos organizados por hora
• Eventos de todo el día en 00:00
• Navegación día a día""",
            
            'week': """📅 VISTA SEMANAL:
• Muestra 7 días de la semana
• Eventos resumidos por día
• Máximo 3 eventos visibles por día
• Navegación semana a semana""",
            
            'month': """🗓️ VISTA MENSUAL:
• Vista tradicional del calendario
• Cuadrados de prioridad en días
• Números indican cantidad de eventos
• Navegación mes a mes""",
            
            'year': """📊 VISTA ANUAL:
• 12 mini calendarios del año
• Días marcados con eventos
• Vista compacta de todo el año
• Navegación año a año"""
        }
        
        self.info_text.setText(info_texts.get(self.current_view, ""))
    
    def update_period_label(self):
        """Actualizar etiqueta del período"""
        if self.current_view == 'day':
            self.period_label.setText(self.current_date.toString("dddd, d 'de' MMMM 'de' yyyy"))
        elif self.current_view == 'week':
            days_to_monday = self.current_date.dayOfWeek() - 1
            monday = self.current_date.addDays(-days_to_monday)
            sunday = monday.addDays(6)
            
            if monday.month() == sunday.month():
                week_text = f"Semana del {monday.day()} al {sunday.day()} de {monday.toString('MMMM yyyy')}"
            else:
                week_text = f"Semana del {monday.toString('d MMM')} al {sunday.toString('d MMM yyyy')}"
            
            self.period_label.setText(week_text)
        elif self.current_view == 'month':
            self.period_label.setText(self.current_date.toString("MMMM yyyy"))
        elif self.current_view == 'year':
            self.period_label.setText(str(self.current_date.year()))
    
    def previous_period(self):
        """Ir al período anterior"""
        if self.current_view == 'day':
            self.current_date = self.current_date.addDays(-1)
            self.day_view.set_date(self.current_date)
        elif self.current_view == 'week':
            self.current_date = self.current_date.addDays(-7)
            self.week_view.set_date(self.current_date)
        elif self.current_view == 'month':
            self.current_date = self.current_date.addMonths(-1)
            self.month_view.setSelectedDate(self.current_date)
        elif self.current_view == 'year':
            self.current_date = self.current_date.addYears(-1)
            self.year_view.set_year(self.current_date.year())
        
        self.update_period_label()
    
    def next_period(self):
        """Ir al período siguiente"""
        if self.current_view == 'day':
            self.current_date = self.current_date.addDays(1)
            self.day_view.set_date(self.current_date)
        elif self.current_view == 'week':
            self.current_date = self.current_date.addDays(7)
            self.week_view.set_date(self.current_date)
        elif self.current_view == 'month':
            self.current_date = self.current_date.addMonths(1)
            self.month_view.setSelectedDate(self.current_date)
        elif self.current_view == 'year':
            self.current_date = self.current_date.addYears(1)
            self.year_view.set_year(self.current_date.year())
        
        self.update_period_label()
    
    def create_test_events(self):
        """Crear eventos de prueba"""
        today = QDate.currentDate()
        
        # Eventos para hoy
        events = [
            {
                'title': 'Reunión matutina',
                'description': 'Reunión de equipo',
                'date': today.toString("yyyy-MM-dd"),
                'time_start': '09:00:00',
                'time_end': '10:00:00',
                'all_day': 0,
                'category': 'Negocios',
                'category_color': '#3498DB',
                'priority': 'orange'
            },
            {
                'title': 'Almuerzo con cliente',
                'description': 'Reunión comercial',
                'date': today.toString("yyyy-MM-dd"),
                'time_start': '13:30:00',
                'time_end': '15:00:00',
                'all_day': 0,
                'category': 'Negocios',
                'category_color': '#3498DB',
                'priority': 'red'
            },
            {
                'title': 'Evento de todo el día',
                'description': 'Conferencia',
                'date': today.toString("yyyy-MM-dd"),
                'all_day': 1,
                'category': 'Importante',
                'category_color': '#E74C3C',
                'priority': 'red'
            }
        ]
        
        # Eventos para esta semana
        for i in range(1, 7):
            date = today.addDays(i)
            events.append({
                'title': f'Evento día {i}',
                'description': f'Evento de prueba para el día {i}',
                'date': date.toString("yyyy-MM-dd"),
                'time_start': '10:00:00',
                'time_end': '11:00:00',
                'all_day': 0,
                'category': 'Familia',
                'category_color': '#27AE60',
                'priority': 'green'
            })
        
        # Guardar eventos
        for event in events:
            self.data_manager.add_event(event)
        
        # Actualizar vistas
        self.refresh_all_views()
        print(f"✅ Creados {len(events)} eventos de prueba")
    
    def clear_test_events(self):
        """Limpiar eventos de prueba"""
        # Obtener todos los eventos
        all_events = self.data_manager.get_all_events()
        
        # Eliminar todos los eventos
        for event in all_events:
            self.data_manager.delete_event(event['id'])
        
        # Actualizar vistas
        self.refresh_all_views()
        print("🗑️ Eventos eliminados")
    
    def refresh_all_views(self):
        """Actualizar todas las vistas"""
        self.day_view.load_day_events()
        self.week_view.load_week_events()
        self.month_view.refresh_events()
        self.year_view.load_year_view()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    window = CalendarViewsTestWindow()
    window.show()
    
    sys.exit(app.exec_())

# 🎠 Carrusel de Banners - Documentación Completa

## 📋 Descripción General

El **Carrusel de Banners** es un sistema avanzado que permite mostrar múltiples banners rotativos en la parte inferior del calendario. Los banners cambian automáticamente con transiciones suaves y duración configurable.

## 🚀 Características Principales

### ✨ **Funcionalidades Clave:**
- **Múltiples Banners**: Agregue tantos banners como desee
- **Rotación Automática**: Cambio automático con duración personalizable por banner
- **Transiciones Suaves**: <PERSON><PERSON><PERSON>, desvanecer o cambio instantáneo
- **Pausa Inteligente**: Se pausa automáticamente al pasar el mouse
- **Soporte Multimedia**: Imágenes (PNG, JPG, GIF, BMP) y texto alternativo
- **Enlaces Clickeables**: Cada banner puede tener su propia URL
- **Altura Configurable**: Ajustable entre 40-200 píxeles
- **Gestión Completa**: <PERSON><PERSON><PERSON><PERSON>, editar, duplicar y eliminar banners
- **Vista Previa**: Previsualización en tiempo real de cambios

### 🎨 **Interfaz de Usuario:**
- **Botón Carrusel (🎠)**: Acceso directo a la configuración
- **Anchura Completa**: Ocupa toda la anchura inferior del calendario
- **Diseño Responsive**: Se adapta automáticamente al redimensionar
- **Indicadores Visuales**: Estados claros (activo, pausado, sin banners)

## 📁 Estructura de Archivos

### **`banner_carousel_widget.py`** - Archivo Principal
```python
# Clases principales:
- BannerItem: Modelo de datos para banners individuales
- BannerEditDialog: Diálogo para editar banners
- BannerManagementDialog: Gestión completa de banners
- BannerCarouselWidget: Widget principal del carrusel
```

### **Integración en `calendar_tab.py`:**
```python
# Importación y uso:
from banner_carousel_widget import BannerCarouselWidget
self.banner_carousel = BannerCarouselWidget(self.data_manager)
self.banner_carousel.banner_clicked.connect(self.on_banner_clicked)
main_layout.addWidget(self.banner_carousel)
```

## 🔧 Configuración del Carrusel

### **Acceso a Configuración:**
1. Haga clic en el botón **🎠** en la esquina superior derecha del carrusel
2. Se abrirá el diálogo "🎠 Gestión del Carrusel de Banners"

### **Configuración Global:**
- **Estado**: Habilitar/deshabilitar carrusel completo
- **Altura**: 40-200 píxeles (valor por defecto: 67px)
- **Transición**: Deslizar, Desvanecer, Instantáneo
- **Velocidad**: 100-2000 milisegundos (valor por defecto: 500ms)
- **Pausa al Hover**: Pausar automáticamente al pasar el mouse

### **Gestión de Banners:**
- **➕ Nuevo**: Crear nuevo banner
- **✏️ Editar**: Modificar banner seleccionado
- **🗑️ Eliminar**: Eliminar banner con confirmación
- **📋 Duplicar**: Crear copia de banner existente

## 📝 Configuración de Banner Individual

### **Campos Disponibles:**
- **Estado**: Banner activo/inactivo
- **Título**: Nombre descriptivo del banner
- **Imagen**: Archivo de imagen (opcional)
- **URL**: Enlace de destino (opcional)
- **Texto Alternativo**: Texto cuando no hay imagen
- **Duración**: Tiempo de visualización (1-30 segundos)

### **Vista Previa en Tiempo Real:**
- Actualización automática al cambiar campos
- Indicadores de estado por colores:
  - 🟢 Verde: Banner válido con imagen
  - 🔵 Azul: Banner de texto válido
  - 🟡 Amarillo: Advertencias (imagen inválida)
  - 🔴 Rojo: Errores o banner deshabilitado

## 🎯 Flujo de Uso

### **Configuración Inicial:**
1. **Abrir Configuración**: Clic en botón 🎠
2. **Habilitar Carrusel**: Marcar "Carrusel activo"
3. **Configurar Altura**: Ajustar según necesidades
4. **Agregar Primer Banner**: Clic en "➕ Nuevo"
5. **Completar Datos**: Título, imagen/texto, URL, duración
6. **Guardar**: Aplicar cambios

### **Agregar Más Banners:**
1. **Nuevo Banner**: Clic en "➕ Nuevo" en gestión
2. **Configurar**: Completar todos los campos
3. **Duración Personalizada**: Cada banner puede tener duración diferente
4. **Activar**: Marcar como activo
5. **Guardar**: El carrusel se actualiza automáticamente

### **Editar Banners Existentes:**
1. **Seleccionar**: Clic en banner en la lista
2. **Editar**: Clic en "✏️ Editar"
3. **Modificar**: Cambiar campos necesarios
4. **Vista Previa**: Verificar cambios en tiempo real
5. **Guardar**: Aplicar modificaciones

## 🔄 Comportamiento del Carrusel

### **Rotación Automática:**
- **Inicio**: Automático al tener 2+ banners activos
- **Duración**: Individual por banner (configurada en cada banner)
- **Ciclo**: Continuo, vuelve al primer banner al terminar
- **Pausa**: Automática al pasar mouse (si está habilitado)
- **Reanudación**: Automática al quitar mouse

### **Transiciones:**
- **Deslizar**: Efecto de deslizamiento horizontal
- **Desvanecer**: Efecto de fade in/out
- **Instantáneo**: Cambio inmediato sin animación

### **Estados del Carrusel:**
- **Sin Banners**: "🎠 Sin banners configurados - Haga clic para agregar"
- **Carrusel Deshabilitado**: "🎠 Carrusel deshabilitado - Haga clic para configurar"
- **Un Banner**: Muestra banner estático (sin rotación)
- **Múltiples Banners**: Rotación automática activa

## 💾 Almacenamiento de Datos

### **Configuración Guardada:**
```json
{
  "banner_carousel_config": {
    "enabled": true,
    "height": 67,
    "transition_type": "Deslizar",
    "transition_speed": 500,
    "pause_on_hover": true,
    "banners": [
      {
        "id": "banner_1234567890",
        "enabled": true,
        "title": "Mi Banner",
        "image_path": "/ruta/imagen.png",
        "url": "https://ejemplo.com",
        "alt_text": "Texto alternativo",
        "duration": 5000
      }
    ]
  }
}
```

### **Persistencia:**
- **Automática**: Configuración se guarda automáticamente
- **Base de Datos**: Almacenado en SQLite del data_manager
- **Recuperación**: Carga automática al iniciar aplicación

## 🧪 Scripts de Prueba

### **`test_banner_carousel.py`** - Prueba Completa
```bash
python test_banner_carousel.py
```
**Funcionalidades de prueba:**
- Interfaz de demostración completa
- Botones para crear banners de ejemplo
- Información en tiempo real del carrusel
- Log de eventos y clics
- Controles de limpieza y configuración

### **Banners de Ejemplo:**
- **Banner 1**: Bienvenida (3 segundos)
- **Banner 2**: Promocional (4 segundos)
- **Banner 3**: Informativo (5 segundos)

## 🔧 Integración en Aplicación Principal

### **En `calendar_tab.py`:**
```python
# Importar carrusel
from banner_carousel_widget import BannerCarouselWidget

# Crear instancia
self.banner_carousel = BannerCarouselWidget(self.data_manager)

# Conectar señales
self.banner_carousel.banner_clicked.connect(self.on_banner_clicked)

# Agregar al layout (anchura completa)
main_layout.addWidget(self.banner_carousel)
```

### **Manejo de Eventos:**
```python
def on_banner_clicked(self, url):
    """Manejar clic en banner del carrusel"""
    print(f"Banner clickeado: {url}")
    # Agregar lógica adicional (tracking, analytics, etc.)
```

## 🎨 Personalización Visual

### **Estilos del Carrusel:**
- **Borde**: 2px sólido #BDC3C7
- **Hover**: Borde azul #3498DB
- **Fondo**: Blanco #FFFFFF
- **Botón**: Verde turquesa con icono 🎠

### **Estados Visuales:**
- **Activo**: Borde normal, contenido visible
- **Hover**: Borde azul, fondo ligeramente gris
- **Sin Contenido**: Texto informativo en gris
- **Error**: Texto en rojo para errores

## 🚀 Ejecución

### **Aplicación Principal:**
```bash
python main.py
```

### **Prueba del Carrusel:**
```bash
python test_banner_carousel.py
```

### **Ubicación:**
- **Posición**: Parte inferior del calendario
- **Anchura**: Completa (100% del ancho disponible)
- **Altura**: Configurable (40-200px)

## 💡 Consejos de Uso

### **Mejores Prácticas:**
1. **Duración Óptima**: 3-7 segundos por banner
2. **Número de Banners**: 3-5 banners para mejor experiencia
3. **Imágenes**: Usar imágenes de alta calidad y tamaño apropiado
4. **Texto Alternativo**: Siempre incluir texto alternativo
5. **URLs**: Verificar que los enlaces funcionen correctamente

### **Recomendaciones de Diseño:**
- **Imágenes**: 600x67 píxeles (o proporcional a altura configurada)
- **Formato**: PNG para transparencias, JPG para fotos
- **Texto**: Conciso y llamativo
- **Colores**: Contrastantes con el fondo del calendario

¡El carrusel de banners está listo para usar con todas las funcionalidades implementadas! 🎉

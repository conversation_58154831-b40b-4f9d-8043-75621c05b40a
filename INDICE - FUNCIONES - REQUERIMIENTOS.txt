PyQt5>=5.15.0
pytz>=2021.1

cypher_calendar/
│
├── main.py                  # Punto de entrada principal
├── data_manager.py          # Gestor de datos y persistencia
├── signals.py               # Señales de la aplicación
├── calendar_tab.py          # Interfaz de la pestaña de calendario
├── event_dialog.py          # Diálogo para crear/editar eventos
├── reminders_tab.py         # Interfaz de la pestaña de recordatorios
├── notes_tab.py             # Interfaz de la pestaña de notas
├── settings_dialog.py       # Diálogo de configuración
├── notification_service.py  # Servicio de notificaciones
├── categories_dialog.py     # Diálogo para gestionar categorías
├── utils.py                 # Funciones de utilidad
├── requirements.txt         # Dependencias de la aplicación
│
└── icons/                   # Directorio para iconos
    ├── calendar.png
    ├── event_add.png
    ├── note_add.png
    └── ...

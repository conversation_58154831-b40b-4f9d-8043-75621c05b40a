#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar el banner simple de vuelta
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from simple_banner_widget import SimpleBannerWidget
from world_clocks_widget import WorldClocksWidget
from data_manager import DataManager
from styles import apply_styles


class TestSimpleBannerReturnWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔄 Banner Simple - De Vuelta")
        self.setGeometry(100, 100, 900, 600)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("🔄 Banner Simple - Vuelta al Concepto Original")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 15px; color: #2C3E50;")
        layout.addWidget(title)
        
        # Descripción
        description = QLabel(
            "✅ CONCEPTO SIMPLE RESTAURADO:\n\n"
            "🎯 Un solo banner debajo de los relojes mundiales\n"
            "🖼️ Imagen con enlace clickeable\n"
            "⚙️ Configuración simple y directa\n"
            "📐 Altura configurable\n"
            "🔗 URL personalizable\n"
            "📝 Texto alternativo\n\n"
            "❌ ELIMINADO: Carrusel complejo con múltiples banners\n"
            "✅ MANTENIDO: Funcionalidad esencial y simplicidad"
        )
        description.setAlignment(Qt.AlignCenter)
        description.setStyleSheet(
            "margin: 15px; color: #2980B9; font-size: 11pt; "
            "background-color: #EBF5FB; padding: 20px; border-radius: 8px;"
        )
        description.setWordWrap(True)
        layout.addWidget(description)
        
        # Simulación del layout original
        simulation_frame = QFrame()
        simulation_frame.setStyleSheet(
            "border: 2px solid #3498DB; border-radius: 8px; "
            "background-color: #F8F9FA; margin: 10px;"
        )
        sim_layout = QVBoxLayout(simulation_frame)
        
        sim_title = QLabel("📱 Simulación del Layout Original")
        sim_title.setFont(QFont("Arial", 14, QFont.Bold))
        sim_title.setAlignment(Qt.AlignCenter)
        sim_title.setStyleSheet("color: #2C3E50; margin: 15px;")
        sim_layout.addWidget(sim_title)
        
        # Área de eventos simulada
        events_area = QWidget()
        events_area.setMinimumHeight(150)
        events_area.setStyleSheet(
            "background-color: #ECF0F1; border: 1px solid #BDC3C7; "
            "border-radius: 5px; margin: 5px;"
        )
        events_layout = QVBoxLayout(events_area)
        
        events_label = QLabel("📅 Área de Eventos del Calendario")
        events_label.setAlignment(Qt.AlignCenter)
        events_label.setStyleSheet("color: #7F8C8D; font-size: 12pt; font-style: italic;")
        events_layout.addWidget(events_label)
        
        sim_layout.addWidget(events_area)
        
        # Widget de relojes mundiales con banner
        self.world_clocks = WorldClocksWidget(self.data_manager)
        sim_layout.addWidget(self.world_clocks)
        
        layout.addWidget(simulation_frame)
        
        # Panel de controles
        controls_frame = QFrame()
        controls_frame.setStyleSheet(
            "border: 1px solid #BDC3C7; border-radius: 5px; "
            "background-color: #FAFAFA; margin: 5px;"
        )
        controls_layout = QHBoxLayout(controls_frame)
        
        # Información del banner
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        info_title = QLabel("ℹ️ Información del Banner:")
        info_title.setFont(QFont("Arial", 12, QFont.Bold))
        info_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        info_layout.addWidget(info_title)
        
        info_text = QLabel(
            "📍 UBICACIÓN: Debajo de los relojes mundiales\n"
            "📐 TAMAÑO: Altura configurable (40-200px)\n"
            "🖼️ CONTENIDO: Imagen o texto alternativo\n"
            "🔗 ENLACE: URL clickeable opcional\n"
            "⚙️ CONFIGURACIÓN: Botón ⚙ en esquina\n"
            "💾 PERSISTENCIA: Configuración guardada\n"
            "🎨 ESTILO: Integrado con tema de la app"
        )
        info_text.setStyleSheet(
            "color: #34495E; font-size: 9pt; background-color: #F8F9FA; "
            "padding: 10px; border-radius: 5px; border: 1px solid #E0E0E0;"
        )
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        
        controls_layout.addWidget(info_widget)
        
        # Botones de acción
        buttons_widget = QWidget()
        buttons_layout = QVBoxLayout(buttons_widget)
        
        buttons_title = QLabel("🔧 Acciones:")
        buttons_title.setFont(QFont("Arial", 12, QFont.Bold))
        buttons_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        buttons_layout.addWidget(buttons_title)
        
        # Botón para configurar banner
        btn_configure = QPushButton("⚙️ Configurar Banner")
        btn_configure.setMinimumHeight(35)
        btn_configure.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        btn_configure.clicked.connect(self.configure_banner)
        buttons_layout.addWidget(btn_configure)
        
        # Botón para crear imagen de ejemplo
        btn_create_image = QPushButton("🖼️ Crear Imagen de Ejemplo")
        btn_create_image.setMinimumHeight(35)
        btn_create_image.setStyleSheet("""
            QPushButton {
                background-color: #9B59B6;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #8E44AD;
            }
        """)
        btn_create_image.clicked.connect(self.create_sample_image)
        buttons_layout.addWidget(btn_create_image)
        
        # Botón para limpiar configuración
        btn_clear = QPushButton("🗑️ Limpiar Banner")
        btn_clear.setMinimumHeight(35)
        btn_clear.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        btn_clear.clicked.connect(self.clear_banner)
        buttons_layout.addWidget(btn_clear)
        
        buttons_layout.addStretch()
        controls_layout.addWidget(buttons_widget)
        
        layout.addWidget(controls_frame)
        
        # Estado inicial
        self.show_status("🔄 Banner simple restaurado - Listo para configurar")
    
    def configure_banner(self):
        """Configurar el banner"""
        try:
            # Acceder al banner del widget de relojes
            if hasattr(self.world_clocks, 'banner'):
                self.world_clocks.banner.configure_banner()
                self.show_status("⚙️ Configuración del banner abierta")
            else:
                self.show_status("❌ Error: Banner no encontrado")
        except Exception as e:
            self.show_status(f"❌ Error configurando banner: {str(e)}")
    
    def create_sample_image(self):
        """Crear imagen de ejemplo para el banner"""
        try:
            from PyQt5.QtGui import QPixmap, QPainter, QColor, QLinearGradient, QBrush
            from PyQt5.QtCore import QRect
            
            # Crear imagen de ejemplo
            width, height = 400, 80
            pixmap = QPixmap(width, height)
            
            # Crear painter
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            
            # Crear gradiente
            gradient = QLinearGradient(0, 0, width, 0)
            gradient.setColorAt(0, QColor("#3498DB"))
            gradient.setColorAt(0.5, QColor("#9B59B6"))
            gradient.setColorAt(1, QColor("#E74C3C"))
            
            # Llenar fondo
            painter.fillRect(0, 0, width, height, QBrush(gradient))
            
            # Texto
            painter.setPen(QColor("white"))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(QRect(0, 0, width, height), Qt.AlignCenter, "🎯 Banner Simple de Ejemplo")
            
            painter.end()
            
            # Guardar imagen
            sample_path = "banner_simple_ejemplo.png"
            success = pixmap.save(sample_path, "PNG")
            
            if success:
                self.show_status(f"🖼️ Imagen creada: {sample_path}")
            else:
                self.show_status("❌ Error guardando imagen")
                
        except Exception as e:
            self.show_status(f"❌ Error creando imagen: {str(e)}")
    
    def clear_banner(self):
        """Limpiar configuración del banner"""
        try:
            # Limpiar configuración
            config = {
                'enabled': False,
                'image_path': '',
                'url': '',
                'alt_text': '',
                'height': 67
            }
            
            self.data_manager.set_config('banner_config', config)
            
            # Recargar banner
            if hasattr(self.world_clocks, 'banner'):
                self.world_clocks.banner.load_banner_config()
            
            self.show_status("🗑️ Banner limpiado")
            
        except Exception as e:
            self.show_status(f"❌ Error limpiando banner: {str(e)}")
    
    def show_status(self, message):
        """Mostrar mensaje de estado"""
        print(f"[STATUS] {message}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestSimpleBannerReturnWindow()
    window.show()
    
    sys.exit(app.exec_())

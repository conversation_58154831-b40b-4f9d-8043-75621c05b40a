#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar que la barra separadora es completamente responsive
sin límites artificiales
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QTextEdit, QSplitter, QPushButton, QHBoxLayout)
from PyQt5.QtCore import Qt

from world_clocks_widget import WorldClocksWidget
from data_manager import DataManager
from styles import apply_styles


class TestFullyResponsiveSplitterWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Splitter Completamente Responsive")
        self.setGeometry(100, 100, 1000, 700)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Splitter Sin Límites")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "🔧 CORRECCIONES APLICADAS:\n"
            "✅ Removidas alturas mínimas fijas de widgets\n"
            "✅ Permitido colapso completo (setChildrenCollapsible(True))\n"
            "✅ Eliminada validación restrictiva de tamaños guardados\n"
            "✅ Reducidos tamaños mínimos de relojes individuales\n"
            "✅ Flexibilizadas alturas de labels internos\n\n"
            "🎯 PRUEBA: Arrastra la barra separadora hasta los extremos.\n"
            "Debería poder colapsar completamente cualquier sección."
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #27AE60; font-size: 10pt; background-color: #F0F8F0; padding: 10px; border-radius: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Crear splitter vertical (como en el calendario real)
        splitter = QSplitter(Qt.Vertical)
        splitter.setHandleWidth(8)
        splitter.setChildrenCollapsible(True)  # Permitir colapso completo
        
        # Sección superior: Simulación de eventos (SIN altura mínima)
        events_section = QWidget()
        # NO establecer altura mínima para permitir colapso completo
        events_layout = QVBoxLayout(events_section)
        
        events_title = QLabel("📅 Sección de Eventos (Sin límites)")
        events_title.setStyleSheet("font-size: 14pt; font-weight: bold; color: #2C3E50; padding: 5px;")
        events_layout.addWidget(events_title)
        
        events_content = QTextEdit()
        events_content.setPlainText(
            "Esta sección NO tiene altura mínima fija.\n\n"
            "Puedes:\n"
            "• Arrastrar la barra hasta arriba para colapsar esta sección\n"
            "• Arrastrar la barra hasta abajo para colapsar los relojes\n"
            "• Redimensionar libremente sin restricciones\n\n"
            "La barra separadora debería ser completamente flexible."
        )
        events_layout.addWidget(events_content)
        
        # Sección inferior: Relojes mundiales (SIN altura mínima fija)
        self.world_clocks = WorldClocksWidget(self.data_manager)
        # NO establecer altura mínima para permitir colapso completo
        
        # Agregar secciones al splitter
        splitter.addWidget(events_section)
        splitter.addWidget(self.world_clocks)
        
        # Configurar comportamiento del splitter
        splitter.setStretchFactor(0, 1)  # Eventos pueden estirarse
        splitter.setStretchFactor(1, 1)  # Relojes también pueden estirarse
        
        # Tamaños iniciales
        splitter.setSizes([300, 300])
        
        # Conectar señal para mostrar cambios
        splitter.splitterMoved.connect(lambda: self.show_splitter_info(splitter))
        
        # Guardar referencia
        self.test_splitter = splitter
        
        layout.addWidget(splitter)
        
        # Botones de prueba
        buttons_layout = QHBoxLayout()
        
        btn_collapse_top = QPushButton("Colapsar Eventos")
        btn_collapse_top.clicked.connect(lambda: splitter.setSizes([0, 600]))
        buttons_layout.addWidget(btn_collapse_top)
        
        btn_collapse_bottom = QPushButton("Colapsar Relojes")
        btn_collapse_bottom.clicked.connect(lambda: splitter.setSizes([600, 0]))
        buttons_layout.addWidget(btn_collapse_bottom)
        
        btn_equal = QPushButton("50/50")
        btn_equal.clicked.connect(lambda: splitter.setSizes([300, 300]))
        buttons_layout.addWidget(btn_equal)
        
        btn_info = QPushButton("Mostrar Tamaños")
        btn_info.clicked.connect(lambda: self.show_splitter_info(splitter))
        buttons_layout.addWidget(btn_info)
        
        layout.addLayout(buttons_layout)
        
        # Información en tiempo real
        self.info_label = QLabel("Tamaños actuales: [300, 300]")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setStyleSheet("margin: 10px; color: #2980B9; font-size: 12pt; font-weight: bold;")
        layout.addWidget(self.info_label)
    
    def show_splitter_info(self, splitter):
        """Mostrar información actual del splitter"""
        sizes = splitter.sizes()
        total = sum(sizes)
        if total > 0:
            percentages = [round((size/total)*100, 1) for size in sizes]
            self.info_label.setText(f"Tamaños: {sizes} | Porcentajes: {percentages}%")
        else:
            self.info_label.setText(f"Tamaños: {sizes}")
        
        print(f"Splitter actualizado - Tamaños: {sizes}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestFullyResponsiveSplitterWindow()
    window.show()
    
    sys.exit(app.exec_())

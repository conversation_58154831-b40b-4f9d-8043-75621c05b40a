#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtCore import QObject, pyqtSignal


class ApplicationSignals(QObject):
    """Clase para gestionar las señales de la aplicación"""
    
    # Señales para eventos
    event_added = pyqtSignal(int)       # ID del evento añadido
    event_modified = pyqtSignal(int)    # ID del evento modificado
    event_deleted = pyqtSignal(int)     # ID del evento eliminado
    
    # Señales para notas
    note_added = pyqtSignal(int)        # ID de la nota añadida
    note_modified = pyqtSignal(int)     # ID de la nota modificada
    note_deleted = pyqtSignal(int)      # ID de la nota eliminada
    
    # Señales para recordatorios
    reminder_triggered = pyqtSignal(int)  # ID del recordatorio activado
    reminder_completed = pyqtSignal(int)  # ID del recordatorio marcado como completado
    
    # Señales para categorías
    category_added = pyqtSignal(int)    # ID de la categoría añadida
    category_modified = pyqtSignal(int) # ID de la categoría modificada
    category_deleted = pyqtSignal(int)  # ID de la categoría eliminada

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de recuperación para restaurar versión funcional del calendario
"""

import os
import shutil
import sys
from pathlib import Path


def backup_current_files():
    """Hacer backup de los archivos actuales"""
    backup_dir = Path("backup_current")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "banner_widget.py",
        "calendar_tab.py",
        "world_clocks_widget.py",
        "protected_storage.py",
        "file_encryption.py"
    ]
    
    print("📦 Haciendo backup de archivos actuales...")
    for file in files_to_backup:
        if Path(file).exists():
            shutil.copy2(file, backup_dir / file)
            print(f"   ✅ {file} -> backup_current/{file}")
    
    print(f"✅ Backup completado en: {backup_dir.absolute()}")


def restore_simple_version():
    """Restaurar versión simplificada funcional"""
    print("🔄 Restaurando versión simplificada...")
    
    # Usar el banner simplificado en calendar_tab.py
    calendar_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QListWidgetItem, 
                            QPushButton, QLabel, QLineEdit, QMessageBox,
                            QMenu, QAction, QSplitter)
from PyQt5.QtCore import Qt, QDate, pyqtSlot
from PyQt5.QtGui import QIcon, QColor, QBrush, QFont

from event_dialog import EventDialog
from calendar_widget import CalendarWidget
from events_list_widget import EventsListWidget
from world_clocks_widget import WorldClocksWidget
from simple_banner_widget import SimpleBannerWidget


class CalendarTab(QWidget):
    """Pestaña del calendario con banner simplificado"""
    
    def __init__(self, data_manager, app_signals, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.app_signals = app_signals
        self.selected_date = QDate.currentDate()
        
        self.setup_ui()
        self.load_events()
    
    def setup_ui(self):
        """Configurar la interfaz de usuario"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Layout principal horizontal
        main_splitter = QSplitter(Qt.Horizontal)
        
        # Widget del calendario
        calendar_widget = QWidget()
        calendar_layout = QVBoxLayout(calendar_widget)
        
        # Etiqueta de fecha
        self.date_label = QLabel()
        self.date_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.date_label.setAlignment(Qt.AlignCenter)
        self.date_label.setStyleSheet("color: #2C3E50; margin: 5px;")
        calendar_layout.addWidget(self.date_label)
        
        # Widget del calendario
        self.calendar = CalendarWidget(self.data_manager)
        self.calendar.selectionChanged.connect(self.date_changed)
        calendar_layout.addWidget(self.calendar)
        
        # Panel derecho con eventos y relojes
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # Splitter vertical para eventos y relojes
        right_splitter = QSplitter(Qt.Vertical)
        
        # Widget de eventos
        events_widget = QWidget()
        events_layout = QVBoxLayout(events_widget)
        
        # Barra de herramientas de eventos
        toolbar_layout = QHBoxLayout()
        
        # Campo de búsqueda
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Buscar eventos...")
        self.search_edit.textChanged.connect(self.search_events)
        toolbar_layout.addWidget(self.search_edit)
        
        # Botón agregar evento
        self.btn_add = QPushButton("Agregar Evento")
        self.btn_add.setIcon(QIcon("icons/add.png"))
        self.btn_add.clicked.connect(self.add_event)
        toolbar_layout.addWidget(self.btn_add)
        
        events_layout.addLayout(toolbar_layout)
        
        # Lista de eventos
        self.events_list = EventsListWidget(self.data_manager)
        self.events_list.event_completed_changed.connect(self.on_event_completed_changed)
        self.events_list.event_edit_requested.connect(self.on_event_edit_requested)
        events_layout.addWidget(self.events_list)
        
        # Widget de relojes mundiales
        self.world_clocks = WorldClocksWidget(self.data_manager)
        
        # Agregar widgets al splitter vertical
        right_splitter.addWidget(events_widget)
        right_splitter.addWidget(self.world_clocks)
        
        # Configurar splitter vertical
        right_splitter.setHandleWidth(8)
        right_splitter.setChildrenCollapsible(False)
        right_splitter.setSizes([300, 200])
        
        # Guardar referencia al splitter
        self.right_splitter = right_splitter
        right_splitter.splitterMoved.connect(self.save_splitter_state)
        
        right_layout.addWidget(right_splitter)
        
        # Agregar widgets al splitter principal
        main_splitter.addWidget(calendar_widget)
        main_splitter.addWidget(right_widget)
        
        # Configurar splitter principal
        main_splitter.setHandleWidth(8)
        main_splitter.setChildrenCollapsible(False)
        main_splitter.setStretchFactor(0, 2)
        main_splitter.setStretchFactor(1, 1)
        main_splitter.setSizes([int(self.width() * 0.6), int(self.width() * 0.4)])
        
        main_layout.addWidget(main_splitter)
        
        # Banner simplificado en la parte inferior
        self.banner = SimpleBannerWidget(self.data_manager)
        main_layout.addWidget(self.banner)
        
        # Cargar estado del splitter
        self.load_splitter_state()
        
        # Establecer fecha actual
        self.update_date_label()
    
    def update_date_label(self):
        """Actualizar etiqueta de fecha"""
        self.date_label.setText(self.selected_date.toString("dddd, d 'de' MMMM 'de' yyyy"))
    
    def date_changed(self, date):
        """Manejar cambio de fecha"""
        self.selected_date = date
        self.update_date_label()
        self.load_events()
    
    def load_events(self):
        """Cargar eventos para la fecha seleccionada"""
        date_str = self.selected_date.toString("yyyy-MM-dd")
        events = self.data_manager.get_events_by_date(date_str)
        self.events_list.set_events(events)
    
    def add_event(self):
        """Agregar nuevo evento"""
        dialog = EventDialog(self.data_manager, self)
        dialog.date_edit.setDate(self.selected_date)
        
        if dialog.exec_():
            event_data = dialog.get_event_data()
            event_id = self.data_manager.add_event(event_data)
            if event_id:
                self.app_signals.event_added.emit(event_id)
                self.load_events()
                self.calendar.refresh_events()
    
    def on_event_completed_changed(self, event_id, completed):
        """Manejar cambio de estado de evento"""
        success = self.data_manager.update_event_completed(event_id, completed)
        if success:
            self.calendar.refresh_events()
    
    def on_event_edit_requested(self, event_id):
        """Manejar solicitud de edición"""
        event = self.data_manager.get_event(event_id)
        if not event:
            return
        
        dialog = EventDialog(self.data_manager, self, event=event)
        if dialog.exec_():
            event_data = dialog.get_event_data()
            success = self.data_manager.update_event(event_id, event_data)
            if success:
                self.app_signals.event_modified.emit(event_id)
                self.load_events()
                self.calendar.refresh_events()
    
    def search_events(self, query):
        """Buscar eventos"""
        if not query:
            self.load_events()
            return
        
        events = self.data_manager.search_events(query)
        self.events_list.set_events(events)
    
    @pyqtSlot()
    def refresh_events(self):
        """Actualizar eventos"""
        self.load_events()
        self.calendar.refresh_events()
    
    def save_splitter_state(self):
        """Guardar estado del splitter"""
        if hasattr(self, 'right_splitter'):
            sizes = self.right_splitter.sizes()
            self.data_manager.set_config('calendar_splitter_sizes', sizes)
    
    def load_splitter_state(self):
        """Cargar estado del splitter"""
        if hasattr(self, 'right_splitter'):
            saved_sizes = self.data_manager.get_config('calendar_splitter_sizes', None)
            if saved_sizes and len(saved_sizes) == 2:
                if all(size >= 0 for size in saved_sizes):
                    self.right_splitter.setSizes(saved_sizes)
'''
    
    # Escribir archivo calendar_tab.py simplificado
    with open("calendar_tab.py", "w", encoding="utf-8") as f:
        f.write(calendar_content)
    
    print("✅ calendar_tab.py restaurado con banner simplificado")


def main():
    """Función principal del script de recuperación"""
    print("🔧 Script de Recuperación del Calendario")
    print("=" * 50)
    
    print("Este script restaurará una versión funcional del calendario.")
    print("¿Qué desea hacer?")
    print("1. Hacer backup de archivos actuales")
    print("2. Restaurar versión simplificada")
    print("3. Ambos (recomendado)")
    print("4. Salir")
    
    try:
        choice = input("\nSeleccione una opción (1-4): ").strip()
        
        if choice == "1":
            backup_current_files()
        elif choice == "2":
            restore_simple_version()
        elif choice == "3":
            backup_current_files()
            restore_simple_version()
        elif choice == "4":
            print("👋 Saliendo...")
            return
        else:
            print("❌ Opción no válida")
            return
        
        print("\n🎉 Operación completada!")
        print("💡 Ahora puede ejecutar: python main.py")
        
    except KeyboardInterrupt:
        print("\n👋 Operación cancelada por el usuario")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")


if __name__ == "__main__":
    main()

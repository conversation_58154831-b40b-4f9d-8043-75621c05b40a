#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pestaña de contactos con aspecto de Excel y pestañas alfabéticas
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QHeaderView, QTabWidget, QFrame, QLineEdit,
                            QDialog, QFormLayout, QTextEdit, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont


class ContactDialog(QDialog):
    """Diálogo para agregar/editar contactos"""

    def __init__(self, contact_data=None, parent=None):
        super().__init__(parent)
        self.contact_data = contact_data
        self.setWindowTitle("Nuevo Contacto" if not contact_data else "Editar Contacto")
        self.setModal(True)
        self.resize(400, 350)

        self.setup_ui()

        if contact_data:
            self.load_contact_data()

    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)

        # Formulario
        form_layout = QFormLayout()

        # Nombre
        self.name_edit = QLineEdit()
        self.name_edit.setStyleSheet("padding: 5px; font-size: 11pt;")
        form_layout.addRow("Nombre:", self.name_edit)

        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setStyleSheet("padding: 5px; font-size: 11pt;")
        form_layout.addRow("Email:", self.email_edit)

        # Teléfono
        self.phone_edit = QLineEdit()
        self.phone_edit.setStyleSheet("padding: 5px; font-size: 11pt;")
        form_layout.addRow("Teléfono:", self.phone_edit)

        # Dirección
        self.address_edit = QLineEdit()
        self.address_edit.setStyleSheet("padding: 5px; font-size: 11pt;")
        form_layout.addRow("Dirección:", self.address_edit)

        # Comentarios
        self.comments_edit = QTextEdit()
        self.comments_edit.setMaximumHeight(80)
        self.comments_edit.setStyleSheet("padding: 5px; font-size: 11pt;")
        form_layout.addRow("Comentarios:", self.comments_edit)

        layout.addLayout(form_layout)

        # Botones
        buttons_layout = QHBoxLayout()

        self.save_button = QPushButton("Guardar")
        self.save_button.clicked.connect(self.accept)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        buttons_layout.addWidget(self.save_button)

        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        buttons_layout.addWidget(self.cancel_button)

        layout.addLayout(buttons_layout)

    def load_contact_data(self):
        """Cargar datos del contacto para edición"""
        if self.contact_data:
            self.name_edit.setText(self.contact_data.get('name', ''))
            self.email_edit.setText(self.contact_data.get('email', ''))
            self.phone_edit.setText(self.contact_data.get('phone', ''))
            self.address_edit.setText(self.contact_data.get('address', ''))
            self.comments_edit.setText(self.contact_data.get('comments', ''))

    def get_contact_data(self):
        """Obtener datos del contacto del formulario"""
        return {
            'name': self.name_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'address': self.address_edit.text().strip(),
            'comments': self.comments_edit.toPlainText().strip()
        }


class ContactsTable(QTableWidget):
    """Tabla de contactos con aspecto de Excel"""

    contact_edited = pyqtSignal(dict, int)  # contacto, fila
    contact_deleted = pyqtSignal(int)  # fila

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()

    def setup_table(self):
        """Configurar la tabla"""
        # Columnas
        self.setColumnCount(6)
        headers = ["Nombre", "Email", "Teléfono", "Dirección", "Comentarios", "Acciones"]
        self.setHorizontalHeaderLabels(headers)

        # Estilo Excel
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #D0D0D0;
                background-color: white;
                alternate-background-color: #F8F9FA;
                selection-background-color: #E3F2FD;
                font-size: 10pt;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #E0E0E0;
            }
            QHeaderView::section {
                background-color: #F0F0F0;
                padding: 8px;
                border: 1px solid #D0D0D0;
                font-weight: bold;
            }
        """)

        # Configuración de la tabla
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.verticalHeader().setVisible(False)

        # Ajustar columnas
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # Nombre
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # Email
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # Teléfono
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # Dirección
        header.setSectionResizeMode(4, QHeaderView.Stretch)     # Comentarios
        header.setSectionResizeMode(5, QHeaderView.Fixed)       # Acciones

        # Ancho de columnas
        self.setColumnWidth(0, 150)  # Nombre
        self.setColumnWidth(1, 200)  # Email
        self.setColumnWidth(2, 120)  # Teléfono
        self.setColumnWidth(3, 200)  # Dirección
        self.setColumnWidth(5, 120)  # Acciones

    def add_contact(self, contact_data):
        """Agregar un contacto a la tabla"""
        row = self.rowCount()
        self.insertRow(row)

        # Datos del contacto
        self.setItem(row, 0, QTableWidgetItem(contact_data.get('name', '')))
        self.setItem(row, 1, QTableWidgetItem(contact_data.get('email', '')))
        self.setItem(row, 2, QTableWidgetItem(contact_data.get('phone', '')))
        self.setItem(row, 3, QTableWidgetItem(contact_data.get('address', '')))
        self.setItem(row, 4, QTableWidgetItem(contact_data.get('comments', '')))

        # Botones de acción
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(5)

        # Botón editar
        edit_button = QPushButton("✏️")
        edit_button.setFixedSize(25, 25)
        edit_button.setToolTip("Editar contacto")
        edit_button.clicked.connect(lambda: self.edit_contact(row))
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        actions_layout.addWidget(edit_button)

        # Botón eliminar
        delete_button = QPushButton("🗑️")
        delete_button.setFixedSize(25, 25)
        delete_button.setToolTip("Eliminar contacto")
        delete_button.clicked.connect(lambda: self.delete_contact(row))
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        actions_layout.addWidget(delete_button)

        self.setCellWidget(row, 5, actions_widget)

    def edit_contact(self, row):
        """Editar un contacto"""
        contact_data = {
            'name': self.item(row, 0).text() if self.item(row, 0) else '',
            'email': self.item(row, 1).text() if self.item(row, 1) else '',
            'phone': self.item(row, 2).text() if self.item(row, 2) else '',
            'address': self.item(row, 3).text() if self.item(row, 3) else '',
            'comments': self.item(row, 4).text() if self.item(row, 4) else ''
        }

        dialog = ContactDialog(contact_data, self)
        if dialog.exec_() == QDialog.Accepted:
            new_data = dialog.get_contact_data()

            # Actualizar la tabla
            self.setItem(row, 0, QTableWidgetItem(new_data.get('name', '')))
            self.setItem(row, 1, QTableWidgetItem(new_data.get('email', '')))
            self.setItem(row, 2, QTableWidgetItem(new_data.get('phone', '')))
            self.setItem(row, 3, QTableWidgetItem(new_data.get('address', '')))
            self.setItem(row, 4, QTableWidgetItem(new_data.get('comments', '')))

            self.contact_edited.emit(new_data, row)

    def delete_contact(self, row):
        """Eliminar un contacto"""
        self.removeRow(row)
        self.contact_deleted.emit(row)

    def clear_contacts(self):
        """Limpiar todos los contactos"""
        self.setRowCount(0)

    def get_contacts_by_letter(self, letter):
        """Obtener contactos que empiecen por una letra"""
        contacts = []
        for row in range(self.rowCount()):
            name_item = self.item(row, 0)
            if name_item and name_item.text().upper().startswith(letter.upper()):
                contact_data = {
                    'name': self.item(row, 0).text() if self.item(row, 0) else '',
                    'email': self.item(row, 1).text() if self.item(row, 1) else '',
                    'phone': self.item(row, 2).text() if self.item(row, 2) else '',
                    'address': self.item(row, 3).text() if self.item(row, 3) else '',
                    'comments': self.item(row, 4).text() if self.item(row, 4) else ''
                }
                contacts.append(contact_data)
        return contacts


class ContactsTab(QWidget):
    """Pestaña de contactos con pestañas alfabéticas"""

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.contacts = []  # Lista de todos los contactos

        self.setup_ui()
        self.load_contacts()

    def setup_ui(self):
        """Configurar la interfaz de la pestaña"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Título y botón nuevo contacto
        header_layout = QHBoxLayout()

        title_label = QLabel("📞 CONTACTOS")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2C3E50; margin: 5px;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        self.new_contact_button = QPushButton("➕ Nuevo Contacto")
        self.new_contact_button.clicked.connect(self.add_new_contact)
        self.new_contact_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        header_layout.addWidget(self.new_contact_button)

        layout.addLayout(header_layout)

        # Pestañas alfabéticas
        self.alphabet_tabs = QTabWidget()
        self.alphabet_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #D0D0D0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #F0F0F0;
                border: 1px solid #D0D0D0;
                padding: 8px 12px;
                margin-right: 2px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498DB;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #E8F4FD;
            }
        """)

        # Crear pestañas para cada letra del alfabeto
        self.letter_tables = {}
        alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"

        for letter in alphabet:
            # Crear tabla para esta letra
            table = ContactsTable()
            table.contact_edited.connect(self.on_contact_edited)
            table.contact_deleted.connect(self.on_contact_deleted)

            # Agregar pestaña
            self.alphabet_tabs.addTab(table, letter)
            self.letter_tables[letter] = table

        layout.addWidget(self.alphabet_tabs)

        # Información de estadísticas
        self.stats_label = QLabel("Total de contactos: 0")
        self.stats_label.setStyleSheet("color: #7F8C8D; font-style: italic; margin: 5px;")
        layout.addWidget(self.stats_label)

    def add_new_contact(self):
        """Agregar un nuevo contacto"""
        dialog = ContactDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            contact_data = dialog.get_contact_data()

            if contact_data['name']:  # Verificar que tenga nombre
                self.add_contact_to_system(contact_data)

    def add_contact_to_system(self, contact_data):
        """Agregar contacto al sistema y organizarlo alfabéticamente"""
        # Agregar a la lista principal
        self.contacts.append(contact_data)

        # Obtener primera letra del nombre
        first_letter = contact_data['name'][0].upper() if contact_data['name'] else 'A'
        if first_letter not in self.letter_tables:
            first_letter = 'A'  # Por defecto si no es una letra

        # Agregar a la tabla correspondiente
        table = self.letter_tables[first_letter]
        table.add_contact(contact_data)

        # Ordenar la tabla alfabéticamente
        self.sort_table_alphabetically(table)

        # Actualizar estadísticas
        self.update_stats()

        # Cambiar a la pestaña correspondiente
        tab_index = list(self.letter_tables.keys()).index(first_letter)
        self.alphabet_tabs.setCurrentIndex(tab_index)

    def sort_table_alphabetically(self, table):
        """Ordenar tabla alfabéticamente por nombre"""
        table.sortItems(0, Qt.AscendingOrder)

    def on_contact_edited(self, contact_data, row):
        """Manejar edición de contacto"""
        # Actualizar en la lista principal
        # Nota: Esto es una simplificación, en una implementación real
        # necesitarías un ID único para cada contacto
        self.update_stats()

    def on_contact_deleted(self, row):
        """Manejar eliminación de contacto"""
        # Actualizar estadísticas
        self.update_stats()

    def update_stats(self):
        """Actualizar estadísticas de contactos"""
        total_contacts = sum(table.rowCount() for table in self.letter_tables.values())
        self.stats_label.setText(f"Total de contactos: {total_contacts}")

    def load_contacts(self):
        """Cargar contactos desde el data manager"""
        # Por ahora, crear algunos contactos de ejemplo
        example_contacts = [
            {
                'name': 'Ana García',
                'email': '<EMAIL>',
                'phone': '+34 600 123 456',
                'address': 'Calle Mayor 123, Madrid',
                'comments': 'Contacto de trabajo'
            },
            {
                'name': 'Carlos López',
                'email': '<EMAIL>',
                'phone': '+34 600 789 012',
                'address': 'Avenida Principal 456, Barcelona',
                'comments': 'Cliente importante'
            },
            {
                'name': 'María Rodríguez',
                'email': '<EMAIL>',
                'phone': '+34 600 345 678',
                'address': 'Plaza Central 789, Valencia',
                'comments': 'Amiga de la familia'
            }
        ]

        for contact in example_contacts:
            self.add_contact_to_system(contact)

    def search_contacts(self, query):
        """Buscar contactos por nombre, email o teléfono"""
        # Esta funcionalidad se puede implementar más adelante
        pass

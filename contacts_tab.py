#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pestaña de contactos con aspecto de Excel y pestañas alfabéticas
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QHeaderView, QTabWidget, QFrame, QLineEdit,
                            QDialog, QFormLayout, QTextEdit, QSplitter,
                            QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont


class ContactDialog(QDialog):
    """Diálogo para agregar/editar contactos"""

    def __init__(self, contact_data=None, parent=None):
        super().__init__(parent)
        self.contact_data = contact_data
        self.setWindowTitle("Nuevo Contacto" if not contact_data else "Editar Contacto")
        self.setModal(True)
        self.resize(1600, 1200)  # Tamaño inicial de 1600x1200px
        self.setMinimumSize(800, 600)  # Tamaño mínimo funcional

        self.setup_ui()

        if contact_data:
            self.load_contact_data()

    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # Título grande
        title_label = QLabel("📞 " + ("NUEVO CONTACTO" if not self.contact_data else "EDITAR CONTACTO"))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 20, QFont.Bold))
        title_label.setStyleSheet("color: #2C3E50; margin: 20px; padding: 15px;")
        layout.addWidget(title_label)

        # Formulario más grande
        form_layout = QFormLayout()
        form_layout.setVerticalSpacing(15)
        form_layout.setHorizontalSpacing(20)

        # Estilo para etiquetas
        label_style = "font-size: 14pt; font-weight: bold; color: #34495E; padding: 5px;"

        # Estilo para campos de entrada
        input_style = """
            QLineEdit, QTextEdit {
                padding: 12px;
                font-size: 14pt;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus, QTextEdit:focus {
                border: 2px solid #3498DB;
                background-color: #F8F9FA;
            }
        """

        # Nombre
        name_label = QLabel("👤 Nombre:")
        name_label.setStyleSheet(label_style)
        self.name_edit = QLineEdit()
        self.name_edit.setStyleSheet(input_style)
        self.name_edit.setPlaceholderText("Ingrese el nombre completo...")
        form_layout.addRow(name_label, self.name_edit)

        # Email
        email_label = QLabel("📧 Email:")
        email_label.setStyleSheet(label_style)
        self.email_edit = QLineEdit()
        self.email_edit.setStyleSheet(input_style)
        self.email_edit.setPlaceholderText("<EMAIL>")
        form_layout.addRow(email_label, self.email_edit)

        # Teléfono
        phone_label = QLabel("📱 Teléfono:")
        phone_label.setStyleSheet(label_style)
        self.phone_edit = QLineEdit()
        self.phone_edit.setStyleSheet(input_style)
        self.phone_edit.setPlaceholderText("+34 600 123 456")
        form_layout.addRow(phone_label, self.phone_edit)

        # Dirección
        address_label = QLabel("🏠 Dirección:")
        address_label.setStyleSheet(label_style)
        self.address_edit = QLineEdit()
        self.address_edit.setStyleSheet(input_style)
        self.address_edit.setPlaceholderText("Calle, número, ciudad...")
        form_layout.addRow(address_label, self.address_edit)

        # Comentarios más grande
        comments_label = QLabel("💬 Comentarios:")
        comments_label.setStyleSheet(label_style)
        self.comments_edit = QTextEdit()
        self.comments_edit.setMinimumHeight(150)
        self.comments_edit.setStyleSheet(input_style)
        self.comments_edit.setPlaceholderText("Notas adicionales sobre el contacto...")
        form_layout.addRow(comments_label, self.comments_edit)

        layout.addLayout(form_layout)

        # Espaciador
        layout.addStretch()

        # Botones más grandes
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(20)

        # Espaciador izquierdo
        buttons_layout.addStretch()

        self.cancel_button = QPushButton("❌ CANCELAR")
        self.cancel_button.setFixedSize(200, 60)
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px 25px;
                font-weight: bold;
                font-size: 14pt;
            }
            QPushButton:hover {
                background-color: #C0392B;
                transform: scale(1.02);
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
        """)
        buttons_layout.addWidget(self.cancel_button)

        self.save_button = QPushButton("💾 GUARDAR")
        self.save_button.setFixedSize(200, 60)
        self.save_button.clicked.connect(self.accept)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px 25px;
                font-weight: bold;
                font-size: 14pt;
            }
            QPushButton:hover {
                background-color: #229954;
                transform: scale(1.02);
            }
            QPushButton:pressed {
                background-color: #1E8449;
            }
        """)
        buttons_layout.addWidget(self.save_button)

        # Espaciador derecho
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # Espaciador inferior
        layout.addStretch()

    def load_contact_data(self):
        """Cargar datos del contacto para edición"""
        if self.contact_data:
            self.name_edit.setText(self.contact_data.get('name', ''))
            self.email_edit.setText(self.contact_data.get('email', ''))
            self.phone_edit.setText(self.contact_data.get('phone', ''))
            self.address_edit.setText(self.contact_data.get('address', ''))
            self.comments_edit.setText(self.contact_data.get('comments', ''))

    def get_contact_data(self):
        """Obtener datos del contacto del formulario"""
        return {
            'name': self.name_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'address': self.address_edit.text().strip(),
            'comments': self.comments_edit.toPlainText().strip()
        }


class ContactsTable(QTableWidget):
    """Tabla de contactos con aspecto de Excel"""

    contact_edited = pyqtSignal(dict, int)  # contacto, fila
    contact_deleted = pyqtSignal(int)  # fila

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()

    def setup_table(self):
        """Configurar la tabla"""
        # Columnas
        self.setColumnCount(6)
        headers = ["Nombre", "Email", "Teléfono", "Dirección", "Comentarios", "Acciones"]
        self.setHorizontalHeaderLabels(headers)

        # Estilo Excel
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #D0D0D0;
                background-color: white;
                alternate-background-color: #F8F9FA;
                selection-background-color: #E3F2FD;
                font-size: 10pt;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #E0E0E0;
            }
            QHeaderView::section {
                background-color: #F0F0F0;
                padding: 8px;
                border: 1px solid #D0D0D0;
                font-weight: bold;
            }
        """)

        # Configuración de la tabla
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.verticalHeader().setVisible(False)

        # Ajustar columnas
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # Nombre
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # Email
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # Teléfono
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # Dirección
        header.setSectionResizeMode(4, QHeaderView.Stretch)     # Comentarios
        header.setSectionResizeMode(5, QHeaderView.Fixed)       # Acciones

        # Ancho de columnas
        self.setColumnWidth(0, 150)  # Nombre
        self.setColumnWidth(1, 200)  # Email
        self.setColumnWidth(2, 120)  # Teléfono
        self.setColumnWidth(3, 200)  # Dirección
        self.setColumnWidth(5, 120)  # Acciones

    def add_contact(self, contact_data):
        """Agregar un contacto a la tabla"""
        row = self.rowCount()
        self.insertRow(row)

        # Datos del contacto
        self.setItem(row, 0, QTableWidgetItem(contact_data.get('name', '')))
        self.setItem(row, 1, QTableWidgetItem(contact_data.get('email', '')))
        self.setItem(row, 2, QTableWidgetItem(contact_data.get('phone', '')))
        self.setItem(row, 3, QTableWidgetItem(contact_data.get('address', '')))
        self.setItem(row, 4, QTableWidgetItem(contact_data.get('comments', '')))

        # Botones de acción
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(5)

        # Botón editar
        edit_button = QPushButton("✏️")
        edit_button.setFixedSize(25, 25)
        edit_button.setToolTip("Editar contacto")
        edit_button.clicked.connect(lambda: self.edit_contact(row))
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        actions_layout.addWidget(edit_button)

        # Botón eliminar
        delete_button = QPushButton("🗑️")
        delete_button.setFixedSize(25, 25)
        delete_button.setToolTip("Eliminar contacto")
        delete_button.clicked.connect(lambda: self.delete_contact(row))
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        actions_layout.addWidget(delete_button)

        self.setCellWidget(row, 5, actions_widget)

    def edit_contact(self, row):
        """Editar un contacto"""
        contact_data = {
            'name': self.item(row, 0).text() if self.item(row, 0) else '',
            'email': self.item(row, 1).text() if self.item(row, 1) else '',
            'phone': self.item(row, 2).text() if self.item(row, 2) else '',
            'address': self.item(row, 3).text() if self.item(row, 3) else '',
            'comments': self.item(row, 4).text() if self.item(row, 4) else ''
        }

        dialog = ContactDialog(contact_data, self)
        if dialog.exec_() == QDialog.Accepted:
            new_data = dialog.get_contact_data()

            # Actualizar la tabla
            self.setItem(row, 0, QTableWidgetItem(new_data.get('name', '')))
            self.setItem(row, 1, QTableWidgetItem(new_data.get('email', '')))
            self.setItem(row, 2, QTableWidgetItem(new_data.get('phone', '')))
            self.setItem(row, 3, QTableWidgetItem(new_data.get('address', '')))
            self.setItem(row, 4, QTableWidgetItem(new_data.get('comments', '')))

            self.contact_edited.emit(new_data, row)

    def delete_contact(self, row):
        """Eliminar un contacto con confirmación"""
        # Obtener datos del contacto para mostrar en la confirmación
        name_item = self.item(row, 0)
        contact_name = name_item.text() if name_item else "Contacto sin nombre"

        # Crear diálogo de confirmación
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("⚠️ Confirmar Eliminación")
        msg_box.setIcon(QMessageBox.Warning)

        # Mensaje de advertencia
        msg_box.setText(f"¿Está seguro de que desea eliminar el contacto?")
        msg_box.setInformativeText(f"📞 Contacto: {contact_name}\n\n"
                                  f"⚠️ ADVERTENCIA: Se perderán todos los datos de este contacto.\n"
                                  f"Esta acción no se puede deshacer.")

        # Botones personalizados
        delete_button = msg_box.addButton("🗑️ ELIMINAR", QMessageBox.DestructiveRole)
        cancel_button = msg_box.addButton("❌ CANCELAR", QMessageBox.RejectRole)

        # Estilos para los botones
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)

        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
        """)

        # Configurar el diálogo
        msg_box.setDefaultButton(cancel_button)  # Cancelar por defecto para seguridad
        msg_box.resize(500, 300)

        # Mostrar diálogo y procesar respuesta
        msg_box.exec_()

        if msg_box.clickedButton() == delete_button:
            # Usuario confirmó la eliminación
            self.removeRow(row)
            self.contact_deleted.emit(row)

    def clear_contacts(self):
        """Limpiar todos los contactos"""
        self.setRowCount(0)

    def get_contacts_by_letter(self, letter):
        """Obtener contactos que empiecen por una letra"""
        contacts = []
        for row in range(self.rowCount()):
            name_item = self.item(row, 0)
            if name_item and name_item.text().upper().startswith(letter.upper()):
                contact_data = {
                    'name': self.item(row, 0).text() if self.item(row, 0) else '',
                    'email': self.item(row, 1).text() if self.item(row, 1) else '',
                    'phone': self.item(row, 2).text() if self.item(row, 2) else '',
                    'address': self.item(row, 3).text() if self.item(row, 3) else '',
                    'comments': self.item(row, 4).text() if self.item(row, 4) else ''
                }
                contacts.append(contact_data)
        return contacts


class ContactsTab(QWidget):
    """Pestaña de contactos con pestañas alfabéticas"""

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.contacts = []  # Lista de todos los contactos

        self.setup_ui()
        self.load_contacts()

    def setup_ui(self):
        """Configurar la interfaz de la pestaña"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Título y botón nuevo contacto
        header_layout = QHBoxLayout()

        title_label = QLabel("📞 CONTACTOS")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2C3E50; margin: 5px;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        self.new_contact_button = QPushButton("➕ Nuevo Contacto")
        self.new_contact_button.clicked.connect(self.add_new_contact)
        self.new_contact_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        header_layout.addWidget(self.new_contact_button)

        layout.addLayout(header_layout)

        # Pestañas alfabéticas
        self.alphabet_tabs = QTabWidget()
        self.alphabet_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #D0D0D0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #F0F0F0;
                border: 1px solid #D0D0D0;
                padding: 8px 12px;
                margin-right: 2px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498DB;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #E8F4FD;
            }
        """)

        # Crear pestañas para cada letra del alfabeto
        self.letter_tables = {}
        alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"

        for letter in alphabet:
            # Crear tabla para esta letra
            table = ContactsTable()
            table.contact_edited.connect(self.on_contact_edited)
            table.contact_deleted.connect(self.on_contact_deleted)

            # Agregar pestaña
            self.alphabet_tabs.addTab(table, letter)
            self.letter_tables[letter] = table

        layout.addWidget(self.alphabet_tabs)

        # Información de estadísticas
        self.stats_label = QLabel("Total de contactos: 0")
        self.stats_label.setStyleSheet("color: #7F8C8D; font-style: italic; margin: 5px;")
        layout.addWidget(self.stats_label)

    def add_new_contact(self):
        """Agregar un nuevo contacto"""
        dialog = ContactDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            contact_data = dialog.get_contact_data()

            if contact_data['name']:  # Verificar que tenga nombre
                self.add_contact_to_system(contact_data)

    def add_contact_to_system(self, contact_data):
        """Agregar contacto al sistema y organizarlo alfabéticamente"""
        # Agregar a la lista principal
        self.contacts.append(contact_data)

        # Obtener primera letra del nombre
        first_letter = contact_data['name'][0].upper() if contact_data['name'] else 'A'
        if first_letter not in self.letter_tables:
            first_letter = 'A'  # Por defecto si no es una letra

        # Agregar a la tabla correspondiente
        table = self.letter_tables[first_letter]
        table.add_contact(contact_data)

        # Ordenar la tabla alfabéticamente
        self.sort_table_alphabetically(table)

        # Actualizar estadísticas
        self.update_stats()

        # Cambiar a la pestaña correspondiente
        tab_index = list(self.letter_tables.keys()).index(first_letter)
        self.alphabet_tabs.setCurrentIndex(tab_index)

    def sort_table_alphabetically(self, table):
        """Ordenar tabla alfabéticamente por nombre"""
        table.sortItems(0, Qt.AscendingOrder)

    def on_contact_edited(self, contact_data, row):
        """Manejar edición de contacto"""
        # Actualizar en la lista principal
        # Nota: Esto es una simplificación, en una implementación real
        # necesitarías un ID único para cada contacto
        self.update_stats()

    def on_contact_deleted(self, row):
        """Manejar eliminación de contacto"""
        # Actualizar estadísticas
        self.update_stats()

    def update_stats(self):
        """Actualizar estadísticas de contactos"""
        total_contacts = sum(table.rowCount() for table in self.letter_tables.values())
        self.stats_label.setText(f"Total de contactos: {total_contacts}")

    def load_contacts(self):
        """Cargar contactos desde el data manager"""
        # Por ahora, crear algunos contactos de ejemplo
        example_contacts = [
            {
                'name': 'Ana García',
                'email': '<EMAIL>',
                'phone': '+34 600 123 456',
                'address': 'Calle Mayor 123, Madrid',
                'comments': 'Contacto de trabajo'
            },
            {
                'name': 'Carlos López',
                'email': '<EMAIL>',
                'phone': '+34 600 789 012',
                'address': 'Avenida Principal 456, Barcelona',
                'comments': 'Cliente importante'
            },
            {
                'name': 'María Rodríguez',
                'email': '<EMAIL>',
                'phone': '+34 600 345 678',
                'address': 'Plaza Central 789, Valencia',
                'comments': 'Amiga de la familia'
            }
        ]

        for contact in example_contacts:
            self.add_contact_to_system(contact)

    def search_contacts(self, query):
        """Buscar contactos por nombre, email o teléfono"""
        # Esta funcionalidad se puede implementar más adelante
        pass

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para el banner centrado con sistema de contraseña maestra
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QFrame, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from simple_banner_widget import SimpleBannerWidget
from world_clocks_widget import WorldClocksWidget
from data_manager import DataManager
from styles import apply_styles


class TestBannerCenteredPasswordWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔐 Banner Centrado con Contraseña Maestra")
        self.setGeometry(100, 100, 1000, 700)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("🔐 Banner Centrado con Sistema de Contraseña Maestra")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 15px; color: #2C3E50;")
        layout.addWidget(title)
        
        # Descripción de funcionalidades
        features = QLabel(
            "✅ FUNCIONALIDADES IMPLEMENTADAS:\n\n"
            "📐 BANNER CENTRADO: Tamaño máximo fijo 600x120px\n"
            "🔐 CONTRASEÑA MAESTRA: Protección de configuración\n"
            "🔑 PRIMERA VEZ: Configuración inicial de contraseña\n"
            "🔄 CAMBIO DE CONTRASEÑA: Pestaña para cambiar contraseña\n"
            "🛡️ SEGURIDAD: Validación y confirmación de cambios\n"
            "💾 PERSISTENCIA: Configuración protegida y guardada\n"
            "⚙️ ACCESO CONTROLADO: Solo administradores pueden configurar\n"
            "🎨 DISEÑO CENTRADO: Banner ocupa espacio fijo centrado"
        )
        features.setAlignment(Qt.AlignCenter)
        features.setStyleSheet(
            "margin: 15px; color: #2980B9; font-size: 11pt; "
            "background-color: #EBF5FB; padding: 20px; border-radius: 8px;"
        )
        features.setWordWrap(True)
        layout.addWidget(features)
        
        # Simulación del layout
        simulation_frame = QFrame()
        simulation_frame.setStyleSheet(
            "border: 2px solid #3498DB; border-radius: 8px; "
            "background-color: #F8F9FA; margin: 10px;"
        )
        sim_layout = QVBoxLayout(simulation_frame)
        
        sim_title = QLabel("📱 Simulación del Layout con Banner Centrado")
        sim_title.setFont(QFont("Arial", 14, QFont.Bold))
        sim_title.setAlignment(Qt.AlignCenter)
        sim_title.setStyleSheet("color: #2C3E50; margin: 15px;")
        sim_layout.addWidget(sim_title)
        
        # Área de eventos simulada
        events_area = QWidget()
        events_area.setMinimumHeight(120)
        events_area.setStyleSheet(
            "background-color: #ECF0F1; border: 1px solid #BDC3C7; "
            "border-radius: 5px; margin: 5px;"
        )
        events_layout = QVBoxLayout(events_area)
        
        events_label = QLabel("📅 Área de Eventos del Calendario")
        events_label.setAlignment(Qt.AlignCenter)
        events_label.setStyleSheet("color: #7F8C8D; font-size: 12pt; font-style: italic;")
        events_layout.addWidget(events_label)
        
        sim_layout.addWidget(events_area)
        
        # Widget de relojes mundiales con banner centrado
        self.world_clocks = WorldClocksWidget(self.data_manager)
        sim_layout.addWidget(self.world_clocks)
        
        layout.addWidget(simulation_frame)
        
        # Panel de información y controles
        info_frame = QFrame()
        info_frame.setStyleSheet(
            "border: 1px solid #BDC3C7; border-radius: 5px; "
            "background-color: #FAFAFA; margin: 5px;"
        )
        info_layout = QHBoxLayout(info_frame)
        
        # Información del sistema de contraseña
        password_info = QWidget()
        password_layout = QVBoxLayout(password_info)
        
        password_title = QLabel("🔐 Sistema de Contraseña Maestra:")
        password_title.setFont(QFont("Arial", 12, QFont.Bold))
        password_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        password_layout.addWidget(password_title)
        
        password_details = QLabel(
            "🔑 PRIMERA VEZ:\n"
            "• Configuración inicial de contraseña\n"
            "• Mínimo 6 caracteres\n"
            "• Confirmación requerida\n\n"
            "🔄 ACCESO POSTERIOR:\n"
            "• Pestaña 'Acceso': Ingresar contraseña\n"
            "• Pestaña 'Cambiar': Modificar contraseña\n"
            "• Validación de contraseña actual\n"
            "• Confirmación de nueva contraseña\n\n"
            "🛡️ SEGURIDAD:\n"
            "• Protección de configuración\n"
            "• Re-encriptación automática\n"
            "• Acceso controlado"
        )
        password_details.setStyleSheet(
            "color: #34495E; font-size: 9pt; background-color: #F8F9FA; "
            "padding: 10px; border-radius: 5px; border: 1px solid #E0E0E0;"
        )
        password_details.setWordWrap(True)
        password_layout.addWidget(password_details)
        
        info_layout.addWidget(password_info)
        
        # Información del banner centrado
        banner_info = QWidget()
        banner_layout = QVBoxLayout(banner_info)
        
        banner_title = QLabel("📐 Banner Centrado:")
        banner_title.setFont(QFont("Arial", 12, QFont.Bold))
        banner_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        banner_layout.addWidget(banner_title)
        
        banner_details = QLabel(
            "📏 DIMENSIONES:\n"
            "• Tamaño fijo: 600x120 píxeles\n"
            "• Centrado horizontalmente\n"
            "• Espaciadores automáticos\n\n"
            "🎨 DISEÑO:\n"
            "• Borde redondeado\n"
            "• Efecto hover\n"
            "• Botón ⚙ superpuesto\n"
            "• Imagen escalada proporcionalmente\n\n"
            "📍 UBICACIÓN:\n"
            "• Debajo de relojes mundiales\n"
            "• Dentro del panel derecho\n"
            "• Espacio fijo reservado\n\n"
            "🔧 CONFIGURACIÓN:\n"
            "• Protegida por contraseña\n"
            "• Imagen + URL + texto\n"
            "• Persistencia automática"
        )
        banner_details.setStyleSheet(
            "color: #34495E; font-size: 9pt; background-color: #F8F9FA; "
            "padding: 10px; border-radius: 5px; border: 1px solid #E0E0E0;"
        )
        banner_details.setWordWrap(True)
        banner_layout.addWidget(banner_details)
        
        info_layout.addWidget(banner_info)
        
        layout.addWidget(info_frame)
        
        # Botones de prueba
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("margin: 5px;")
        buttons_layout = QHBoxLayout(buttons_frame)
        
        # Botón para configurar banner
        btn_configure = QPushButton("🔐 Configurar Banner (Con Contraseña)")
        btn_configure.setMinimumHeight(40)
        btn_configure.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        btn_configure.clicked.connect(self.configure_banner)
        buttons_layout.addWidget(btn_configure)
        
        # Botón para limpiar contraseña
        btn_clear_password = QPushButton("🗑️ Limpiar Contraseña Maestra")
        btn_clear_password.setMinimumHeight(40)
        btn_clear_password.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #E67E22;
            }
        """)
        btn_clear_password.clicked.connect(self.clear_master_password)
        buttons_layout.addWidget(btn_clear_password)
        
        layout.addWidget(buttons_frame)
        
        # Log de eventos
        log_title = QLabel("📝 Log de Eventos:")
        log_title.setFont(QFont("Arial", 11, QFont.Bold))
        log_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        layout.addWidget(log_title)
        
        self.log_area = QTextEdit()
        self.log_area.setMaximumHeight(100)
        self.log_area.setPlaceholderText("Los eventos del sistema aparecerán aquí...")
        self.log_area.setStyleSheet(
            "background-color: #FAFAFA; border: 1px solid #BDC3C7; "
            "border-radius: 3px; font-family: 'Courier New'; font-size: 9pt;"
        )
        layout.addWidget(self.log_area)
        
        # Estado inicial
        self.check_password_status()
    
    def configure_banner(self):
        """Configurar el banner"""
        try:
            # Acceder al banner del widget de relojes
            if hasattr(self.world_clocks, 'banner'):
                self.world_clocks.banner.configure_banner()
                self.log_event("🔐 Configuración del banner iniciada")
            else:
                self.log_event("❌ Error: Banner no encontrado")
        except Exception as e:
            self.log_event(f"❌ Error configurando banner: {str(e)}")
    
    def clear_master_password(self):
        """Limpiar contraseña maestra para pruebas"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self,
                "Confirmar Limpieza",
                "¿Está seguro de eliminar la contraseña maestra?\n\n"
                "Esto permitirá configurar una nueva contraseña.",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.data_manager.set_config('banner_master_password', None)
                self.log_event("🗑️ Contraseña maestra eliminada")
                self.check_password_status()
                
        except Exception as e:
            self.log_event(f"❌ Error limpiando contraseña: {str(e)}")
    
    def check_password_status(self):
        """Verificar estado de la contraseña maestra"""
        try:
            master_password = self.data_manager.get_config('banner_master_password', None)
            
            if master_password is None:
                self.log_event("🔑 Estado: Sin contraseña maestra configurada")
                self.log_event("💡 Próximo clic en configurar → Configuración inicial")
            else:
                self.log_event("🔐 Estado: Contraseña maestra configurada")
                self.log_event("💡 Próximo clic en configurar → Acceso con pestañas")
                
        except Exception as e:
            self.log_event(f"❌ Error verificando estado: {str(e)}")
    
    def log_event(self, message):
        """Agregar evento al log"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {message}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestBannerCenteredPasswordWindow()
    window.show()
    
    sys.exit(app.exec_())

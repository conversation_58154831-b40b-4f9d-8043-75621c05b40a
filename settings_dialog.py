#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
                            QFormLayout, QLabel, QComboBox, QCheckBox, QPushButton,
                            QLineEdit, QGroupBox, QDialogButtonBox, QFileDialog,
                            QMessageBox, QSpinBox)
from PyQt5.QtCore import Qt
import pytz


class SettingsDialog(QDialog):
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        
        self.setWindowTitle("Configuración")
        self.resize(500, 400)
        
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """Inicializar la interfaz de usuario"""
        layout = QVBoxLayout(self)
        
        # Crear pestañas
        self.tabs = QTabWidget()
        
        # Pestaña General
        general_tab = QWidget()
        general_layout = QVBoxLayout(general_tab)
        
        # Grupo de opciones generales
        general_group = QGroupBox("Opciones generales")
        general_form = QFormLayout(general_group)
        
        # Tema
        self.theme_combo = QComboBox()
        self.theme_combo.addItem("Claro", "light")
        self.theme_combo.addItem("Oscuro", "dark")
        self.theme_combo.addItem("Sistema", "system")
        general_form.addRow("Tema:", self.theme_combo)
        
        # Inicio automático
        self.startup_check = QCheckBox("Iniciar con el sistema")
        general_form.addRow("", self.startup_check)
        
        # Minimizar a bandeja
        self.minimize_check = QCheckBox("Minimizar a bandeja en lugar de cerrar")
        general_form.addRow("", self.minimize_check)
        
        general_layout.addWidget(general_group)
        
        # Grupo de opciones de sincronización
        sync_group = QGroupBox("Sincronización")
        sync_form = QFormLayout(sync_group)
        
        # Zona horaria
        self.timezone_combo = QComboBox()
        self.timezone_combo.addItem("Hora local", "local")
        
        # Añadir zonas horarias
        for tz in pytz.common_timezones:
            self.timezone_combo.addItem(tz, tz)
        
        sync_form.addRow("Zona horaria:", self.timezone_combo)
        
        general_layout.addWidget(sync_group)
        
        # Añadir pestaña general
        self.tabs.addTab(general_tab, "General")
        
        # Pestaña Recordatorios
        reminders_tab = QWidget()
        reminders_layout = QVBoxLayout(reminders_tab)
        
        # Grupo de opciones de recordatorios
        reminders_group = QGroupBox("Opciones de recordatorios")
        reminders_form = QFormLayout(reminders_group)
        
        # Sonido de recordatorio
        self.reminder_sound_combo = QComboBox()
        self.reminder_sound_combo.addItem("Por defecto", "default")
        self.reminder_sound_combo.addItem("Sonido 1", "sound1")
        self.reminder_sound_combo.addItem("Sonido 2", "sound2")
        self.reminder_sound_combo.addItem("Sonido 3", "sound3")
        self.reminder_sound_combo.addItem("Sin sonido", "none")
        reminders_form.addRow("Sonido de recordatorio:", self.reminder_sound_combo)
        
        # Tiempo de anticipación por defecto
        self.advance_time_spin = QSpinBox()
        self.advance_time_spin.setMinimum(1)
        self.advance_time_spin.setMaximum(60)
        self.advance_time_spin.setSuffix(" minutos")
        reminders_form.addRow("Tiempo de anticipación por defecto:", self.advance_time_spin)
        
        reminders_layout.addWidget(reminders_group)
        
        # Añadir pestaña recordatorios
        self.tabs.addTab(reminders_tab, "Recordatorios")
        
        # Pestaña Respaldo
        backup_tab = QWidget()
        backup_layout = QVBoxLayout(backup_tab)
        
        # Grupo de respaldo
        backup_group = QGroupBox("Respaldo y restauración")
        backup_form = QVBoxLayout(backup_group)
        
        # Botones de respaldo
        backup_buttons = QHBoxLayout()
        
        self.btn_export = QPushButton("Exportar datos")
        self.btn_export.clicked.connect(self.export_data)
        backup_buttons.addWidget(self.btn_export)
        
        self.btn_import = QPushButton("Importar datos")
        self.btn_import.clicked.connect(self.import_data)
        backup_buttons.addWidget(self.btn_import)
        
        backup_form.addLayout(backup_buttons)
        
        # Advertencia
        warning_label = QLabel("Nota: La importación reemplazará todos los datos actuales.")
        warning_label.setStyleSheet("color: red;")
        backup_form.addWidget(warning_label)
        
        backup_layout.addWidget(backup_group)
        
        # Añadir pestaña respaldo
        self.tabs.addTab(backup_tab, "Respaldo")
        
        layout.addWidget(self.tabs)
        
        # Botones
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.save_settings)
        buttons.rejected.connect(self.reject)
        
        layout.addWidget(buttons)
    
    def load_settings(self):
        """Cargar configuraciones actuales"""
        # Tema
        theme = self.data_manager.get_config("theme", "light")
        index = self.theme_combo.findData(theme)
        if index >= 0:
            self.theme_combo.setCurrentIndex(index)
        
        # Inicio automático
        self.startup_check.setChecked(self.data_manager.get_config("start_with_system", False))
        
        # Minimizar a bandeja
        self.minimize_check.setChecked(self.data_manager.get_config("minimize_to_tray", True))
        
        # Zona horaria
        timezone = self.data_manager.get_config("timezone", "local")
        index = self.timezone_combo.findData(timezone)
        if index >= 0:
            self.timezone_combo.setCurrentIndex(index)
        
        # Sonido de recordatorio
        reminder_sound = self.data_manager.get_config("reminder_sound", "default")
        index = self.reminder_sound_combo.findData(reminder_sound)
        if index >= 0:
            self.reminder_sound_combo.setCurrentIndex(index)
        
        # Tiempo de anticipación
        self.advance_time_spin.setValue(self.data_manager.get_config("reminder_advance_time", 15))
    
    def save_settings(self):
        """Guardar configuraciones"""
        # Tema
        self.data_manager.set_config("theme", self.theme_combo.currentData())
        
        # Inicio automático
        self.data_manager.set_config("start_with_system", self.startup_check.isChecked())
        
        # Minimizar a bandeja
        self.data_manager.set_config("minimize_to_tray", self.minimize_check.isChecked())
        
        # Zona horaria
        self.data_manager.set_config("timezone", self.timezone_combo.currentData())
        
        # Sonido de recordatorio
        self.data_manager.set_config("reminder_sound", self.reminder_sound_combo.currentData())
        
        # Tiempo de anticipación
        self.data_manager.set_config("reminder_advance_time", self.advance_time_spin.value())
        
        self.accept()
    
    def export_data(self):
        """Exportar datos"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Exportar datos", "", "Archivo ZIP (*.zip)"
        )
        if file_path:
            success = self.data_manager.export_data(file_path)
            if success:
                QMessageBox.information(self, "Exportación completada", 
                    "Los datos se han exportado correctamente.")
            else:
                QMessageBox.warning(self, "Error en exportación", 
                    "No se pudieron exportar los datos.")
    
    def import_data(self):
        """Importar datos"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Importar datos", "", "Archivo ZIP (*.zip)"
        )
        if file_path:
            confirm = QMessageBox.question(
                self, "Confirmar importación", 
                "La importación reemplazará todos los datos actuales. ¿Desea continuar?",
                QMessageBox.Yes | QMessageBox.No
            )
            if confirm == QMessageBox.Yes:
                success = self.data_manager.import_data(file_path)
                if success:
                    QMessageBox.information(self, "Importación completada", 
                        "Los datos se han importado correctamente. La aplicación se reiniciará.")
                    self.accept()  # Cerrar el diálogo para que la aplicación pueda reiniciarse
                else:
                    QMessageBox.warning(self, "Error en importación", 
                        "No se pudieron importar los datos.")

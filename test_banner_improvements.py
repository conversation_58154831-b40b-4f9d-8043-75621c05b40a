#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar las mejoras del banner:
- Banner ocupa toda la anchura inferior
- Botón de configuración superpuesto
- Almacenamiento protegido de imágenes
- Altura configurable del banner
- Sistema de contraseña maestra con pestañas
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QTextEdit, QSplitter)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from banner_widget import BannerWidget
from protected_storage import ProtectedStorage
from data_manager import DataManager
from styles import apply_styles


class TestBannerImprovementsWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Mejoras del Banner")
        self.setGeometry(100, 100, 1200, 800)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Mejoras del Banner Configurable")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 10px; color: #2C3E50;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "🎯 MEJORAS IMPLEMENTADAS:\n\n"
            "✅ ANCHURA COMPLETA: Banner ocupa toda la anchura inferior\n"
            "✅ BOTÓN SUPERPUESTO: Configuración flotante en esquina superior derecha\n"
            "✅ ALMACENAMIENTO PROTEGIDO: Imágenes copiadas a carpeta protegida\n"
            "✅ ALTURA CONFIGURABLE: Ajustable en píxeles (40-200px)\n"
            "✅ CONTRASEÑA MAESTRA: Sistema con pestañas para acceso/cambio\n"
            "✅ ENCRIPTACIÓN: Archivos protegidos automáticamente\n\n"
            "🔧 PRUEBA: Configura el banner y observa todas las mejoras"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #2980B9; font-size: 11pt; background-color: #EBF5FB; padding: 15px; border-radius: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Simulación del layout del calendario con banner
        simulation_frame = QWidget()
        simulation_frame.setStyleSheet("border: 2px solid #3498DB; border-radius: 5px; background-color: #F8F9FA;")
        simulation_layout = QVBoxLayout(simulation_frame)
        
        # Título de simulación
        sim_title = QLabel("📱 Simulación del Layout del Calendario")
        sim_title.setFont(QFont("Arial", 12, QFont.Bold))
        sim_title.setAlignment(Qt.AlignCenter)
        sim_title.setStyleSheet("color: #2C3E50; margin: 10px;")
        simulation_layout.addWidget(sim_title)
        
        # Área principal (simulando calendario + eventos + relojes)
        main_area = QWidget()
        main_area.setMinimumHeight(300)
        main_area.setStyleSheet("background-color: #ECF0F1; border: 1px solid #BDC3C7; border-radius: 3px;")
        main_layout = QVBoxLayout(main_area)
        
        main_label = QLabel("Área Principal del Calendario\n(Calendario + Eventos + Relojes)")
        main_label.setAlignment(Qt.AlignCenter)
        main_label.setStyleSheet("color: #7F8C8D; font-size: 14pt; font-style: italic;")
        main_layout.addWidget(main_label)
        
        simulation_layout.addWidget(main_area)
        
        # Banner en la parte inferior (ocupa toda la anchura)
        self.banner = BannerWidget(self.data_manager)
        simulation_layout.addWidget(self.banner)
        
        layout.addWidget(simulation_frame)
        
        # Panel de controles
        controls_frame = QWidget()
        controls_layout = QHBoxLayout(controls_frame)
        
        # Información del almacenamiento protegido
        storage_info = QWidget()
        storage_layout = QVBoxLayout(storage_info)
        
        storage_title = QLabel("🔒 Almacenamiento Protegido:")
        storage_title.setFont(QFont("Arial", 11, QFont.Bold))
        storage_title.setStyleSheet("color: #2C3E50;")
        storage_layout.addWidget(storage_title)
        
        self.storage_info_label = QLabel()
        self.storage_info_label.setStyleSheet("color: #34495E; font-size: 9pt; background-color: #F8F9FA; padding: 8px; border-radius: 3px;")
        self.storage_info_label.setWordWrap(True)
        storage_layout.addWidget(self.storage_info_label)
        
        # Botones de prueba
        btn_update_info = QPushButton("🔄 Actualizar Info")
        btn_update_info.setMinimumHeight(30)
        btn_update_info.setStyleSheet("background-color: #3498DB; color: white;")
        btn_update_info.clicked.connect(self.update_storage_info)
        storage_layout.addWidget(btn_update_info)
        
        btn_create_sample = QPushButton("🖼️ Crear Imagen de Muestra")
        btn_create_sample.setMinimumHeight(30)
        btn_create_sample.setStyleSheet("background-color: #9B59B6; color: white;")
        btn_create_sample.clicked.connect(self.create_sample_image)
        storage_layout.addWidget(btn_create_sample)
        
        controls_layout.addWidget(storage_info)
        
        # Información técnica
        tech_info = QWidget()
        tech_layout = QVBoxLayout(tech_info)
        
        tech_title = QLabel("📐 Especificaciones Técnicas:")
        tech_title.setFont(QFont("Arial", 11, QFont.Bold))
        tech_title.setStyleSheet("color: #2C3E50;")
        tech_layout.addWidget(tech_title)
        
        tech_details = QLabel(
            "🎨 DISEÑO:\n"
            "• Banner: Anchura completa inferior\n"
            "• Botón: Superpuesto, esquina superior derecha\n"
            "• Altura: Configurable 40-200px\n"
            "• Layout: Sin restricciones automáticas\n\n"
            "🔐 SEGURIDAD:\n"
            "• Contraseña maestra con pestañas\n"
            "• Almacenamiento protegido automático\n"
            "• Encriptación de imágenes\n"
            "• Carpeta: protected_storage/images/\n\n"
            "⚡ FUNCIONALIDAD:\n"
            "• Copia automática de imágenes\n"
            "• Escalado proporcional\n"
            "• Desencriptación temporal\n"
            "• Limpieza automática de archivos temp\n\n"
            "🔧 CONFIGURACIÓN:\n"
            "• Pestaña 1: Acceso con contraseña\n"
            "• Pestaña 2: Cambio de contraseña\n"
            "• Re-encriptación automática\n"
            "• Validación de archivos"
        )
        tech_details.setStyleSheet("color: #34495E; font-size: 8pt; background-color: #F8F9FA; padding: 8px; border-radius: 3px;")
        tech_details.setWordWrap(True)
        tech_layout.addWidget(tech_details)
        
        controls_layout.addWidget(tech_info)
        layout.addWidget(controls_frame)
        
        # Área de resultados
        self.results_area = QTextEdit()
        self.results_area.setMaximumHeight(100)
        self.results_area.setPlaceholderText("Los resultados de las pruebas aparecerán aquí...")
        self.results_area.setStyleSheet("background-color: #FAFAFA; border: 1px solid #BDC3C7; border-radius: 3px;")
        layout.addWidget(self.results_area)
        
        # Actualizar información inicial
        self.update_storage_info()
        self.show_initial_instructions()
    
    def update_storage_info(self):
        """Actualizar información del almacenamiento protegido"""
        try:
            storage = ProtectedStorage(self.data_manager)
            info = storage.get_storage_info()
            
            info_text = f"""📁 DIRECTORIO: {info['storage_dir']}
🔐 AUTORIZADO: {'✅ Sí' if info['authorized'] else '❌ No'}
🔒 ENCRIPTACIÓN: {'✅ Disponible' if info['encryption_available'] else '❌ No disponible'}
📊 IMÁGENES: {info['total_images']} total
🔐 ENCRIPTADAS: {info['encrypted_images']} archivos
💾 TAMAÑO TOTAL: {info['total_size']} bytes"""
            
            self.storage_info_label.setText(info_text)
            
            if info['authorized']:
                self.results_area.append("✅ Almacenamiento protegido accesible")
            else:
                self.results_area.append("⚠️ Configure contraseña maestra para acceder al almacenamiento")
                
        except Exception as e:
            self.storage_info_label.setText(f"❌ Error: {str(e)}")
            self.results_area.append(f"❌ Error actualizando info: {str(e)}")
    
    def create_sample_image(self):
        """Crear imagen de muestra para probar el banner"""
        try:
            from PyQt5.QtGui import QPixmap, QPainter, QColor, QFont as QGuiFont
            from PyQt5.QtCore import QRect
            
            # Crear imagen de muestra
            width, height = 400, 80
            pixmap = QPixmap(width, height)
            
            # Crear gradiente de fondo
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            
            # Fondo con gradiente
            from PyQt5.QtGui import QLinearGradient, QBrush
            gradient = QLinearGradient(0, 0, width, 0)
            gradient.setColorAt(0, QColor("#3498DB"))
            gradient.setColorAt(0.5, QColor("#9B59B6"))
            gradient.setColorAt(1, QColor("#E74C3C"))
            
            painter.fillRect(0, 0, width, height, QBrush(gradient))
            
            # Texto principal
            painter.setPen(QColor("white"))
            painter.setFont(QGuiFont("Arial", 16, QGuiFont.Bold))
            painter.drawText(QRect(0, 0, width, height//2), Qt.AlignCenter, "🎯 BANNER DE PRUEBA")
            
            # Texto secundario
            painter.setFont(QGuiFont("Arial", 10))
            painter.drawText(QRect(0, height//2, width, height//2), Qt.AlignCenter, "Haz clic para probar el enlace")
            
            painter.end()
            
            # Guardar imagen
            sample_path = "sample_banner_gradient.png"
            success = pixmap.save(sample_path, "PNG")
            
            if success:
                self.results_area.append(f"🖼️ Imagen de muestra creada: {sample_path}")
                self.results_area.append("💡 Usa esta imagen para configurar el banner")
                self.results_area.append("🔧 Haz clic en ⚙ del banner para configurar")
            else:
                self.results_area.append("❌ Error guardando imagen de muestra")
                
        except Exception as e:
            self.results_area.append(f"❌ Error creando imagen: {str(e)}")
    
    def show_initial_instructions(self):
        """Mostrar instrucciones iniciales"""
        self.results_area.append("🎯 INSTRUCCIONES DE PRUEBA:")
        self.results_area.append("1. Observa que el banner ocupa toda la anchura inferior")
        self.results_area.append("2. El botón ⚙ está superpuesto en la esquina superior derecha")
        self.results_area.append("3. Haz clic en 'Crear Imagen de Muestra' para generar una imagen")
        self.results_area.append("4. Haz clic en ⚙ para configurar el banner")
        self.results_area.append("5. Configura contraseña maestra (primera vez)")
        self.results_area.append("6. Selecciona la imagen creada y configura altura")
        self.results_area.append("7. Observa cómo la imagen se copia al almacenamiento protegido")
        self.results_area.append("8. Prueba cambiar la altura del banner")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestBannerImprovementsWindow()
    window.show()
    
    sys.exit(app.exec_())

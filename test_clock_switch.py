#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar el cambio entre reloj digital y analógico
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QHBoxLayout, QPushButton, QLabel)
from PyQt5.QtCore import Qt

from clock_widget import ClockWidget
from data_manager import DataManager
from styles import apply_styles


class TestClockSwitchWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Cambio Digital/Analógico")
        self.setGeometry(100, 100, 600, 400)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Cambio de Tipo de Reloj")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Contenedor de relojes
        clocks_layout = QHBoxLayout()
        
        # Reloj 1: Madrid
        self.clock1 = ClockWidget(
            timezone="Europe/Madrid",
            city_name="Madrid",
            clock_type="digital"
        )
        clocks_layout.addWidget(self.clock1)
        
        # Reloj 2: São Paulo
        self.clock2 = ClockWidget(
            timezone="America/Sao_Paulo",
            city_name="São Paulo",
            clock_type="analog"
        )
        clocks_layout.addWidget(self.clock2)
        
        layout.addLayout(clocks_layout)
        
        # Botones de prueba
        buttons_layout = QHBoxLayout()
        
        btn_switch1 = QPushButton("Cambiar Madrid a Analógico")
        btn_switch1.clicked.connect(lambda: self.clock1.change_clock_type("analog"))
        buttons_layout.addWidget(btn_switch1)
        
        btn_switch2 = QPushButton("Cambiar São Paulo a Digital")
        btn_switch2.clicked.connect(lambda: self.clock2.change_clock_type("digital"))
        buttons_layout.addWidget(btn_switch2)
        
        btn_switch1_back = QPushButton("Madrid a Digital")
        btn_switch1_back.clicked.connect(lambda: self.clock1.change_clock_type("digital"))
        buttons_layout.addWidget(btn_switch1_back)
        
        btn_switch2_back = QPushButton("São Paulo a Analógico")
        btn_switch2_back.clicked.connect(lambda: self.clock2.change_clock_type("analog"))
        buttons_layout.addWidget(btn_switch2_back)
        
        layout.addLayout(buttons_layout)
        
        # Información
        info = QLabel("Usa los botones para probar el cambio entre digital y analógico.\n"
                     "También puedes usar el botón ⚙ en cada reloj.")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("margin: 10px; color: #666;")
        layout.addWidget(info)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestClockSwitchWindow()
    window.show()
    
    sys.exit(app.exec_())

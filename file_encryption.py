#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema de encriptación de archivos usando la contraseña maestra del banner
"""

import os
import base64
import hashlib
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class FileEncryption:
    """Clase para manejar encriptación y desencriptación de archivos"""
    
    def __init__(self):
        self.salt = b'calendar_app_salt_2024'  # Salt fijo para consistencia
    
    def _derive_key(self, password: str) -> bytes:
        """Derivar clave de encriptación desde la contraseña"""
        password_bytes = password.encode('utf-8')
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        return key
    
    def encrypt_file(self, file_path: str, password: str) -> bool:
        """Encriptar un archivo con la contraseña dada"""
        try:
            # Leer contenido original
            with open(file_path, 'rb') as file:
                original_data = file.read()
            
            # Derivar clave
            key = self._derive_key(password)
            fernet = Fernet(key)
            
            # Encriptar datos
            encrypted_data = fernet.encrypt(original_data)
            
            # Escribir archivo encriptado
            encrypted_path = file_path + '.encrypted'
            with open(encrypted_path, 'wb') as file:
                file.write(encrypted_data)
            
            # Eliminar archivo original
            os.remove(file_path)
            
            # Renombrar archivo encriptado
            os.rename(encrypted_path, file_path)
            
            return True
            
        except Exception as e:
            print(f"Error encriptando {file_path}: {str(e)}")
            return False
    
    def decrypt_file(self, file_path: str, password: str) -> bool:
        """Desencriptar un archivo con la contraseña dada"""
        try:
            # Leer contenido encriptado
            with open(file_path, 'rb') as file:
                encrypted_data = file.read()
            
            # Derivar clave
            key = self._derive_key(password)
            fernet = Fernet(key)
            
            # Desencriptar datos
            decrypted_data = fernet.decrypt(encrypted_data)
            
            # Escribir archivo desencriptado
            decrypted_path = file_path + '.decrypted'
            with open(decrypted_path, 'wb') as file:
                file.write(decrypted_data)
            
            # Eliminar archivo encriptado
            os.remove(file_path)
            
            # Renombrar archivo desencriptado
            os.rename(decrypted_path, file_path)
            
            return True
            
        except Exception as e:
            print(f"Error desencriptando {file_path}: {str(e)}")
            return False
    
    def is_file_encrypted(self, file_path: str) -> bool:
        """Verificar si un archivo está encriptado"""
        try:
            with open(file_path, 'rb') as file:
                # Leer los primeros bytes para verificar si es un archivo Fernet
                header = file.read(10)
                # Los archivos Fernet empiezan con caracteres base64
                return header.startswith(b'gAAAAA')
        except:
            return False
    
    def encrypt_all_program_files(self, password: str, exclude_files=None) -> dict:
        """Encriptar todos los archivos del programa"""
        if exclude_files is None:
            exclude_files = [
                'file_encryption.py',  # No encriptar este archivo
                '__pycache__',
                '.git',
                '.encrypted',
                '.decrypted'
            ]
        
        results = {
            'encrypted': [],
            'failed': [],
            'skipped': []
        }
        
        # Obtener todos los archivos .py en el directorio actual
        for file_name in os.listdir('.'):
            if file_name.endswith('.py'):
                # Verificar si debe ser excluido
                if any(exclude in file_name for exclude in exclude_files):
                    results['skipped'].append(file_name)
                    continue
                
                # Verificar si ya está encriptado
                if self.is_file_encrypted(file_name):
                    results['skipped'].append(f"{file_name} (ya encriptado)")
                    continue
                
                # Encriptar archivo
                if self.encrypt_file(file_name, password):
                    results['encrypted'].append(file_name)
                else:
                    results['failed'].append(file_name)
        
        return results
    
    def decrypt_all_program_files(self, password: str, exclude_files=None) -> dict:
        """Desencriptar todos los archivos del programa"""
        if exclude_files is None:
            exclude_files = [
                'file_encryption.py',  # No desencriptar este archivo
                '__pycache__',
                '.git'
            ]
        
        results = {
            'decrypted': [],
            'failed': [],
            'skipped': []
        }
        
        # Obtener todos los archivos .py en el directorio actual
        for file_name in os.listdir('.'):
            if file_name.endswith('.py'):
                # Verificar si debe ser excluido
                if any(exclude in file_name for exclude in exclude_files):
                    results['skipped'].append(file_name)
                    continue
                
                # Verificar si está encriptado
                if not self.is_file_encrypted(file_name):
                    results['skipped'].append(f"{file_name} (no encriptado)")
                    continue
                
                # Desencriptar archivo
                if self.decrypt_file(file_name, password):
                    results['decrypted'].append(file_name)
                else:
                    results['failed'].append(file_name)
        
        return results


class EncryptedFileManager:
    """Gestor de archivos encriptados para la aplicación"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.encryption = FileEncryption()
    
    def get_master_password(self) -> str:
        """Obtener la contraseña maestra del banner"""
        return self.data_manager.get_config('banner_master_password', None)
    
    def encrypt_program_files(self) -> dict:
        """Encriptar archivos del programa usando la contraseña maestra"""
        master_password = self.get_master_password()
        
        if not master_password:
            return {
                'error': 'No hay contraseña maestra configurada',
                'encrypted': [],
                'failed': [],
                'skipped': []
            }
        
        return self.encryption.encrypt_all_program_files(master_password)
    
    def decrypt_program_files(self, password: str) -> dict:
        """Desencriptar archivos del programa con la contraseña proporcionada"""
        return self.encryption.decrypt_all_program_files(password)
    
    def verify_master_password(self, password: str) -> bool:
        """Verificar si la contraseña proporcionada es correcta"""
        master_password = self.get_master_password()
        return master_password == password if master_password else False
    
    def get_encryption_status(self) -> dict:
        """Obtener estado de encriptación de los archivos"""
        status = {
            'encrypted_files': [],
            'unencrypted_files': [],
            'total_files': 0
        }
        
        # Verificar archivos .py en el directorio actual
        for file_name in os.listdir('.'):
            if file_name.endswith('.py') and file_name != 'file_encryption.py':
                status['total_files'] += 1
                
                if self.encryption.is_file_encrypted(file_name):
                    status['encrypted_files'].append(file_name)
                else:
                    status['unencrypted_files'].append(file_name)
        
        return status


# Funciones de utilidad para uso directo
def encrypt_files_with_password(password: str) -> dict:
    """Función de utilidad para encriptar archivos con una contraseña específica"""
    encryption = FileEncryption()
    return encryption.encrypt_all_program_files(password)


def decrypt_files_with_password(password: str) -> dict:
    """Función de utilidad para desencriptar archivos con una contraseña específica"""
    encryption = FileEncryption()
    return encryption.decrypt_all_program_files(password)


def check_encryption_status() -> dict:
    """Función de utilidad para verificar el estado de encriptación"""
    encryption = FileEncryption()
    status = {
        'encrypted_files': [],
        'unencrypted_files': [],
        'total_files': 0
    }
    
    for file_name in os.listdir('.'):
        if file_name.endswith('.py') and file_name != 'file_encryption.py':
            status['total_files'] += 1
            
            if encryption.is_file_encrypted(file_name):
                status['encrypted_files'].append(file_name)
            else:
                status['unencrypted_files'].append(file_name)
    
    return status


if __name__ == "__main__":
    # Script de prueba
    print("🔐 Sistema de Encriptación de Archivos")
    print("=" * 40)
    
    # Verificar estado actual
    status = check_encryption_status()
    print(f"📊 Estado actual:")
    print(f"   Total de archivos: {status['total_files']}")
    print(f"   Encriptados: {len(status['encrypted_files'])}")
    print(f"   Sin encriptar: {len(status['unencrypted_files'])}")
    
    if status['encrypted_files']:
        print(f"   Archivos encriptados: {', '.join(status['encrypted_files'])}")
    
    if status['unencrypted_files']:
        print(f"   Archivos sin encriptar: {', '.join(status['unencrypted_files'])}")
    
    print("\n💡 Para encriptar/desencriptar archivos:")
    print("   1. Configure la contraseña maestra en el banner")
    print("   2. Use las funciones del EncryptedFileManager")
    print("   3. O use las funciones de utilidad directamente")

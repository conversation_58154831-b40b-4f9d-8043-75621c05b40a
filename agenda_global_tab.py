#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pestaña de Agenda Global - Muestra todos los eventos organizados por días
con funcionalidad de completar, eliminar y exportar a CSV
"""

import csv
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QMessageBox, QFileDialog,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QCheckBox, QAbstractItemView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


class AgendaGlobalTab(QWidget):
    """Pestaña de Agenda Global con todos los eventos organizados por días"""

    def __init__(self, data_manager, app_signals, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.app_signals = app_signals

        self.setup_ui()
        self.load_spacing_settings()
        self.refresh_agenda()

    def setup_ui(self):
        """Configurar la interfaz de usuario tipo Excel"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Título y botones superiores
        header_layout = QHBoxLayout()

        # Título
        title = QLabel("📋 AGENDA GLOBAL")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2C3E50; margin: 10px;")
        header_layout.addWidget(title)

        header_layout.addStretch()

        # Botón de actualizar
        self.refresh_button = QPushButton("🔄 Actualizar")
        self.refresh_button.setMinimumSize(120, 35)
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_agenda)
        header_layout.addWidget(self.refresh_button)

        # Botón de exportar CSV
        self.export_button = QPushButton("📊 Exportar CSV")
        self.export_button.setMinimumSize(120, 35)
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.export_button.clicked.connect(self.export_to_csv)
        header_layout.addWidget(self.export_button)

        layout.addLayout(header_layout)

        # Información
        info_label = QLabel(
            "📅 Todos los eventos en formato Excel. "
            "Arrastre los separadores de columnas para ajustar el ancho. "
            "Use el checkbox para marcar como completado."
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #7F8C8D; font-style: italic; margin: 5px;")
        layout.addWidget(info_label)

        # Tabla tipo Excel
        self.events_table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.events_table)

        # Contador de eventos
        self.count_label = QLabel("📊 Total de eventos: 0")
        self.count_label.setStyleSheet("color: #7F8C8D; font-weight: bold; margin: 5px;")
        layout.addWidget(self.count_label)

    def setup_table(self):
        """Configurar la tabla tipo Excel"""
        # Configurar columnas
        columns = [
            "✓", "Fecha", "Día", "Título", "Descripción",
            "Hora Inicio", "Hora Fin", "Categoría", "Prioridad", "Acciones"
        ]
        self.events_table.setColumnCount(len(columns))
        self.events_table.setHorizontalHeaderLabels(columns)

        # Estilo tipo Excel
        self.events_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #D0D0D0;
                background-color: white;
                alternate-background-color: #F8F9FA;
                selection-background-color: #E3F2FD;
                font-size: 10pt;
                border: 1px solid #BDC3C7;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E0E0E0;
                border-right: 1px solid #E0E0E0;
            }
            QHeaderView::section {
                background-color: #F0F0F0;
                padding: 10px;
                border: 1px solid #D0D0D0;
                font-weight: bold;
                font-size: 11pt;
            }
            QHeaderView::section:hover {
                background-color: #E8F4FD;
            }
        """)

        # Configuración de la tabla
        self.events_table.setAlternatingRowColors(True)
        self.events_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.events_table.verticalHeader().setVisible(False)
        self.events_table.setSortingEnabled(True)

        # Configurar redimensionamiento de columnas (tipo Excel)
        header = self.events_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)        # Checkbox
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # Fecha
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # Día
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # Título
        header.setSectionResizeMode(4, QHeaderView.Stretch)     # Descripción
        header.setSectionResizeMode(5, QHeaderView.Interactive)  # Hora Inicio
        header.setSectionResizeMode(6, QHeaderView.Interactive)  # Hora Fin
        header.setSectionResizeMode(7, QHeaderView.Interactive)  # Categoría
        header.setSectionResizeMode(8, QHeaderView.Interactive)  # Prioridad
        header.setSectionResizeMode(9, QHeaderView.Fixed)       # Acciones

        # Anchos iniciales de columnas
        self.events_table.setColumnWidth(0, 50)   # Checkbox
        self.events_table.setColumnWidth(1, 100)  # Fecha
        self.events_table.setColumnWidth(2, 100)  # Día
        self.events_table.setColumnWidth(3, 200)  # Título
        self.events_table.setColumnWidth(5, 100)  # Hora Inicio
        self.events_table.setColumnWidth(6, 100)  # Hora Fin
        self.events_table.setColumnWidth(7, 120)  # Categoría
        self.events_table.setColumnWidth(8, 100)  # Prioridad
        self.events_table.setColumnWidth(9, 100)  # Acciones

    def refresh_agenda(self):
        """Actualizar la agenda con todos los eventos en formato tabla Excel"""
        # Limpiar tabla
        self.events_table.setRowCount(0)

        # Obtener todos los eventos
        all_events = self.data_manager.get_all_events()

        if not all_events:
            self.count_label.setText("📊 Total de eventos: 0")
            return

        # Ordenar eventos por fecha y prioridad
        all_events.sort(key=lambda x: (
            x['date'],
            -x.get('priority', 1),  # Prioridad descendente
            x.get('time_start', '00:00:00') if not x.get('all_day', 0) else '00:00:00'
        ))

        # Configurar número de filas
        self.events_table.setRowCount(len(all_events))

        # Llenar tabla con eventos
        for row, event in enumerate(all_events):
            # Columna 0: Checkbox de completado
            checkbox = QCheckBox()
            checkbox.setChecked(event.get('completed', False))
            checkbox.stateChanged.connect(lambda state, eid=event['id']: self.on_event_completed_changed(eid, state == Qt.Checked))
            checkbox.setStyleSheet("QCheckBox { margin: 5px; }")
            self.events_table.setCellWidget(row, 0, checkbox)

            # Columna 1: Fecha
            try:
                date_obj = datetime.strptime(event['date'], "%Y-%m-%d")
                formatted_date = date_obj.strftime("%d/%m/%Y")
            except:
                formatted_date = event['date']
            self.events_table.setItem(row, 1, QTableWidgetItem(formatted_date))

            # Columna 2: Día de la semana
            try:
                date_obj = datetime.strptime(event['date'], "%Y-%m-%d")
                day_names_spanish = {
                    'Monday': 'Lunes', 'Tuesday': 'Martes', 'Wednesday': 'Miércoles',
                    'Thursday': 'Jueves', 'Friday': 'Viernes', 'Saturday': 'Sábado', 'Sunday': 'Domingo'
                }
                day_name = day_names_spanish.get(date_obj.strftime("%A"), date_obj.strftime("%A"))
            except:
                day_name = ""
            self.events_table.setItem(row, 2, QTableWidgetItem(day_name))

            # Columna 3: Título
            title_item = QTableWidgetItem(event.get('title', ''))
            if event.get('completed', False):
                title_item.setBackground(Qt.lightGray)
            self.events_table.setItem(row, 3, title_item)

            # Columna 4: Descripción
            desc_item = QTableWidgetItem(event.get('description', ''))
            if event.get('completed', False):
                desc_item.setBackground(Qt.lightGray)
            self.events_table.setItem(row, 4, desc_item)

            # Columna 5: Hora Inicio
            time_start = event.get('time_start', '') if not event.get('all_day') else 'Todo el día'
            self.events_table.setItem(row, 5, QTableWidgetItem(time_start))

            # Columna 6: Hora Fin
            time_end = event.get('time_end', '') if not event.get('all_day') else 'Todo el día'
            self.events_table.setItem(row, 6, QTableWidgetItem(time_end))

            # Columna 7: Categoría
            category_name = ""
            if event.get('category_id'):
                category = self.data_manager.get_category(event['category_id'])
                if category:
                    category_name = category['name']
            self.events_table.setItem(row, 7, QTableWidgetItem(category_name))

            # Columna 8: Prioridad
            priority_map = {1: "Normal", 2: "Poco Importante", 3: "Importante", 4: "Muy Importante"}
            priority_text = priority_map.get(event.get('priority', 1), "Normal")
            priority_item = QTableWidgetItem(priority_text)

            # Color según prioridad
            priority_colors = {
                1: "#27AE60",  # Verde - Normal
                2: "#F39C12",  # Amarillo - Poco Importante
                3: "#E67E22",  # Naranja - Importante
                4: "#E74C3C"   # Rojo - Muy Importante
            }
            priority_color = priority_colors.get(event.get('priority', 1), "#27AE60")
            priority_item.setBackground(Qt.white)
            priority_item.setForeground(Qt.black)
            priority_item.setData(Qt.UserRole, priority_color)
            self.events_table.setItem(row, 8, priority_item)

            # Columna 9: Botones de acción
            actions_widget = self.create_actions_widget(event['id'])
            self.events_table.setCellWidget(row, 9, actions_widget)

        # Actualizar contador
        self.count_label.setText(f"📊 Total de eventos: {len(all_events)}")

    def create_actions_widget(self, event_id):
        """Crear widget con botones de acción para cada fila"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(5)

        # Botón editar
        edit_button = QPushButton("✏️")
        edit_button.setFixedSize(25, 25)
        edit_button.setToolTip("Editar evento")
        edit_button.clicked.connect(lambda: self.on_event_edit_requested(event_id))
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        layout.addWidget(edit_button)

        # Botón eliminar
        delete_button = QPushButton("🗑️")
        delete_button.setFixedSize(25, 25)
        delete_button.setToolTip("Eliminar evento")
        delete_button.clicked.connect(lambda: self.on_event_delete_requested(event_id))
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        layout.addWidget(delete_button)

        return widget

    def clear_events(self):
        """Limpiar todos los eventos de la tabla"""
        self.events_table.setRowCount(0)

    def on_event_completed_changed(self, event_id, completed):
        """Manejar cambio en estado de completado"""
        success = self.data_manager.update_event_completed(event_id, completed)
        if success:
            # Emitir señal para actualizar otras pestañas
            self.app_signals.event_modified.emit(event_id)
        else:
            QMessageBox.warning(self, "Error", "No se pudo actualizar el estado del evento.")

    def on_event_edit_requested(self, event_id):
        """Manejar solicitud de edición de evento"""
        # Importar aquí para evitar importación circular
        from event_dialog import EventDialog

        event = self.data_manager.get_event(event_id)
        if not event:
            return

        dialog = EventDialog(self.data_manager, self, event=event)
        if dialog.exec_():
            event_data = dialog.get_event_data()
            success = self.data_manager.update_event(event_id, event_data)
            if success:
                self.app_signals.event_modified.emit(event_id)
                self.refresh_agenda()

    def on_event_delete_requested(self, event_id):
        """Manejar solicitud de eliminación de evento"""
        # Obtener datos del evento para mostrar en la confirmación
        event = self.data_manager.get_event(event_id)
        event_title = event.get('title', 'Evento sin título') if event else 'Evento'

        # Crear diálogo de confirmación
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("⚠️ Confirmar Eliminación")
        msg_box.setIcon(QMessageBox.Warning)

        # Mensaje de advertencia
        msg_box.setText(f"¿Está seguro de que desea eliminar el evento?")
        msg_box.setInformativeText(f"📅 Evento: {event_title}\n\n"
                                  f"⚠️ ADVERTENCIA: Se perderán todos los datos del evento.\n"
                                  f"Esta acción no se puede deshacer.")

        # Botones personalizados
        delete_button = msg_box.addButton("🗑️ ELIMINAR", QMessageBox.DestructiveRole)
        cancel_button = msg_box.addButton("❌ CANCELAR", QMessageBox.RejectRole)

        # Configurar el diálogo
        msg_box.setDefaultButton(cancel_button)  # Cancelar por defecto para seguridad

        # Mostrar diálogo y procesar respuesta
        msg_box.exec_()

        if msg_box.clickedButton() == delete_button:
            # Usuario confirmó la eliminación
            success = self.data_manager.delete_event(event_id)
            if success:
                self.app_signals.event_deleted.emit(event_id)
                self.refresh_agenda()
            else:
                QMessageBox.warning(self, "Error", "No se pudo eliminar el evento.")
        else:
            QMessageBox.warning(self, "Error", "No se pudo eliminar el evento.")

    def export_to_csv(self):
        """Exportar todos los eventos a un archivo CSV"""
        try:
            # Solicitar ubicación del archivo
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exportar Agenda a CSV",
                f"agenda_global_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "Archivos CSV (*.csv)"
            )

            if not file_path:
                return

            # Obtener todos los eventos
            all_events = self.data_manager.get_all_events()

            if not all_events:
                QMessageBox.information(self, "Sin eventos", "No hay eventos para exportar.")
                return

            # Crear archivo CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'Fecha', 'Día de la Semana', 'Título', 'Descripción',
                    'Hora Inicio', 'Hora Fin', 'Todo el Día', 'Categoría',
                    'Prioridad', 'Completado', 'Color'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # Escribir cabeceras
                writer.writeheader()

                # Ordenar eventos por fecha
                all_events.sort(key=lambda x: x['date'])

                # Escribir eventos
                for event in all_events:
                    # Convertir fecha para obtener día de la semana
                    try:
                        date_obj = datetime.strptime(event['date'], "%Y-%m-%d")
                        day_names_spanish = {
                            'Monday': 'Lunes', 'Tuesday': 'Martes', 'Wednesday': 'Miércoles',
                            'Thursday': 'Jueves', 'Friday': 'Viernes', 'Saturday': 'Sábado', 'Sunday': 'Domingo'
                        }
                        day_name = day_names_spanish.get(date_obj.strftime("%A"), date_obj.strftime("%A"))
                        formatted_date = date_obj.strftime("%d/%m/%Y")
                    except:
                        day_name = ""
                        formatted_date = event['date']

                    # Obtener categoría
                    category_name = ""
                    if event.get('category_id'):
                        category = self.data_manager.get_category(event['category_id'])
                        if category:
                            category_name = category['name']

                    # Mapear prioridad
                    priority_map = {1: "Normal", 2: "Poco Importante", 3: "Importante", 4: "Muy Importante"}
                    priority_text = priority_map.get(event.get('priority', 1), "Normal")

                    writer.writerow({
                        'Fecha': formatted_date,
                        'Día de la Semana': day_name,
                        'Título': event.get('title', ''),
                        'Descripción': event.get('description', ''),
                        'Hora Inicio': event.get('time_start', '') if not event.get('all_day') else 'Todo el día',
                        'Hora Fin': event.get('time_end', '') if not event.get('all_day') else 'Todo el día',
                        'Todo el Día': 'Sí' if event.get('all_day') else 'No',
                        'Categoría': category_name,
                        'Prioridad': priority_text,
                        'Completado': 'Sí' if event.get('completed') else 'No',
                        'Color': event.get('color', '')
                    })

            QMessageBox.information(
                self,
                "Exportación Exitosa",
                f"La agenda se ha exportado correctamente a:\n{file_path}\n\n"
                f"Total de eventos exportados: {len(all_events)}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error de Exportación",
                f"No se pudo exportar la agenda:\n{str(e)}"
            )

    def update_event_spacing(self, value):
        """Actualizar el espaciado entre eventos"""
        self.events_layout.setSpacing(value)
        # Guardar configuración
        if hasattr(self, 'data_manager'):
            self.data_manager.set_config('agenda_event_spacing', value)

    def update_day_spacing(self, value):
        """Actualizar el espaciado entre días (separadores)"""
        # Actualizar el margen de los separadores existentes
        for i in range(self.events_layout.count()):
            widget = self.events_layout.itemAt(i).widget()
            if isinstance(widget, QFrame) and widget.frameShape() == QFrame.HLine:
                widget.setStyleSheet(f"color: #BDC3C7; margin: {value}px 0px;")

        # Guardar configuración
        if hasattr(self, 'data_manager'):
            self.data_manager.set_config('agenda_day_spacing', value)

    def reset_spacing(self):
        """Resetear espaciado a valores por defecto"""
        # Resetear solo el espaciado de eventos (formato tipo Excel)
        self.event_spacing_slider.setValue(1)
        self.update_event_spacing(1)

    def load_spacing_settings(self):
        """Cargar configuración de espaciado guardada"""
        if hasattr(self, 'data_manager'):
            # Cargar solo espaciado de eventos (formato tipo Excel)
            event_spacing = self.data_manager.get_config('agenda_event_spacing', 1)
            self.event_spacing_slider.setValue(event_spacing)
            self.update_event_spacing(event_spacing)

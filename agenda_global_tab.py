#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pestaña de Agenda Global - Muestra todos los eventos organizados por días
con funcionalidad de completar, eliminar y exportar a CSV
"""

import sys
import csv
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QScrollArea, QFrame, QMessageBox,
                            QFileDialog, QSlider, QSpinBox, QGroupBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from event_item_widget import EventItemWidget


class DayHeaderWidget(QWidget):
    """Widget para mostrar la cabecera de un día"""

    def __init__(self, date_str, parent=None):
        super().__init__(parent)
        self.date_str = date_str
        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz de la cabecera del día"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 8, 10, 8)

        # Convertir fecha string a objeto datetime
        try:
            date_obj = datetime.strptime(self.date_str, "%Y-%m-%d")

            # Formatear fecha: LUNES 26/05/2025
            day_name = date_obj.strftime("%A").upper()
            day_names_spanish = {
                'MONDAY': 'LUNES',
                'TUESDAY': 'MARTES',
                'WEDNESDAY': 'MIÉRCOLES',
                'THURSDAY': 'JUEVES',
                'FRIDAY': 'VIERNES',
                'SATURDAY': 'SÁBADO',
                'SUNDAY': 'DOMINGO'
            }
            day_name_spanish = day_names_spanish.get(day_name, day_name)
            formatted_date = date_obj.strftime("%d/%m/%Y")

            header_text = f"{day_name_spanish} {formatted_date}"

        except ValueError:
            header_text = self.date_str

        # Label de la fecha
        self.date_label = QLabel(header_text)
        self.date_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.date_label.setStyleSheet("""
            QLabel {
                color: #2C3E50;
                background-color: #ECF0F1;
                padding: 8px 15px;
                border-radius: 5px;
                border-left: 4px solid #3498DB;
            }
        """)

        layout.addWidget(self.date_label)
        layout.addStretch()

        # Estilo del widget contenedor
        self.setStyleSheet("""
            DayHeaderWidget {
                background-color: #F8F9FA;
                border-bottom: 1px solid #BDC3C7;
                margin: 5px 0px;
            }
        """)


class AgendaGlobalTab(QWidget):
    """Pestaña de Agenda Global con todos los eventos organizados por días"""

    def __init__(self, data_manager, app_signals, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.app_signals = app_signals

        self.setup_ui()
        self.load_spacing_settings()
        self.refresh_agenda()

    def setup_ui(self):
        """Configurar la interfaz de usuario"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Título y botones superiores
        header_layout = QHBoxLayout()

        # Título
        title = QLabel("📋 AGENDA GLOBAL")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2C3E50; margin: 10px;")
        header_layout.addWidget(title)

        header_layout.addStretch()

        # Botón de actualizar
        self.refresh_button = QPushButton("🔄 Actualizar")
        self.refresh_button.setMinimumSize(120, 35)
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_agenda)
        header_layout.addWidget(self.refresh_button)

        # Botón de exportar CSV
        self.export_button = QPushButton("📊 Exportar CSV")
        self.export_button.setMinimumSize(120, 35)
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.export_button.clicked.connect(self.export_to_csv)
        header_layout.addWidget(self.export_button)

        layout.addLayout(header_layout)

        # Controles de espaciado tipo Excel
        spacing_frame = QGroupBox("📐 Ajuste de Espaciado (Tipo Excel)")
        spacing_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                margin: 5px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2C3E50;
            }
        """)
        spacing_layout = QHBoxLayout(spacing_frame)

        # Control de espaciado entre eventos
        spacing_layout.addWidget(QLabel("Espaciado entre eventos:"))
        self.event_spacing_slider = QSlider(Qt.Horizontal)
        self.event_spacing_slider.setRange(0, 20)
        self.event_spacing_slider.setValue(2)
        self.event_spacing_slider.setFixedWidth(150)
        self.event_spacing_slider.valueChanged.connect(self.update_event_spacing)
        spacing_layout.addWidget(self.event_spacing_slider)

        self.event_spacing_value = QSpinBox()
        self.event_spacing_value.setRange(0, 20)
        self.event_spacing_value.setValue(2)
        self.event_spacing_value.setSuffix(" px")
        self.event_spacing_value.valueChanged.connect(self.event_spacing_slider.setValue)
        self.event_spacing_slider.valueChanged.connect(self.event_spacing_value.setValue)
        spacing_layout.addWidget(self.event_spacing_value)

        spacing_layout.addWidget(QFrame())  # Separador

        # Control de espaciado entre días
        spacing_layout.addWidget(QLabel("Espaciado entre días:"))
        self.day_spacing_slider = QSlider(Qt.Horizontal)
        self.day_spacing_slider.setRange(5, 30)
        self.day_spacing_slider.setValue(15)
        self.day_spacing_slider.setFixedWidth(150)
        self.day_spacing_slider.valueChanged.connect(self.update_day_spacing)
        spacing_layout.addWidget(self.day_spacing_slider)

        self.day_spacing_value = QSpinBox()
        self.day_spacing_value.setRange(5, 30)
        self.day_spacing_value.setValue(15)
        self.day_spacing_value.setSuffix(" px")
        self.day_spacing_value.valueChanged.connect(self.day_spacing_slider.setValue)
        self.day_spacing_slider.valueChanged.connect(self.day_spacing_value.setValue)
        spacing_layout.addWidget(self.day_spacing_value)

        spacing_layout.addStretch()

        # Botón de reset
        reset_spacing_btn = QPushButton("🔄 Reset")
        reset_spacing_btn.setFixedSize(60, 25)
        reset_spacing_btn.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
        """)
        reset_spacing_btn.clicked.connect(self.reset_spacing)
        spacing_layout.addWidget(reset_spacing_btn)

        layout.addWidget(spacing_frame)

        # Información
        info_label = QLabel(
            "📅 Todos los eventos organizados por días. "
            "Use el checkbox para marcar como completado y el botón ✕ para eliminar."
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #7F8C8D; font-style: italic; margin: 5px;")
        layout.addWidget(info_label)

        # Área de scroll para los eventos
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Widget contenedor de eventos
        self.events_container = QWidget()
        self.events_layout = QVBoxLayout(self.events_container)
        self.events_layout.setContentsMargins(5, 5, 5, 5)
        self.events_layout.setSpacing(2)
        self.events_layout.setAlignment(Qt.AlignTop)  # Alinear eventos desde arriba

        self.scroll_area.setWidget(self.events_container)
        layout.addWidget(self.scroll_area)

        # Contador de eventos
        self.count_label = QLabel("📊 Total de eventos: 0")
        self.count_label.setStyleSheet("color: #7F8C8D; font-weight: bold; margin: 5px;")
        layout.addWidget(self.count_label)

    def refresh_agenda(self):
        """Actualizar la agenda con todos los eventos"""
        # Limpiar eventos existentes
        self.clear_events()

        # Obtener todos los eventos
        all_events = self.data_manager.get_all_events()

        if not all_events:
            # Mostrar mensaje si no hay eventos
            no_events_label = QLabel("📭 No hay eventos en la agenda")
            no_events_label.setAlignment(Qt.AlignCenter)
            no_events_label.setFont(QFont("Arial", 14))
            no_events_label.setStyleSheet("color: #7F8C8D; margin: 50px;")
            self.events_layout.addWidget(no_events_label)
            self.count_label.setText("📊 Total de eventos: 0")
            return

        # Organizar eventos por fecha
        events_by_date = {}
        for event in all_events:
            date = event['date']
            if date not in events_by_date:
                events_by_date[date] = []
            events_by_date[date].append(event)

        # Ordenar fechas
        sorted_dates = sorted(events_by_date.keys())

        # Crear widgets para cada día con eventos
        total_events = 0
        for date in sorted_dates:
            events_for_date = events_by_date[date]

            # Solo crear cabecera si hay eventos para este día
            if events_for_date:
                # Agregar cabecera del día
                day_header = DayHeaderWidget(date)
                self.events_layout.addWidget(day_header)

                # Ordenar eventos del día por prioridad (más importantes arriba) y hora
                events_for_date.sort(key=lambda x: (
                    -x.get('priority', 1),  # Prioridad descendente (4=Muy Importante, 3=Importante, 2=Poco Importante, 1=Normal)
                    x.get('time_start', '00:00:00') if not x.get('all_day', 0) else '00:00:00'  # Hora ascendente
                ))

                # Agregar eventos del día
                for event in events_for_date:
                    event_widget = EventItemWidget(event)
                    event_widget.completed_changed.connect(self.on_event_completed_changed)
                    event_widget.event_clicked.connect(self.on_event_edit_requested)
                    event_widget.event_delete_requested.connect(self.on_event_delete_requested)

                    self.events_layout.addWidget(event_widget)
                    total_events += 1

                # Agregar separador después de cada día
                separator = QFrame()
                separator.setFrameShape(QFrame.HLine)
                separator.setFrameShadow(QFrame.Sunken)
                separator.setStyleSheet("color: #BDC3C7; margin: 10px 0px;")
                self.events_layout.addWidget(separator)

        # NO agregar stretch al final para mantener alineación superior

        # Actualizar contador
        self.count_label.setText(f"📊 Total de eventos: {total_events}")

    def clear_events(self):
        """Limpiar todos los widgets de eventos"""
        while self.events_layout.count():
            child = self.events_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def on_event_completed_changed(self, event_id, completed):
        """Manejar cambio en estado de completado"""
        success = self.data_manager.update_event_completed(event_id, completed)
        if success:
            # Emitir señal para actualizar otras pestañas
            self.app_signals.event_modified.emit(event_id)
        else:
            QMessageBox.warning(self, "Error", "No se pudo actualizar el estado del evento.")

    def on_event_edit_requested(self, event_id):
        """Manejar solicitud de edición de evento"""
        # Importar aquí para evitar importación circular
        from event_dialog import EventDialog

        event = self.data_manager.get_event(event_id)
        if not event:
            return

        dialog = EventDialog(self.data_manager, self, event=event)
        if dialog.exec_():
            event_data = dialog.get_event_data()
            success = self.data_manager.update_event(event_id, event_data)
            if success:
                self.app_signals.event_modified.emit(event_id)
                self.refresh_agenda()

    def on_event_delete_requested(self, event_id):
        """Manejar solicitud de eliminación de evento"""
        success = self.data_manager.delete_event(event_id)
        if success:
            self.app_signals.event_deleted.emit(event_id)
            self.refresh_agenda()
        else:
            QMessageBox.warning(self, "Error", "No se pudo eliminar el evento.")

    def export_to_csv(self):
        """Exportar todos los eventos a un archivo CSV"""
        try:
            # Solicitar ubicación del archivo
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exportar Agenda a CSV",
                f"agenda_global_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "Archivos CSV (*.csv)"
            )

            if not file_path:
                return

            # Obtener todos los eventos
            all_events = self.data_manager.get_all_events()

            if not all_events:
                QMessageBox.information(self, "Sin eventos", "No hay eventos para exportar.")
                return

            # Crear archivo CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'Fecha', 'Día de la Semana', 'Título', 'Descripción',
                    'Hora Inicio', 'Hora Fin', 'Todo el Día', 'Categoría',
                    'Prioridad', 'Completado', 'Color'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # Escribir cabeceras
                writer.writeheader()

                # Ordenar eventos por fecha
                all_events.sort(key=lambda x: x['date'])

                # Escribir eventos
                for event in all_events:
                    # Convertir fecha para obtener día de la semana
                    try:
                        date_obj = datetime.strptime(event['date'], "%Y-%m-%d")
                        day_names_spanish = {
                            'Monday': 'Lunes', 'Tuesday': 'Martes', 'Wednesday': 'Miércoles',
                            'Thursday': 'Jueves', 'Friday': 'Viernes', 'Saturday': 'Sábado', 'Sunday': 'Domingo'
                        }
                        day_name = day_names_spanish.get(date_obj.strftime("%A"), date_obj.strftime("%A"))
                        formatted_date = date_obj.strftime("%d/%m/%Y")
                    except:
                        day_name = ""
                        formatted_date = event['date']

                    # Obtener categoría
                    category_name = ""
                    if event.get('category_id'):
                        category = self.data_manager.get_category(event['category_id'])
                        if category:
                            category_name = category['name']

                    # Mapear prioridad
                    priority_map = {1: "Normal", 2: "Poco Importante", 3: "Importante", 4: "Muy Importante"}
                    priority_text = priority_map.get(event.get('priority', 1), "Normal")

                    writer.writerow({
                        'Fecha': formatted_date,
                        'Día de la Semana': day_name,
                        'Título': event.get('title', ''),
                        'Descripción': event.get('description', ''),
                        'Hora Inicio': event.get('time_start', '') if not event.get('all_day') else 'Todo el día',
                        'Hora Fin': event.get('time_end', '') if not event.get('all_day') else 'Todo el día',
                        'Todo el Día': 'Sí' if event.get('all_day') else 'No',
                        'Categoría': category_name,
                        'Prioridad': priority_text,
                        'Completado': 'Sí' if event.get('completed') else 'No',
                        'Color': event.get('color', '')
                    })

            QMessageBox.information(
                self,
                "Exportación Exitosa",
                f"La agenda se ha exportado correctamente a:\n{file_path}\n\n"
                f"Total de eventos exportados: {len(all_events)}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error de Exportación",
                f"No se pudo exportar la agenda:\n{str(e)}"
            )

    def update_event_spacing(self, value):
        """Actualizar el espaciado entre eventos"""
        self.events_layout.setSpacing(value)
        # Guardar configuración
        if hasattr(self, 'data_manager'):
            self.data_manager.set_config('agenda_event_spacing', value)

    def update_day_spacing(self, value):
        """Actualizar el espaciado entre días (separadores)"""
        # Actualizar el margen de los separadores existentes
        for i in range(self.events_layout.count()):
            widget = self.events_layout.itemAt(i).widget()
            if isinstance(widget, QFrame) and widget.frameShape() == QFrame.HLine:
                widget.setStyleSheet(f"color: #BDC3C7; margin: {value}px 0px;")

        # Guardar configuración
        if hasattr(self, 'data_manager'):
            self.data_manager.set_config('agenda_day_spacing', value)

    def reset_spacing(self):
        """Resetear espaciado a valores por defecto"""
        # Resetear sliders y spinboxes
        self.event_spacing_slider.setValue(2)
        self.day_spacing_slider.setValue(15)

        # Aplicar cambios
        self.update_event_spacing(2)
        self.update_day_spacing(15)

    def load_spacing_settings(self):
        """Cargar configuración de espaciado guardada"""
        if hasattr(self, 'data_manager'):
            # Cargar espaciado de eventos
            event_spacing = self.data_manager.get_config('agenda_event_spacing', 2)
            self.event_spacing_slider.setValue(event_spacing)
            self.update_event_spacing(event_spacing)

            # Cargar espaciado de días
            day_spacing = self.data_manager.get_config('agenda_day_spacing', 15)
            self.day_spacing_slider.setValue(day_spacing)
            self.update_day_spacing(day_spacing)

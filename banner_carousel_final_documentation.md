# 🎠 Carrusel de Banners - Versión Final

## 📋 Especificaciones Técnicas

### 🎯 **Dimensiones Fijas:**
- **Carrusel**: 300px de altura (fijo)
- **Banners**: 600x200 píxeles (fijo)
- **Ventana Configuración**: 1000x700 píxeles (mejorada visibilidad)
- **Ventana Edición**: 650x550 píxeles (mejorada visibilidad)

### 🔄 **Comportamiento Dinámico:**

#### **1-3 Banners: Modo Fijo**
- ✅ **Posición**: Centrados horizontalmente
- ✅ **Espaciado**: 20px entre banners
- ✅ **Tamaño**: 600x200px cada uno
- ✅ **Comportamiento**: Estáticos, sin rotación
- ✅ **Interacción**: Clickeables individualmente

#### **4+ Banners: Modo <PERSON>l**
- ✅ **Rotación**: Automática como cinta continua
- ✅ **Transiciones**: Deslizar, desvanecer o instantáneo
- ✅ **Duración**: Configurable por banner (1-30 segundos)
- ✅ **Pausa**: Al pasar el mouse (configurable)
- ✅ **Tamaño**: 600x200px centrado en carrusel

## 🎨 Interfaz Mejorada

### **Diálogo de Gestión (1000x700px):**
```
┌─────────────────────────────────────────────────────────────┐
│  🎠 Gestión del Carrusel de Banners                        │
├─────────────────────┬───────────────────────────────────────┤
│ 📋 Lista Banners    │ ⚙️ Configuración Global              │
│                     │                                       │
│ ✅ Banner 1 (3.0s)  │ ☑️ Carrusel activo                   │
│ ✅ Banner 2 (4.0s)  │ 📐 Altura: 300 px (fijo)            │
│ ❌ Banner 3 (5.0s)  │ 🔄 Transición: Deslizar             │
│                     │ ⚡ Velocidad: 500ms                  │
│ ➕ Nuevo            │ ⏸️ Pausar al hover: ☑️               │
│ ✏️ Editar           │                                       │
│ 🗑️ Eliminar         │ 👁️ Vista Previa:                    │
│ 📋 Duplicar         │ ┌─────────────────────────────────┐   │
│                     │ │ 📌 Banners Fijos               │   │
│                     │ │ 3 banners • 600x200px          │   │
│                     │ │ • Centrados                     │   │
│                     │ └─────────────────────────────────┘   │
└─────────────────────┴───────────────────────────────────────┘
```

### **Diálogo de Edición (650x550px):**
```
┌─────────────────────────────────────────────────────────────┐
│  📝 Editar Banner                                           │
├─────────────────────────────────────────────────────────────┤
│ Estado:           ☑️ Banner activo                          │
│ Título:           [Mi Banner Promocional____________]       │
│ Imagen:           [/ruta/imagen.png_______] [📁 Examinar]   │
│ URL:              [https://ejemplo.com_______________]       │
│ Texto Alt:        [Texto alternativo________________]       │
│ Duración:         [5000] ms                                 │
│                                                             │
│ 📐 Tamaño de Banner: 600x200 píxeles | Carrusel: 300px     │
│                                                             │
│ Vista Previa:                                               │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                                                         │ │
│ │           [Imagen del banner 300x100]                  │ │
│ │                                                         │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Funcionalidades Implementadas

### ✨ **Gestión de Banners:**
- ✅ **➕ Agregar**: Nuevos banners ilimitados
- ✅ **✏️ Editar**: Modificar banners existentes
- ✅ **🗑️ Eliminar**: Eliminar con confirmación
- ✅ **📋 Duplicar**: Crear copias de banners
- ✅ **👁️ Vista Previa**: Tiempo real en configuración

### ✨ **Configuración Avanzada:**
- ✅ **Estado Global**: Habilitar/deshabilitar carrusel
- ✅ **Altura Fija**: 300px (no configurable)
- ✅ **Transiciones**: Deslizar, Desvanecer, Instantáneo
- ✅ **Velocidad**: 100-2000ms configurable
- ✅ **Pausa Inteligente**: Al hover del mouse

### ✨ **Comportamiento Adaptativo:**
- ✅ **1 Banner**: Centrado y fijo
- ✅ **2-3 Banners**: Múltiples centrados y fijos
- ✅ **4+ Banners**: Carrusel rotativo automático
- ✅ **Enlaces**: Clickeables con apertura en navegador

## 📁 Archivos del Sistema

### **`banner_carousel_widget.py` - Sistema Completo:**
```python
# Clases principales:
- BannerItem: Modelo de datos (600x200, duración, URL)
- BannerEditDialog: Edición individual (650x550)
- BannerManagementDialog: Gestión completa (1000x700)
- BannerCarouselWidget: Widget principal (altura 300px)

# Métodos clave:
- display_fixed_banners(): Modo 1-3 banners
- display_banner(): Modo 4+ banners (carrusel)
- create_banner_widget(): Widgets 600x200 individuales
- start_rotation(): Solo para 4+ banners
```

### **Integración en `calendar_tab.py`:**
```python
from banner_carousel_widget import BannerCarouselWidget

# Crear carrusel (altura fija 300px)
self.banner_carousel = BannerCarouselWidget(self.data_manager)
self.banner_carousel.banner_clicked.connect(self.on_banner_clicked)

# Agregar al layout (anchura completa)
main_layout.addWidget(self.banner_carousel)
```

## 🎯 Flujo de Uso Completo

### **1. 🚀 Configuración Inicial:**
```bash
# Ejecutar aplicación
python main.py

# Localizar carrusel (parte inferior, 300px altura)
# Clic en botón 🎠 para abrir gestión
```

### **2. ➕ Agregar Banners:**
```
1. Gestión → "➕ Nuevo"
2. Completar formulario:
   ✅ Estado: Banner activo
   📝 Título: "Banner Promocional"
   🖼️ Imagen: Seleccionar 600x200px
   🔗 URL: "https://ejemplo.com"
   📄 Alt: "Texto alternativo"
   ⏱️ Duración: 5000ms
3. Vista previa automática
4. Guardar → Banner agregado
```

### **3. 🔄 Comportamiento Automático:**
```
• 1 Banner:    [Banner] (centrado, fijo)
• 2 Banners:   [Banner] [Banner] (centrados, fijos)
• 3 Banners:   [Banner] [Banner] [Banner] (centrados, fijos)
• 4+ Banners:  🎠 Carrusel rotativo automático
```

## 🧪 Scripts de Prueba

### **`test_banner_carousel.py` - Prueba Completa:**
```bash
python test_banner_carousel.py
```
**Funcionalidades:**
- Interfaz de demostración
- Botones para crear banners de ejemplo
- Información en tiempo real
- Log de eventos y clics

### **`create_test_banner_images.py` - Imágenes de Prueba:**
```bash
python create_test_banner_images.py
```
**Crea 5 imágenes 600x200:**
- `banner_promocional_600x200.png`
- `banner_funciones_600x200.png`
- `banner_consejos_600x200.png`
- `banner_anuncio_600x200.png`
- `banner_celebracion_600x200.png`

## 💾 Configuración Guardada

### **Estructura JSON:**
```json
{
  "banner_carousel_config": {
    "enabled": true,
    "height": 300,
    "transition_type": "Deslizar",
    "transition_speed": 500,
    "pause_on_hover": true,
    "banners": [
      {
        "id": "banner_unique_id",
        "enabled": true,
        "title": "Mi Banner",
        "image_path": "/ruta/imagen_600x200.png",
        "url": "https://ejemplo.com",
        "alt_text": "Texto alternativo",
        "duration": 5000
      }
    ]
  }
}
```

## 🎨 Especificaciones Visuales

### **Carrusel (300px altura):**
- **Fondo**: Blanco #FFFFFF
- **Borde**: Gris #BDC3C7 (normal), Azul #3498DB (hover)
- **Botón**: 🎠 Verde turquesa, esquina superior derecha
- **Contenido**: Centrado vertical y horizontalmente

### **Banners Individuales (600x200):**
- **Tamaño**: Fijo 600x200 píxeles
- **Borde**: Gris #BDC3C7 (normal), Azul #3498DB (hover)
- **Espaciado**: 20px entre banners (modo fijo)
- **Cursor**: Pointer cuando tienen URL

### **Estados del Carrusel:**
- **Sin Banners**: "🎠 Sin banners configurados"
- **Deshabilitado**: "🎠 Carrusel deshabilitado"
- **1-3 Banners**: Fijos y centrados
- **4+ Banners**: Rotación automática

## 💡 Mejores Prácticas

### **Imágenes Recomendadas:**
- **Tamaño**: Exactamente 600x200 píxeles
- **Formato**: PNG (transparencias) o JPG (fotos)
- **Calidad**: Alta resolución para mejor visualización
- **Contenido**: Texto legible, colores contrastantes

### **Configuración Óptima:**
- **Duración**: 3-7 segundos por banner
- **Cantidad**: 3-5 banners para mejor experiencia
- **URLs**: Verificar que funcionen correctamente
- **Texto Alt**: Siempre incluir para accesibilidad

### **Transiciones:**
- **Deslizar**: Mejor para contenido relacionado
- **Desvanecer**: Suave para cualquier contenido
- **Instantáneo**: Rápido para muchos banners

## ✅ Resultado Final

🎉 **CARRUSEL COMPLETAMENTE FUNCIONAL:**
- ✅ **Altura fija**: 300px para consistencia visual
- ✅ **Banners estándar**: 600x200px para uniformidad
- ✅ **Comportamiento adaptativo**: Fijo (1-3) vs Rotativo (4+)
- ✅ **Interfaz mejorada**: Ventanas más grandes y claras
- ✅ **Gestión completa**: Agregar, editar, duplicar, eliminar
- ✅ **Vista previa**: Tiempo real en configuración
- ✅ **Enlaces funcionales**: Clickeables con apertura en navegador
- ✅ **Persistencia**: Configuración guardada automáticamente

¡El carrusel está listo para uso profesional! 🚀

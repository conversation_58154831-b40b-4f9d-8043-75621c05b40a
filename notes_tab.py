#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QListWidget, 
                            QListWidgetItem, QTextEdit, QPushButton, QToolBar, QAction,
                            QLineEdit, QMenu, QMessageBox, QInputDialog, QComboBox, QLabel)
from PyQt5.QtCore import Qt, QDateTime, pyqtSlot, QSize
from PyQt5.QtGui import QIcon, QColor, QBrush, QFont


class NotesTab(QWidget):
    def __init__(self, data_manager, app_signals, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.app_signals = app_signals
        
        # ID de la nota actual
        self.current_note_id = None
        
        # Flag para controlar si los cambios en el editor deben actualizar la nota
        self.update_enabled = True
        
        self.init_ui()
        
        # Conectar señales
        self.app_signals.note_added.connect(self.refresh_notes)
        self.app_signals.note_modified.connect(self.refresh_notes)
        self.app_signals.note_deleted.connect(self.refresh_notes)
    
    def init_ui(self):
        """Inicializar la interfaz de usuario"""
        layout = QVBoxLayout(self)
        
        # Barra de herramientas
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(20, 20))
        
        # Botón para nueva nota
        new_note_action = QAction(QIcon("icons/note_add.png"), "Nueva nota", self)
        new_note_action.triggered.connect(self.new_note)
        toolbar.addAction(new_note_action)
        
        toolbar.addSeparator()
        
        # Campo de búsqueda
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Buscar notas...")
        self.search_edit.textChanged.connect(self.search_notes)
        toolbar.addWidget(self.search_edit)
        
        # Filtro de categorías
        self.category_combo = QComboBox()
        self.category_combo.addItem("Todas las categorías", None)
        
        # Cargar categorías
        categories = self.data_manager.get_all_categories()
        for category in categories:
            self.category_combo.addItem(category['name'], category['id'])
        
        self.category_combo.currentIndexChanged.connect(self.refresh_notes)
        toolbar.addWidget(self.category_combo)
        
        layout.addWidget(toolbar)
        
        # Splitter principal
        splitter = QSplitter(Qt.Horizontal)
        
        # Lista de notas
        self.notes_list = QListWidget()
        self.notes_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.notes_list.customContextMenuRequested.connect(self.show_context_menu)
        splitter.addWidget(self.notes_list)
        
        # Editor de notas
        editor_widget = QWidget()
        editor_layout = QVBoxLayout(editor_widget)
        editor_layout.setContentsMargins(0, 0, 0, 0)
        
        # Título de la nota
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("Título de la nota")
        self.title_edit.textChanged.connect(self.update_current_note_title)
        editor_layout.addWidget(self.title_edit)
        
        # Editor de texto
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("Escriba su nota aquí...")
        self.text_edit.textChanged.connect(self.update_current_note)
        editor_layout.addWidget(self.text_edit)
        
        # Botones del editor
        editor_buttons = QHBoxLayout()
        
        self.btn_save = QPushButton("Guardar")
        self.btn_save.clicked.connect(self.save_note)
        self.btn_save.setEnabled(False)
        editor_buttons.addWidget(self.btn_save)
        
        self.btn_delete = QPushButton("Eliminar")
        self.btn_delete.clicked.connect(self.delete_note)
        self.btn_delete.setEnabled(False)
        editor_buttons.addWidget(self.btn_delete)
        
        editor_layout.addLayout(editor_buttons)
        
        splitter.addWidget(editor_widget)
        
        # Establecer tamaños relativos
        splitter.setSizes([int(self.width() * 0.3), int(self.width() * 0.7)])
        
        layout.addWidget(splitter)
        
        # Conectar evento de selección
        self.notes_list.itemSelectionChanged.connect(self.note_selected)
        
        # Cargar notas
        self.refresh_notes()
    
    def new_note(self):
        """Crear una nueva nota"""
        title, ok = QInputDialog.getText(self, "Nueva nota", "Título de la nota:")
        if ok and title:
            # Crear la nota en la base de datos
            note_data = {
                'title': title,
                'content': '',
                'category_id': self.category_combo.currentData()
            }
            
            note_id = self.data_manager.add_note(note_data)
            if note_id:
                self.app_signals.note_added.emit(note_id)
                self.refresh_notes()
                
                # Seleccionar la nueva nota
                for i in range(self.notes_list.count()):
                    item = self.notes_list.item(i)
                    if item and item.data(Qt.UserRole) == note_id:
                        self.notes_list.setCurrentItem(item)
                        break
    
    def load_notes(self, search_query=None, category_id=None):
        """Cargar notas en la lista"""
        self.notes_list.clear()
        
        # Obtener todas las notas
        notes = self.data_manager.get_all_notes()
        
        # Filtrar por búsqueda si es necesario
        if search_query:
            notes = [n for n in notes if search_query.lower() in n['title'].lower() or
                    (n.get('content') and search_query.lower() in n['content'].lower())]
        
        # Filtrar por categoría si es necesario
        if category_id:
            notes = [n for n in notes if n.get('category_id') == category_id]
        
        if not notes:
            item = QListWidgetItem("No hay notas")
            item.setFlags(Qt.NoItemFlags)
            self.notes_list.addItem(item)
            return
        
        # Ordenar por fecha de modificación (más reciente primero)
        notes.sort(key=lambda x: x.get('modified_date', ''), reverse=True)
        
        for note in notes:
            item = QListWidgetItem(note['title'])
            item.setData(Qt.UserRole, note['id'])
            
            # Añadir color si existe categoría
            if note.get('category_color'):
                item.setForeground(QBrush(QColor(note['category_color'])))
            
            self.notes_list.addItem(item)
    
    def note_selected(self):
        """Manejar selección de nota"""
        selected_items = self.notes_list.selectedItems()
        if not selected_items:
            self.clear_editor()
            self.btn_save.setEnabled(False)
            self.btn_delete.setEnabled(False)
            return
        
        item = selected_items[0]
        note_id = item.data(Qt.UserRole)
        
        if not note_id:
            return
        
        # Cargar la nota
        self.load_note(note_id)
        
        # Habilitar botones
        self.btn_save.setEnabled(True)
        self.btn_delete.setEnabled(True)
    
    def load_note(self, note_id):
        """Cargar una nota en el editor"""
        note = self.data_manager.get_note(note_id)
        if not note:
            return
        
        # Desactivar actualización mientras cargamos la nota
        self.update_enabled = False
        
        self.current_note_id = note_id
        self.title_edit.setText(note['title'])
        self.text_edit.setPlainText(note.get('content', ''))
        
        # Reactivar actualización
        self.update_enabled = True
    
    def clear_editor(self):
        """Limpiar el editor"""
        self.update_enabled = False
        
        self.current_note_id = None
        self.title_edit.clear()
        self.text_edit.clear()
        
        self.update_enabled = True
    
    def update_current_note_title(self):
        """Actualizar el título de la nota actual"""
        if not self.update_enabled or not self.current_note_id:
            return
        
        # Actualizar el título en la lista
        selected_items = self.notes_list.selectedItems()
        if selected_items:
            selected_items[0].setText(self.title_edit.text())
    
    def update_current_note(self):
        """Actualizar el contenido de la nota actual"""
        if not self.update_enabled or not self.current_note_id:
            return
    
    def save_note(self):
        """Guardar la nota actual"""
        if not self.current_note_id:
            return
        
        note_data = {
            'title': self.title_edit.text(),
            'content': self.text_edit.toPlainText()
        }
        
        success = self.data_manager.update_note(self.current_note_id, note_data)
        if success:
            self.app_signals.note_modified.emit(self.current_note_id)
            
            # Actualizar solo la lista sin cambiar la selección
            search_query = self.search_edit.text() if self.search_edit.text() else None
            category_id = self.category_combo.currentData()
            
            self.load_notes(search_query, category_id)
            
            # Mantener la selección
            for i in range(self.notes_list.count()):
                item = self.notes_list.item(i)
                if item and item.data(Qt.UserRole) == self.current_note_id:
                    self.notes_list.setCurrentItem(item)
                    break
    
    def delete_note(self):
        """Eliminar la nota actual"""
        if not self.current_note_id:
            return
        
        confirm = QMessageBox.question(
            self, "Confirmar eliminación", 
            "¿Está seguro de eliminar esta nota?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if confirm == QMessageBox.Yes:
            success = self.data_manager.delete_note(self.current_note_id)
            if success:
                self.app_signals.note_deleted.emit(self.current_note_id)
                self.clear_editor()
                self.refresh_notes()
    
    def show_context_menu(self, position):
        """Mostrar menú contextual para la lista de notas"""
        selected_items = self.notes_list.selectedItems()
        if not selected_items:
            return
        
        item = selected_items[0]
        note_id = item.data(Qt.UserRole)
        
        if not note_id:
            return
        
        menu = QMenu()
        edit_title_action = menu.addAction("Editar título")
        delete_action = menu.addAction("Eliminar")
        
        action = menu.exec_(self.notes_list.mapToGlobal(position))
        
        if action == edit_title_action:
            self.edit_note_title(note_id)
        elif action == delete_action:
            self.delete_selected_note()
    
    def edit_note_title(self, note_id):
        """Editar el título de una nota"""
        note = self.data_manager.get_note(note_id)
        if not note:
            return
        
        title, ok = QInputDialog.getText(
            self, "Editar título", "Nuevo título de la nota:", 
            text=note['title']
        )
        
        if ok and title:
            note_data = {
                'title': title,
                'content': note.get('content', '')
            }
            
            success = self.data_manager.update_note(note_id, note_data)
            if success:
                self.app_signals.note_modified.emit(note_id)
                
                # Si es la nota actual, actualizar también el editor
                if self.current_note_id == note_id:
                    self.title_edit.setText(title)
                
                self.refresh_notes()
    
    def delete_selected_note(self):
        """Eliminar la nota seleccionada"""
        selected_items = self.notes_list.selectedItems()
        if not selected_items:
            return
        
        item = selected_items[0]
        note_id = item.data(Qt.UserRole)
        
        if not note_id:
            return
        
        confirm = QMessageBox.question(
            self, "Confirmar eliminación", 
            "¿Está seguro de eliminar esta nota?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if confirm == QMessageBox.Yes:
            success = self.data_manager.delete_note(note_id)
            if success:
                self.app_signals.note_deleted.emit(note_id)
                
                # Si es la nota actual, limpiar el editor
                if self.current_note_id == note_id:
                    self.clear_editor()
                
                self.refresh_notes()
    
    def search_notes(self, query):
        """Buscar notas"""
        self.refresh_notes()
    
    @pyqtSlot()
    def refresh_notes(self):
        """Actualizar la lista de notas"""
        search_query = self.search_edit.text() if self.search_edit.text() else None
        category_id = self.category_combo.currentData()
        
        # Guardar ID de nota seleccionada
        selected_note_id = self.current_note_id
        
        self.load_notes(search_query, category_id)
        
        # Restaurar selección si es posible
        if selected_note_id:
            for i in range(self.notes_list.count()):
                item = self.notes_list.item(i)
                if item and item.data(Qt.UserRole) == selected_note_id:
                    self.notes_list.setCurrentItem(item)
                    return

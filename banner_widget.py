#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget de banner configurable con protección por contraseña
"""

import os
import webbrowser
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QFileDialog, QMessageBox,
                            QDialog, QDialogButtonBox, QFormLayout, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QFont, QCursor


class BannerConfigDialog(QDialog):
    """Diálogo para configurar el banner"""
    
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setWindowTitle("Configurar Banner")
        self.setModal(True)
        self.setFixedSize(500, 400)
        
        self.setup_ui()
        self.load_current_config()
    
    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)
        
        # Título
        title = QLabel("Configuración del Banner")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 10px;")
        layout.addWidget(title)
        
        # Formulario
        form_layout = QFormLayout()
        
        # Habilitar banner
        self.enabled_checkbox = QCheckBox("Habilitar banner")
        form_layout.addRow("Estado:", self.enabled_checkbox)
        
        # Imagen
        image_layout = QHBoxLayout()
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setPlaceholderText("Ruta de la imagen...")
        self.browse_button = QPushButton("Examinar")
        self.browse_button.clicked.connect(self.browse_image)
        image_layout.addWidget(self.image_path_edit)
        image_layout.addWidget(self.browse_button)
        form_layout.addRow("Imagen:", image_layout)
        
        # URL
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("https://ejemplo.com")
        form_layout.addRow("URL:", self.url_edit)
        
        # Texto alternativo
        self.alt_text_edit = QLineEdit()
        self.alt_text_edit.setPlaceholderText("Texto cuando no hay imagen")
        form_layout.addRow("Texto alternativo:", self.alt_text_edit)
        
        layout.addLayout(form_layout)
        
        # Vista previa
        preview_label = QLabel("Vista previa:")
        preview_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(preview_label)
        
        self.preview_widget = QWidget()
        self.preview_widget.setFixedHeight(60)
        self.preview_widget.setStyleSheet("border: 1px solid #BDC3C7; background-color: #F8F9FA;")
        layout.addWidget(self.preview_widget)
        
        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Conectar señales para vista previa
        self.image_path_edit.textChanged.connect(self.update_preview)
        self.alt_text_edit.textChanged.connect(self.update_preview)
        self.enabled_checkbox.stateChanged.connect(self.update_preview)
    
    def browse_image(self):
        """Examinar archivo de imagen"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar imagen",
            "",
            "Imágenes (*.png *.jpg *.jpeg *.gif *.bmp);;Todos los archivos (*)"
        )
        
        if file_path:
            self.image_path_edit.setText(file_path)
    
    def update_preview(self):
        """Actualizar vista previa del banner"""
        # Limpiar layout anterior
        if self.preview_widget.layout():
            while self.preview_widget.layout().count():
                child = self.preview_widget.layout().takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
        else:
            layout = QHBoxLayout(self.preview_widget)
            layout.setContentsMargins(5, 5, 5, 5)
        
        if not self.enabled_checkbox.isChecked():
            label = QLabel("Banner deshabilitado")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("color: #7F8C8D; font-style: italic;")
            self.preview_widget.layout().addWidget(label)
            return
        
        image_path = self.image_path_edit.text().strip()
        alt_text = self.alt_text_edit.text().strip()
        
        if image_path and os.path.exists(image_path):
            # Mostrar imagen
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(100, 50, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                image_label = QLabel()
                image_label.setPixmap(scaled_pixmap)
                image_label.setAlignment(Qt.AlignCenter)
                self.preview_widget.layout().addWidget(image_label)
            else:
                # Imagen inválida, mostrar texto
                text_label = QLabel(alt_text or "Imagen inválida")
                text_label.setAlignment(Qt.AlignCenter)
                self.preview_widget.layout().addWidget(text_label)
        else:
            # Mostrar texto alternativo
            text_label = QLabel(alt_text or "Sin imagen")
            text_label.setAlignment(Qt.AlignCenter)
            self.preview_widget.layout().addWidget(text_label)
    
    def load_current_config(self):
        """Cargar configuración actual"""
        config = self.data_manager.get_config('banner_config', {})
        
        self.enabled_checkbox.setChecked(config.get('enabled', False))
        self.image_path_edit.setText(config.get('image_path', ''))
        self.url_edit.setText(config.get('url', ''))
        self.alt_text_edit.setText(config.get('alt_text', ''))
        
        self.update_preview()
    
    def get_config(self):
        """Obtener configuración del diálogo"""
        return {
            'enabled': self.enabled_checkbox.isChecked(),
            'image_path': self.image_path_edit.text().strip(),
            'url': self.url_edit.text().strip(),
            'alt_text': self.alt_text_edit.text().strip()
        }


class PasswordDialog(QDialog):
    """Diálogo para solicitar contraseña"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Contraseña requerida")
        self.setModal(True)
        self.setFixedSize(300, 150)
        
        layout = QVBoxLayout(self)
        
        # Mensaje
        message = QLabel("Ingrese la contraseña para configurar el banner:")
        message.setWordWrap(True)
        layout.addWidget(message)
        
        # Campo de contraseña
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("Contraseña...")
        layout.addWidget(self.password_edit)
        
        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Foco en el campo de contraseña
        self.password_edit.setFocus()
    
    def get_password(self):
        """Obtener la contraseña ingresada"""
        return self.password_edit.text()


class BannerWidget(QWidget):
    """Widget del banner configurable"""
    
    # Contraseña para configurar el banner (en producción debería estar encriptada)
    BANNER_PASSWORD = "admin123"
    
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        
        self.setup_ui()
        self.load_banner_config()
    
    def setup_ui(self):
        """Configurar la interfaz del banner"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(5, 5, 5, 5)
        
        # Altura fija igual a una cuadrícula del calendario (aproximadamente 60px)
        self.setFixedHeight(60)
        self.setStyleSheet("""
            BannerWidget {
                background-color: #FFFFFF;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
            }
            BannerWidget:hover {
                border: 1px solid #3498DB;
            }
        """)
        
        # Widget de contenido del banner
        self.content_widget = QWidget()
        self.content_layout = QHBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        
        self.layout.addWidget(self.content_widget)
        
        # Botón de configuración (pequeño, en la esquina)
        self.config_button = QPushButton("⚙")
        self.config_button.setFixedSize(20, 20)
        self.config_button.setToolTip("Configurar banner")
        self.config_button.setStyleSheet("""
            QPushButton {
                background-color: #ECF0F1;
                border: 1px solid #BDC3C7;
                border-radius: 10px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #D5DBDB;
            }
        """)
        self.config_button.clicked.connect(self.configure_banner)
        
        # Posicionar botón en la esquina superior derecha
        self.layout.addWidget(self.config_button, 0, Qt.AlignTop | Qt.AlignRight)
        
        # Configurar cursor para indicar que es clickeable
        self.setCursor(QCursor(Qt.PointingHandCursor))
    
    def load_banner_config(self):
        """Cargar y aplicar configuración del banner"""
        config = self.data_manager.get_config('banner_config', {})
        
        # Limpiar contenido anterior
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        if not config.get('enabled', False):
            # Banner deshabilitado
            label = QLabel("Banner deshabilitado - Haga clic en ⚙ para configurar")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("color: #7F8C8D; font-style: italic; font-size: 10px;")
            self.content_layout.addWidget(label)
            self.url = None
            return
        
        # Banner habilitado
        image_path = config.get('image_path', '')
        self.url = config.get('url', '')
        alt_text = config.get('alt_text', '')
        
        if image_path and os.path.exists(image_path):
            # Mostrar imagen
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # Escalar imagen para que quepa en el banner
                scaled_pixmap = pixmap.scaled(
                    self.width() - 50, 50, 
                    Qt.KeepAspectRatio, 
                    Qt.SmoothTransformation
                )
                
                image_label = QLabel()
                image_label.setPixmap(scaled_pixmap)
                image_label.setAlignment(Qt.AlignCenter)
                self.content_layout.addWidget(image_label)
            else:
                # Imagen inválida, mostrar texto alternativo
                text_label = QLabel(alt_text or "Imagen no válida")
                text_label.setAlignment(Qt.AlignCenter)
                text_label.setStyleSheet("color: #2C3E50; font-weight: bold;")
                self.content_layout.addWidget(text_label)
        else:
            # Mostrar texto alternativo
            text_label = QLabel(alt_text or "Banner sin imagen")
            text_label.setAlignment(Qt.AlignCenter)
            text_label.setStyleSheet("color: #2C3E50; font-weight: bold;")
            self.content_layout.addWidget(text_label)
    
    def mousePressEvent(self, event):
        """Manejar clic en el banner"""
        if event.button() == Qt.LeftButton and self.url:
            # Abrir URL en el navegador
            try:
                webbrowser.open(self.url)
            except Exception as e:
                QMessageBox.warning(self, "Error", f"No se pudo abrir la URL: {str(e)}")
        
        super().mousePressEvent(event)
    
    def configure_banner(self):
        """Configurar el banner (requiere contraseña)"""
        # Solicitar contraseña
        password_dialog = PasswordDialog(self)
        if password_dialog.exec_() != QDialog.Accepted:
            return
        
        password = password_dialog.get_password()
        if password != self.BANNER_PASSWORD:
            QMessageBox.warning(self, "Acceso denegado", "Contraseña incorrecta.")
            return
        
        # Mostrar diálogo de configuración
        config_dialog = BannerConfigDialog(self.data_manager, self)
        if config_dialog.exec_() == QDialog.Accepted:
            # Guardar configuración
            config = config_dialog.get_config()
            self.data_manager.set_config('banner_config', config)
            
            # Recargar banner
            self.load_banner_config()
            
            QMessageBox.information(self, "Configuración guardada", "El banner ha sido configurado correctamente.")
    
    def resizeEvent(self, event):
        """Manejar redimensionamiento del widget"""
        super().resizeEvent(event)
        # Recargar banner para ajustar imagen al nuevo tamaño
        self.load_banner_config()

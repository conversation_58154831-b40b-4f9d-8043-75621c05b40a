#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget de banner configurable con protección por contraseña
"""

import os
import webbrowser
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QLineEdit, QFileDialog, QMessageBox,
                            QDialog, QDialogButtonBox, QFormLayout, QCheckBox,
                            QTextEdit, QTabWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QFont, QCursor

try:
    from file_encryption import EncryptedFileManager
    from protected_storage import ProtectedStorage
    ENCRYPTION_AVAILABLE = True
except ImportError:
    ENCRYPTION_AVAILABLE = False
    print("⚠️ Módulo de encriptación no disponible. Instale: pip install cryptography")


class BannerConfigDialog(QDialog):
    """Diálogo para configurar el banner"""

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setWindowTitle("Configurar Banner")
        self.setModal(True)
        self.setFixedSize(500, 400)

        self.setup_ui()
        self.load_current_config()

    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)

        # Título
        title = QLabel("Configuración del Banner")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 10px;")
        layout.addWidget(title)

        # Formulario
        form_layout = QFormLayout()

        # Habilitar banner
        self.enabled_checkbox = QCheckBox("Habilitar banner")
        form_layout.addRow("Estado:", self.enabled_checkbox)

        # Imagen
        image_layout = QHBoxLayout()
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setPlaceholderText("Ruta de la imagen...")
        self.browse_button = QPushButton("Examinar")
        self.browse_button.clicked.connect(self.browse_image)
        image_layout.addWidget(self.image_path_edit)
        image_layout.addWidget(self.browse_button)
        form_layout.addRow("Imagen:", image_layout)

        # URL
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("https://ejemplo.com")
        form_layout.addRow("URL:", self.url_edit)

        # Texto alternativo
        self.alt_text_edit = QLineEdit()
        self.alt_text_edit.setPlaceholderText("Texto cuando no hay imagen")
        form_layout.addRow("Texto alternativo:", self.alt_text_edit)

        # Altura del banner
        from PyQt5.QtWidgets import QSpinBox
        self.height_spinbox = QSpinBox()
        self.height_spinbox.setRange(40, 200)  # Rango de altura en píxeles
        self.height_spinbox.setValue(67)  # Valor por defecto
        self.height_spinbox.setSuffix(" px")
        self.height_spinbox.setToolTip("Altura del banner en píxeles (40-200)")
        form_layout.addRow("Altura del banner:", self.height_spinbox)

        layout.addLayout(form_layout)

        # Información de tamaño recomendado
        size_info = QLabel("📐 Tamaño recomendado: 600x80 píxeles (o proporcional) | DPI: 96-300")
        size_info.setStyleSheet("color: #7F8C8D; font-size: 9pt; font-style: italic; margin: 5px;")
        size_info.setWordWrap(True)
        layout.addWidget(size_info)

        # Vista previa
        preview_label = QLabel("Vista previa:")
        preview_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(preview_label)

        self.preview_widget = QWidget()
        self.preview_widget.setFixedHeight(67)  # Altura por defecto
        self.preview_widget.setStyleSheet("border: 2px solid #BDC3C7; background-color: #F8F9FA; border-radius: 3px;")
        layout.addWidget(self.preview_widget)

        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Conectar señales para vista previa
        self.image_path_edit.textChanged.connect(self.update_preview)
        self.alt_text_edit.textChanged.connect(self.update_preview)
        self.enabled_checkbox.stateChanged.connect(self.update_preview)
        self.height_spinbox.valueChanged.connect(self.update_preview)

    def browse_image(self):
        """Examinar archivo de imagen y copiarlo al almacenamiento protegido"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar imagen para el banner",
            "",
            "Imágenes (*.png *.jpg *.jpeg *.gif *.bmp);;Todos los archivos (*)"
        )

        if file_path:
            try:
                # Crear almacenamiento protegido
                if ENCRYPTION_AVAILABLE:
                    storage = ProtectedStorage(self.data_manager)

                    # Copiar imagen al almacenamiento protegido
                    protected_path = storage.copy_image_to_storage(file_path)
                    self.image_path_edit.setText(protected_path)

                    QMessageBox.information(
                        self,
                        "Imagen Copiada",
                        f"La imagen ha sido copiada al almacenamiento protegido.\n\n"
                        f"Archivo original: {os.path.basename(file_path)}\n"
                        f"Ubicación protegida: {os.path.basename(protected_path)}"
                    )
                else:
                    # Sin encriptación, usar ruta directa
                    self.image_path_edit.setText(file_path)

            except Exception as e:
                QMessageBox.warning(
                    self,
                    "Error",
                    f"No se pudo copiar la imagen al almacenamiento protegido:\n{str(e)}"
                )
                # Como fallback, usar ruta original
                self.image_path_edit.setText(file_path)

    def update_preview(self):
        """Actualizar vista previa del banner"""
        # Actualizar altura del widget de vista previa
        banner_height = self.height_spinbox.value()
        self.preview_widget.setFixedHeight(banner_height)

        # Limpiar layout anterior
        if self.preview_widget.layout():
            while self.preview_widget.layout().count():
                child = self.preview_widget.layout().takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
        else:
            layout = QHBoxLayout(self.preview_widget)
            layout.setContentsMargins(5, 5, 5, 5)

        if not self.enabled_checkbox.isChecked():
            label = QLabel("Banner deshabilitado")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("color: #7F8C8D; font-style: italic;")
            self.preview_widget.layout().addWidget(label)
            return

        image_path = self.image_path_edit.text().strip()
        alt_text = self.alt_text_edit.text().strip()

        if image_path and os.path.exists(image_path):
            # Mostrar imagen escalada a la altura configurada
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # Escalar imagen según la altura configurada
                preview_height = banner_height - 10  # Dejar margen
                scaled_pixmap = pixmap.scaled(200, preview_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                image_label = QLabel()
                image_label.setPixmap(scaled_pixmap)
                image_label.setAlignment(Qt.AlignCenter)
                self.preview_widget.layout().addWidget(image_label)
            else:
                # Imagen inválida, mostrar texto
                text_label = QLabel(alt_text or "Imagen inválida")
                text_label.setAlignment(Qt.AlignCenter)
                self.preview_widget.layout().addWidget(text_label)
        else:
            # Mostrar texto alternativo o información de tamaño
            if alt_text:
                text_label = QLabel(alt_text)
                text_label.setAlignment(Qt.AlignCenter)
                self.preview_widget.layout().addWidget(text_label)
            else:
                # Mostrar información de tamaño recomendado
                size_text = f"📐 Tamaño recomendado: 600x{banner_height} px"
                text_label = QLabel(size_text)
                text_label.setAlignment(Qt.AlignCenter)
                text_label.setStyleSheet("color: #7F8C8D; font-style: italic;")
                self.preview_widget.layout().addWidget(text_label)

    def load_current_config(self):
        """Cargar configuración actual"""
        config = self.data_manager.get_config('banner_config', {})

        self.enabled_checkbox.setChecked(config.get('enabled', False))
        self.image_path_edit.setText(config.get('image_path', ''))
        self.url_edit.setText(config.get('url', ''))
        self.alt_text_edit.setText(config.get('alt_text', ''))
        self.height_spinbox.setValue(config.get('height', 67))

        self.update_preview()

    def get_config(self):
        """Obtener configuración del diálogo"""
        return {
            'enabled': self.enabled_checkbox.isChecked(),
            'image_path': self.image_path_edit.text().strip(),
            'url': self.url_edit.text().strip(),
            'alt_text': self.alt_text_edit.text().strip(),
            'height': self.height_spinbox.value()
        }


class MasterPasswordSetupDialog(QDialog):
    """Diálogo para configurar la contraseña maestra"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configurar Contraseña Maestra")
        self.setModal(True)
        self.setFixedSize(400, 250)

        layout = QVBoxLayout(self)

        # Título
        title = QLabel("Configuración de Contraseña Maestra")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 10px;")
        layout.addWidget(title)

        # Mensaje
        message = QLabel("Establezca una contraseña maestra para proteger la configuración del banner:")
        message.setWordWrap(True)
        message.setStyleSheet("color: #34495E; margin: 10px;")
        layout.addWidget(message)

        # Formulario
        form_layout = QFormLayout()

        # Nueva contraseña
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("Mínimo 6 caracteres...")
        form_layout.addRow("Nueva contraseña:", self.password_edit)

        # Confirmar contraseña
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setPlaceholderText("Repetir contraseña...")
        form_layout.addRow("Confirmar:", self.confirm_password_edit)

        layout.addLayout(form_layout)

        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.validate_and_accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Foco en el primer campo
        self.password_edit.setFocus()

    def validate_and_accept(self):
        """Validar contraseñas antes de aceptar"""
        password = self.password_edit.text()
        confirm = self.confirm_password_edit.text()

        if len(password) < 6:
            QMessageBox.warning(self, "Error", "La contraseña debe tener al menos 6 caracteres.")
            return

        if password != confirm:
            QMessageBox.warning(self, "Error", "Las contraseñas no coinciden.")
            return

        self.accept()

    def get_password(self):
        """Obtener la contraseña configurada"""
        return self.password_edit.text()


class PasswordDialog(QDialog):
    """Diálogo para solicitar contraseña con pestañas para configurar/cambiar"""

    def __init__(self, is_first_time=False, allow_change=True, parent=None):
        super().__init__(parent)
        self.is_first_time = is_first_time
        self.allow_change = allow_change
        self.new_password = None

        if is_first_time:
            self.setWindowTitle("Configurar Contraseña Maestra")
            self.setFixedSize(400, 300)
        else:
            self.setWindowTitle("Gestión de Contraseña Maestra")
            self.setFixedSize(450, 400)

        self.setModal(True)
        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)

        if self.is_first_time:
            # Primera vez - solo configuración
            self.setup_first_time_ui(layout)
        else:
            # Pestañas para acceso y cambio
            self.setup_tabbed_ui(layout)

    def setup_first_time_ui(self, layout):
        """Configurar UI para primera vez"""
        # Título
        title = QLabel("Configurar Contraseña Maestra")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2980B9; margin: 10px;")
        layout.addWidget(title)

        # Mensaje
        message = QLabel("Es la primera vez que configura el banner.\nEstablezca una contraseña maestra:")
        message.setWordWrap(True)
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("color: #2C3E50; margin: 10px;")
        layout.addWidget(message)

        # Formulario
        form_layout = QFormLayout()

        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("Mínimo 6 caracteres...")
        form_layout.addRow("Nueva contraseña:", self.password_edit)

        self.confirm_edit = QLineEdit()
        self.confirm_edit.setEchoMode(QLineEdit.Password)
        self.confirm_edit.setPlaceholderText("Repetir contraseña...")
        form_layout.addRow("Confirmar:", self.confirm_edit)

        layout.addLayout(form_layout)

        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.validate_first_time)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.password_edit.setFocus()

    def setup_tabbed_ui(self, layout):
        """Configurar UI con pestañas"""
        # Título
        title = QLabel("Gestión de Contraseña Maestra")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 10px;")
        layout.addWidget(title)

        # Pestañas
        from PyQt5.QtWidgets import QTabWidget
        self.tab_widget = QTabWidget()

        # Pestaña 1: Acceso
        access_tab = QWidget()
        self.setup_access_tab(access_tab)
        self.tab_widget.addTab(access_tab, "🔑 Acceso")

        # Pestaña 2: Cambiar contraseña
        change_tab = QWidget()
        self.setup_change_tab(change_tab)
        self.tab_widget.addTab(change_tab, "🔄 Cambiar Contraseña")

        layout.addWidget(self.tab_widget)

        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.validate_tabbed)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def setup_access_tab(self, tab):
        """Configurar pestaña de acceso"""
        layout = QVBoxLayout(tab)

        # Mensaje
        message = QLabel("Ingrese la contraseña maestra para acceder a la configuración del banner:")
        message.setWordWrap(True)
        message.setStyleSheet("color: #2C3E50; margin: 10px;")
        layout.addWidget(message)

        # Campo de contraseña
        self.access_password_edit = QLineEdit()
        self.access_password_edit.setEchoMode(QLineEdit.Password)
        self.access_password_edit.setPlaceholderText("Contraseña maestra actual...")
        layout.addWidget(self.access_password_edit)

        layout.addStretch()

    def setup_change_tab(self, tab):
        """Configurar pestaña de cambio de contraseña"""
        layout = QVBoxLayout(tab)

        # Mensaje
        message = QLabel("Cambiar la contraseña maestra afectará la encriptación de todos los archivos:")
        message.setWordWrap(True)
        message.setStyleSheet("color: #E67E22; font-weight: bold; margin: 10px;")
        layout.addWidget(message)

        # Advertencia
        warning = QLabel("⚠️ ADVERTENCIA: Los archivos encriptados se re-encriptarán con la nueva contraseña.")
        warning.setWordWrap(True)
        warning.setStyleSheet("color: #E74C3C; font-style: italic; margin: 5px;")
        layout.addWidget(warning)

        # Formulario
        form_layout = QFormLayout()

        # Contraseña actual
        self.current_password_edit = QLineEdit()
        self.current_password_edit.setEchoMode(QLineEdit.Password)
        self.current_password_edit.setPlaceholderText("Contraseña actual...")
        form_layout.addRow("Contraseña actual:", self.current_password_edit)

        # Nueva contraseña
        self.new_password_edit = QLineEdit()
        self.new_password_edit.setEchoMode(QLineEdit.Password)
        self.new_password_edit.setPlaceholderText("Mínimo 6 caracteres...")
        form_layout.addRow("Nueva contraseña:", self.new_password_edit)

        # Verificar nueva contraseña
        self.verify_password_edit = QLineEdit()
        self.verify_password_edit.setEchoMode(QLineEdit.Password)
        self.verify_password_edit.setPlaceholderText("Repetir nueva contraseña...")
        form_layout.addRow("Verificar nueva:", self.verify_password_edit)

        layout.addLayout(form_layout)
        layout.addStretch()

    def validate_first_time(self):
        """Validar configuración de primera vez"""
        password = self.password_edit.text()
        confirm = self.confirm_edit.text()

        if len(password) < 6:
            QMessageBox.warning(self, "Error", "La contraseña debe tener al menos 6 caracteres.")
            return

        if password != confirm:
            QMessageBox.warning(self, "Error", "Las contraseñas no coinciden.")
            return

        self.accept()

    def validate_tabbed(self):
        """Validar según la pestaña activa"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # Pestaña de acceso
            if not self.access_password_edit.text():
                QMessageBox.warning(self, "Error", "Debe ingresar la contraseña maestra.")
                return
            self.accept()

        elif current_tab == 1:  # Pestaña de cambio
            current = self.current_password_edit.text()
            new = self.new_password_edit.text()
            verify = self.verify_password_edit.text()

            if not current:
                QMessageBox.warning(self, "Error", "Debe ingresar la contraseña actual.")
                return

            if len(new) < 6:
                QMessageBox.warning(self, "Error", "La nueva contraseña debe tener al menos 6 caracteres.")
                return

            if new != verify:
                QMessageBox.warning(self, "Error", "Las nuevas contraseñas no coinciden.")
                return

            # Confirmar cambio
            reply = QMessageBox.question(
                self,
                "Confirmar Cambio",
                "¿Está seguro de cambiar la contraseña maestra?\n\n"
                "⚠️ Esto re-encriptará todos los archivos con la nueva contraseña.",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.new_password = new
                self.accept()

    def get_password(self):
        """Obtener la contraseña ingresada"""
        if self.is_first_time:
            return self.password_edit.text()
        else:
            current_tab = self.tab_widget.currentIndex()
            if current_tab == 0:  # Acceso
                return self.access_password_edit.text()
            else:  # Cambio
                return self.current_password_edit.text()

    def get_new_password(self):
        """Obtener la nueva contraseña (solo para cambio)"""
        return self.new_password

    def is_password_change(self):
        """Verificar si se está cambiando la contraseña"""
        return not self.is_first_time and self.tab_widget.currentIndex() == 1 and self.new_password is not None


class BannerWidget(QWidget):
    """Widget del banner configurable"""

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager

        self.setup_ui()
        self.load_banner_config()

    def setup_ui(self):
        """Configurar la interfaz del banner"""
        # Usar layout absoluto para superponer el botón
        self.setLayout(None)  # Sin layout automático

        # Altura configurable del banner (se actualizará al cargar configuración)
        self.banner_height = 67  # Altura por defecto
        self.setFixedHeight(self.banner_height)

        self.update_banner_style()

        # Widget de contenido del banner (ocupa todo el espacio)
        self.content_widget = QWidget(self)
        self.content_layout = QHBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(10, 10, 10, 10)

        # Botón de configuración superpuesto en la esquina superior derecha
        self.config_button = QPushButton("⚙", self)
        self.config_button.setFixedSize(24, 24)
        self.config_button.setToolTip("Configurar banner (clic derecho para más opciones)")
        self.config_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(236, 240, 241, 200);
                border: 1px solid #BDC3C7;
                border-radius: 12px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(213, 219, 219, 220);
                border: 2px solid #3498DB;
            }
            QPushButton:pressed {
                background-color: rgba(189, 195, 199, 240);
            }
        """)
        self.config_button.clicked.connect(self.configure_banner)

        # Configurar cursor para indicar que es clickeable
        self.setCursor(QCursor(Qt.PointingHandCursor))

        # Configurar menú contextual
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

        # Inicializar gestor de encriptación si está disponible
        if ENCRYPTION_AVAILABLE:
            self.encryption_manager = EncryptedFileManager(self.data_manager)
            self.protected_storage = ProtectedStorage(self.data_manager)
        else:
            self.encryption_manager = None
            self.protected_storage = None

    def update_banner_style(self):
        """Actualizar el estilo del banner con la altura actual"""
        self.setStyleSheet(f"""
            BannerWidget {{
                background-color: #FFFFFF;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                min-height: {self.banner_height}px;
                max-height: {self.banner_height}px;
            }}
            BannerWidget:hover {{
                border: 2px solid #3498DB;
                background-color: #F8F9FA;
            }}
        """)

    def banner_height_changed(self):
        """Notificar cambio de altura del banner al contenedor padre"""
        # Buscar el widget padre que contenga los relojes y ajustar proporciones
        parent = self.parent()
        while parent:
            if hasattr(parent, 'adjust_layout_for_banner_height'):
                parent.adjust_layout_for_banner_height(self.banner_height)
                break
            parent = parent.parent()

    def load_banner_config(self):
        """Cargar y aplicar configuración del banner"""
        config = self.data_manager.get_config('banner_config', {})

        # Actualizar altura del banner
        self.banner_height = config.get('height', 67)
        self.setFixedHeight(self.banner_height)
        self.update_banner_style()

        # Emitir señal para que el contenedor ajuste los espacios
        self.banner_height_changed()

        # Limpiar contenido anterior
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not config.get('enabled', False):
            # Banner deshabilitado - mostrar información de tamaño
            self.show_size_info()
            self.url = None
            return

        # Banner habilitado
        image_path = config.get('image_path', '')
        self.url = config.get('url', '')
        alt_text = config.get('alt_text', '')

        if image_path:
            # Intentar cargar imagen desde almacenamiento protegido
            actual_image_path = None

            if self.protected_storage:
                # Obtener ruta real de la imagen (desencriptada si es necesario)
                actual_image_path = self.protected_storage.get_image_path(os.path.basename(image_path))

            # Si no se pudo obtener desde almacenamiento protegido, usar ruta original
            if not actual_image_path and os.path.exists(image_path):
                actual_image_path = image_path

            if actual_image_path and os.path.exists(actual_image_path):
                # Mostrar imagen
                pixmap = QPixmap(actual_image_path)
                if not pixmap.isNull():
                    # Escalar imagen para que quepa en el banner (ocupar todo el ancho disponible)
                    available_width = self.width() - 20  # Margen para el contenido
                    available_height = self.banner_height - 20  # Margen vertical

                    scaled_pixmap = pixmap.scaled(
                        available_width, available_height,
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )

                    image_label = QLabel()
                    image_label.setPixmap(scaled_pixmap)
                    image_label.setAlignment(Qt.AlignCenter)
                    self.content_layout.addWidget(image_label)
                else:
                    # Imagen inválida, mostrar texto alternativo
                    text_label = QLabel(alt_text or "Imagen no válida")
                    text_label.setAlignment(Qt.AlignCenter)
                    text_label.setStyleSheet("color: #2C3E50; font-weight: bold; font-size: 12pt;")
                    self.content_layout.addWidget(text_label)
            else:
                # Imagen no encontrada, mostrar texto alternativo
                text_label = QLabel(alt_text or "Imagen no encontrada")
                text_label.setAlignment(Qt.AlignCenter)
                text_label.setStyleSheet("color: #E74C3C; font-weight: bold; font-size: 12pt;")
                self.content_layout.addWidget(text_label)
        else:
            # Mostrar texto alternativo o información de tamaño si no hay texto
            if alt_text:
                text_label = QLabel(alt_text)
                text_label.setAlignment(Qt.AlignCenter)
                text_label.setStyleSheet("color: #2C3E50; font-weight: bold; font-size: 12pt;")
                self.content_layout.addWidget(text_label)
            else:
                self.show_size_info()

    def show_size_info(self):
        """Mostrar información sobre el tamaño de imagen admitido"""
        # Calcular tamaño disponible para la imagen
        available_width = self.width() - 100  # Reservar espacio para botón config
        available_height = self.height() - 16  # Reservar espacio para márgenes

        # Calcular DPI aproximado (asumiendo 96 DPI estándar)
        dpi = 96
        width_inches = available_width / dpi
        height_inches = available_height / dpi

        info_text = f"""
        📐 ESPACIO DISPONIBLE PARA IMAGEN:

        • Tamaño en píxeles: {available_width} x {available_height} px
        • Tamaño en pulgadas: {width_inches:.1f}" x {height_inches:.1f}"
        • DPI recomendado: 96-300 DPI
        • Formatos: PNG, JPG, JPEG, GIF, BMP

        🔧 Haga clic en ⚙ para configurar
        """

        info_label = QLabel(info_text.strip())
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            color: #7F8C8D;
            font-size: 9pt;
            font-style: italic;
            background-color: #F8F9FA;
            padding: 10px;
            border-radius: 3px;
            border: 1px dashed #BDC3C7;
        """)
        info_label.setWordWrap(True)
        self.content_layout.addWidget(info_label)

    def mousePressEvent(self, event):
        """Manejar clic en el banner"""
        if event.button() == Qt.LeftButton and self.url:
            # Abrir URL en el navegador
            try:
                webbrowser.open(self.url)
            except Exception as e:
                QMessageBox.warning(self, "Error", f"No se pudo abrir la URL: {str(e)}")

        super().mousePressEvent(event)

    def configure_banner(self):
        """Configurar el banner (requiere contraseña maestra)"""
        # Verificar si existe contraseña maestra
        master_password = self.data_manager.get_config('banner_master_password', None)

        if master_password is None:
            # Primera vez - configurar contraseña maestra
            password_dialog = PasswordDialog(is_first_time=True, parent=self)
            if password_dialog.exec_() != QDialog.Accepted:
                return

            new_password = password_dialog.get_password()

            # Guardar contraseña maestra (en producción debería estar encriptada)
            self.data_manager.set_config('banner_master_password', new_password)

            QMessageBox.information(
                self,
                "Contraseña Configurada",
                "Contraseña maestra configurada correctamente.\nRecuerde esta contraseña para futuras configuraciones."
            )
        else:
            # Solicitar contraseña maestra existente
            password_dialog = PasswordDialog(is_first_time=False, parent=self)
            if password_dialog.exec_() != QDialog.Accepted:
                return

            entered_password = password_dialog.get_password()
            if entered_password != master_password:
                QMessageBox.warning(self, "Acceso Denegado", "Contraseña maestra incorrecta.")
                return

        # Mostrar diálogo de configuración
        config_dialog = BannerConfigDialog(self.data_manager, self)
        if config_dialog.exec_() == QDialog.Accepted:
            # Guardar configuración
            config = config_dialog.get_config()
            self.data_manager.set_config('banner_config', config)

            # Recargar banner
            self.load_banner_config()

            QMessageBox.information(self, "Configuración Guardada", "El banner ha sido configurado correctamente.")

    def reset_master_password(self):
        """Resetear la contraseña maestra (método para administradores)"""
        reply = QMessageBox.question(
            self,
            "Resetear Contraseña Maestra",
            "¿Está seguro de que desea resetear la contraseña maestra?\nEsto permitirá configurar una nueva contraseña.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.data_manager.set_config('banner_master_password', None)
            QMessageBox.information(self, "Contraseña Reseteada", "La contraseña maestra ha sido eliminada.\nLa próxima configuración requerirá establecer una nueva contraseña.")

    def show_context_menu(self, position):
        """Mostrar menú contextual con opciones de encriptación"""
        from PyQt5.QtWidgets import QMenu, QAction

        menu = QMenu(self)

        # Acción de configuración
        config_action = QAction("⚙️ Configurar Banner", self)
        config_action.triggered.connect(self.configure_banner)
        menu.addAction(config_action)

        if ENCRYPTION_AVAILABLE and self.encryption_manager:
            menu.addSeparator()

            # Acciones de encriptación
            encrypt_action = QAction("🔒 Encriptar Archivos del Programa", self)
            encrypt_action.triggered.connect(self.encrypt_program_files)
            menu.addAction(encrypt_action)

            decrypt_action = QAction("🔓 Desencriptar Archivos del Programa", self)
            decrypt_action.triggered.connect(self.decrypt_program_files)
            menu.addAction(decrypt_action)

            status_action = QAction("📊 Estado de Encriptación", self)
            status_action.triggered.connect(self.show_encryption_status)
            menu.addAction(status_action)

        menu.exec_(self.mapToGlobal(position))

    def encrypt_program_files(self):
        """Encriptar todos los archivos del programa"""
        if not self.encryption_manager:
            QMessageBox.warning(self, "Error", "Sistema de encriptación no disponible.")
            return

        # Verificar que hay contraseña maestra
        master_password = self.encryption_manager.get_master_password()
        if not master_password:
            QMessageBox.warning(
                self,
                "Contraseña Requerida",
                "Debe configurar una contraseña maestra primero.\nUse 'Configurar Banner' para establecerla."
            )
            return

        # Confirmar acción
        reply = QMessageBox.question(
            self,
            "Confirmar Encriptación",
            "¿Está seguro de que desea encriptar todos los archivos del programa?\n\n"
            "⚠️ ADVERTENCIA: Los archivos solo podrán abrirse con la contraseña maestra.\n"
            "Asegúrese de recordar la contraseña.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # Encriptar archivos
        results = self.encryption_manager.encrypt_program_files()

        # Mostrar resultados
        message = "🔒 ENCRIPTACIÓN COMPLETADA\n\n"

        if 'error' in results:
            message += f"❌ Error: {results['error']}\n"
        else:
            message += f"✅ Archivos encriptados: {len(results['encrypted'])}\n"
            message += f"❌ Fallos: {len(results['failed'])}\n"
            message += f"⏭️ Omitidos: {len(results['skipped'])}\n\n"

            if results['encrypted']:
                message += f"Encriptados: {', '.join(results['encrypted'])}\n"

            if results['failed']:
                message += f"Fallos: {', '.join(results['failed'])}\n"

        QMessageBox.information(self, "Resultado de Encriptación", message)

    def decrypt_program_files(self):
        """Desencriptar todos los archivos del programa"""
        if not self.encryption_manager:
            QMessageBox.warning(self, "Error", "Sistema de encriptación no disponible.")
            return

        # Solicitar contraseña maestra
        password_dialog = PasswordDialog(is_first_time=False, parent=self)
        password_dialog.setWindowTitle("Desencriptar Archivos")

        if password_dialog.exec_() != QDialog.Accepted:
            return

        password = password_dialog.get_password()

        # Verificar contraseña
        if not self.encryption_manager.verify_master_password(password):
            QMessageBox.warning(self, "Error", "Contraseña maestra incorrecta.")
            return

        # Confirmar acción
        reply = QMessageBox.question(
            self,
            "Confirmar Desencriptación",
            "¿Está seguro de que desea desencriptar todos los archivos del programa?",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # Desencriptar archivos
        results = self.encryption_manager.decrypt_program_files(password)

        # Mostrar resultados
        message = "🔓 DESENCRIPTACIÓN COMPLETADA\n\n"
        message += f"✅ Archivos desencriptados: {len(results['decrypted'])}\n"
        message += f"❌ Fallos: {len(results['failed'])}\n"
        message += f"⏭️ Omitidos: {len(results['skipped'])}\n\n"

        if results['decrypted']:
            message += f"Desencriptados: {', '.join(results['decrypted'])}\n"

        if results['failed']:
            message += f"Fallos: {', '.join(results['failed'])}\n"

        QMessageBox.information(self, "Resultado de Desencriptación", message)

    def show_encryption_status(self):
        """Mostrar estado actual de encriptación"""
        if not self.encryption_manager:
            QMessageBox.warning(self, "Error", "Sistema de encriptación no disponible.")
            return

        status = self.encryption_manager.get_encryption_status()

        message = "📊 ESTADO DE ENCRIPTACIÓN\n\n"
        message += f"📁 Total de archivos: {status['total_files']}\n"
        message += f"🔒 Encriptados: {len(status['encrypted_files'])}\n"
        message += f"🔓 Sin encriptar: {len(status['unencrypted_files'])}\n\n"

        if status['encrypted_files']:
            message += f"Archivos encriptados:\n"
            for file in status['encrypted_files']:
                message += f"  • {file}\n"
            message += "\n"

        if status['unencrypted_files']:
            message += f"Archivos sin encriptar:\n"
            for file in status['unencrypted_files']:
                message += f"  • {file}\n"

        QMessageBox.information(self, "Estado de Encriptación", message)

    def resizeEvent(self, event):
        """Manejar redimensionamiento del widget"""
        super().resizeEvent(event)

        # Posicionar el widget de contenido para que ocupe todo el espacio
        if hasattr(self, 'content_widget'):
            self.content_widget.setGeometry(0, 0, self.width(), self.height())

        # Posicionar el botón de configuración en la esquina superior derecha
        if hasattr(self, 'config_button'):
            button_margin = 5
            self.config_button.move(
                self.width() - self.config_button.width() - button_margin,
                button_margin
            )

        # Recargar banner para ajustar imagen al nuevo tamaño
        self.load_banner_config()

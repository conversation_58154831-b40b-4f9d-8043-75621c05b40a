#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget de banner configurable con protección por contraseña
"""

import os
import webbrowser
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QLineEdit, QFileDialog, QMessageBox,
                            QDialog, QDialogButtonBox, QFormLayout, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QFont, QCursor


class BannerConfigDialog(QDialog):
    """Diálogo para configurar el banner"""

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setWindowTitle("Configurar Banner")
        self.setModal(True)
        self.setFixedSize(500, 400)

        self.setup_ui()
        self.load_current_config()

    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)

        # Título
        title = QLabel("Configuración del Banner")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 10px;")
        layout.addWidget(title)

        # Formulario
        form_layout = QFormLayout()

        # Habilitar banner
        self.enabled_checkbox = QCheckBox("Habilitar banner")
        form_layout.addRow("Estado:", self.enabled_checkbox)

        # Imagen
        image_layout = QHBoxLayout()
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setPlaceholderText("Ruta de la imagen...")
        self.browse_button = QPushButton("Examinar")
        self.browse_button.clicked.connect(self.browse_image)
        image_layout.addWidget(self.image_path_edit)
        image_layout.addWidget(self.browse_button)
        form_layout.addRow("Imagen:", image_layout)

        # URL
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("https://ejemplo.com")
        form_layout.addRow("URL:", self.url_edit)

        # Texto alternativo
        self.alt_text_edit = QLineEdit()
        self.alt_text_edit.setPlaceholderText("Texto cuando no hay imagen")
        form_layout.addRow("Texto alternativo:", self.alt_text_edit)

        layout.addLayout(form_layout)

        # Información de tamaño recomendado
        size_info = QLabel("📐 Tamaño recomendado: 600x80 píxeles (o proporcional) | DPI: 96-300")
        size_info.setStyleSheet("color: #7F8C8D; font-size: 9pt; font-style: italic; margin: 5px;")
        size_info.setWordWrap(True)
        layout.addWidget(size_info)

        # Vista previa
        preview_label = QLabel("Vista previa:")
        preview_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(preview_label)

        self.preview_widget = QWidget()
        self.preview_widget.setFixedHeight(80)  # Altura más grande
        self.preview_widget.setStyleSheet("border: 2px solid #BDC3C7; background-color: #F8F9FA; border-radius: 3px;")
        layout.addWidget(self.preview_widget)

        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Conectar señales para vista previa
        self.image_path_edit.textChanged.connect(self.update_preview)
        self.alt_text_edit.textChanged.connect(self.update_preview)
        self.enabled_checkbox.stateChanged.connect(self.update_preview)

    def browse_image(self):
        """Examinar archivo de imagen"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar imagen",
            "",
            "Imágenes (*.png *.jpg *.jpeg *.gif *.bmp);;Todos los archivos (*)"
        )

        if file_path:
            self.image_path_edit.setText(file_path)

    def update_preview(self):
        """Actualizar vista previa del banner"""
        # Limpiar layout anterior
        if self.preview_widget.layout():
            while self.preview_widget.layout().count():
                child = self.preview_widget.layout().takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
        else:
            layout = QHBoxLayout(self.preview_widget)
            layout.setContentsMargins(5, 5, 5, 5)

        if not self.enabled_checkbox.isChecked():
            label = QLabel("Banner deshabilitado")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("color: #7F8C8D; font-style: italic;")
            self.preview_widget.layout().addWidget(label)
            return

        image_path = self.image_path_edit.text().strip()
        alt_text = self.alt_text_edit.text().strip()

        if image_path and os.path.exists(image_path):
            # Mostrar imagen
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(100, 50, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                image_label = QLabel()
                image_label.setPixmap(scaled_pixmap)
                image_label.setAlignment(Qt.AlignCenter)
                self.preview_widget.layout().addWidget(image_label)
            else:
                # Imagen inválida, mostrar texto
                text_label = QLabel(alt_text or "Imagen inválida")
                text_label.setAlignment(Qt.AlignCenter)
                self.preview_widget.layout().addWidget(text_label)
        else:
            # Mostrar texto alternativo
            text_label = QLabel(alt_text or "Sin imagen")
            text_label.setAlignment(Qt.AlignCenter)
            self.preview_widget.layout().addWidget(text_label)

    def load_current_config(self):
        """Cargar configuración actual"""
        config = self.data_manager.get_config('banner_config', {})

        self.enabled_checkbox.setChecked(config.get('enabled', False))
        self.image_path_edit.setText(config.get('image_path', ''))
        self.url_edit.setText(config.get('url', ''))
        self.alt_text_edit.setText(config.get('alt_text', ''))

        self.update_preview()

    def get_config(self):
        """Obtener configuración del diálogo"""
        return {
            'enabled': self.enabled_checkbox.isChecked(),
            'image_path': self.image_path_edit.text().strip(),
            'url': self.url_edit.text().strip(),
            'alt_text': self.alt_text_edit.text().strip()
        }


class MasterPasswordSetupDialog(QDialog):
    """Diálogo para configurar la contraseña maestra"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configurar Contraseña Maestra")
        self.setModal(True)
        self.setFixedSize(400, 250)

        layout = QVBoxLayout(self)

        # Título
        title = QLabel("Configuración de Contraseña Maestra")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 10px;")
        layout.addWidget(title)

        # Mensaje
        message = QLabel("Establezca una contraseña maestra para proteger la configuración del banner:")
        message.setWordWrap(True)
        message.setStyleSheet("color: #34495E; margin: 10px;")
        layout.addWidget(message)

        # Formulario
        form_layout = QFormLayout()

        # Nueva contraseña
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("Mínimo 6 caracteres...")
        form_layout.addRow("Nueva contraseña:", self.password_edit)

        # Confirmar contraseña
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setPlaceholderText("Repetir contraseña...")
        form_layout.addRow("Confirmar:", self.confirm_password_edit)

        layout.addLayout(form_layout)

        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.validate_and_accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Foco en el primer campo
        self.password_edit.setFocus()

    def validate_and_accept(self):
        """Validar contraseñas antes de aceptar"""
        password = self.password_edit.text()
        confirm = self.confirm_password_edit.text()

        if len(password) < 6:
            QMessageBox.warning(self, "Error", "La contraseña debe tener al menos 6 caracteres.")
            return

        if password != confirm:
            QMessageBox.warning(self, "Error", "Las contraseñas no coinciden.")
            return

        self.accept()

    def get_password(self):
        """Obtener la contraseña configurada"""
        return self.password_edit.text()


class PasswordDialog(QDialog):
    """Diálogo para solicitar contraseña"""

    def __init__(self, is_first_time=False, parent=None):
        super().__init__(parent)
        self.is_first_time = is_first_time

        if is_first_time:
            self.setWindowTitle("Configurar Contraseña Maestra")
        else:
            self.setWindowTitle("Contraseña Requerida")

        self.setModal(True)
        self.setFixedSize(350, 200)

        layout = QVBoxLayout(self)

        # Mensaje
        if is_first_time:
            message = QLabel("Es la primera vez que configura el banner.\nEstablezca una contraseña maestra:")
            message.setStyleSheet("color: #2980B9; font-weight: bold;")
        else:
            message = QLabel("Ingrese la contraseña maestra para configurar el banner:")
            message.setStyleSheet("color: #2C3E50;")

        message.setWordWrap(True)
        message.setAlignment(Qt.AlignCenter)
        layout.addWidget(message)

        # Campo de contraseña
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)

        if is_first_time:
            self.password_edit.setPlaceholderText("Nueva contraseña maestra (mín. 6 caracteres)...")
        else:
            self.password_edit.setPlaceholderText("Contraseña maestra...")

        layout.addWidget(self.password_edit)

        # Campo de confirmación (solo para primera vez)
        if is_first_time:
            self.confirm_edit = QLineEdit()
            self.confirm_edit.setEchoMode(QLineEdit.Password)
            self.confirm_edit.setPlaceholderText("Confirmar contraseña...")
            layout.addWidget(self.confirm_edit)

        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)

        if is_first_time:
            button_box.accepted.connect(self.validate_first_time)
        else:
            button_box.accepted.connect(self.accept)

        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Foco en el campo de contraseña
        self.password_edit.setFocus()

    def validate_first_time(self):
        """Validar configuración de primera vez"""
        password = self.password_edit.text()
        confirm = self.confirm_edit.text()

        if len(password) < 6:
            QMessageBox.warning(self, "Error", "La contraseña debe tener al menos 6 caracteres.")
            return

        if password != confirm:
            QMessageBox.warning(self, "Error", "Las contraseñas no coinciden.")
            return

        self.accept()

    def get_password(self):
        """Obtener la contraseña ingresada"""
        return self.password_edit.text()


class BannerWidget(QWidget):
    """Widget del banner configurable"""

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager

        self.setup_ui()
        self.load_banner_config()

    def setup_ui(self):
        """Configurar la interfaz del banner"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(8, 8, 8, 8)

        # Altura más grande - igual a un cuadrado de día del calendario (aproximadamente 80px)
        self.setFixedHeight(80)
        self.setStyleSheet("""
            BannerWidget {
                background-color: #FFFFFF;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
            }
            BannerWidget:hover {
                border: 2px solid #3498DB;
                background-color: #F8F9FA;
            }
        """)

        # Widget de contenido del banner
        self.content_widget = QWidget()
        self.content_layout = QHBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)

        self.layout.addWidget(self.content_widget)

        # Botón de configuración (pequeño, en la esquina)
        self.config_button = QPushButton("⚙")
        self.config_button.setFixedSize(20, 20)
        self.config_button.setToolTip("Configurar banner")
        self.config_button.setStyleSheet("""
            QPushButton {
                background-color: #ECF0F1;
                border: 1px solid #BDC3C7;
                border-radius: 10px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #D5DBDB;
            }
        """)
        self.config_button.clicked.connect(self.configure_banner)

        # Posicionar botón en la esquina superior derecha
        self.layout.addWidget(self.config_button, 0, Qt.AlignTop | Qt.AlignRight)

        # Configurar cursor para indicar que es clickeable
        self.setCursor(QCursor(Qt.PointingHandCursor))

    def load_banner_config(self):
        """Cargar y aplicar configuración del banner"""
        config = self.data_manager.get_config('banner_config', {})

        # Limpiar contenido anterior
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not config.get('enabled', False):
            # Banner deshabilitado - mostrar información de tamaño
            self.show_size_info()
            self.url = None
            return

        # Banner habilitado
        image_path = config.get('image_path', '')
        self.url = config.get('url', '')
        alt_text = config.get('alt_text', '')

        if image_path and os.path.exists(image_path):
            # Mostrar imagen
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # Escalar imagen para que quepa en el banner
                scaled_pixmap = pixmap.scaled(
                    self.width() - 50, 50,
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )

                image_label = QLabel()
                image_label.setPixmap(scaled_pixmap)
                image_label.setAlignment(Qt.AlignCenter)
                self.content_layout.addWidget(image_label)
            else:
                # Imagen inválida, mostrar texto alternativo
                text_label = QLabel(alt_text or "Imagen no válida")
                text_label.setAlignment(Qt.AlignCenter)
                text_label.setStyleSheet("color: #2C3E50; font-weight: bold;")
                self.content_layout.addWidget(text_label)
        else:
            # Mostrar texto alternativo o información de tamaño si no hay texto
            if alt_text:
                text_label = QLabel(alt_text)
                text_label.setAlignment(Qt.AlignCenter)
                text_label.setStyleSheet("color: #2C3E50; font-weight: bold; font-size: 12pt;")
                self.content_layout.addWidget(text_label)
            else:
                self.show_size_info()

    def show_size_info(self):
        """Mostrar información sobre el tamaño de imagen admitido"""
        # Calcular tamaño disponible para la imagen
        available_width = self.width() - 100  # Reservar espacio para botón config
        available_height = self.height() - 16  # Reservar espacio para márgenes

        # Calcular DPI aproximado (asumiendo 96 DPI estándar)
        dpi = 96
        width_inches = available_width / dpi
        height_inches = available_height / dpi

        info_text = f"""
        📐 ESPACIO DISPONIBLE PARA IMAGEN:

        • Tamaño en píxeles: {available_width} x {available_height} px
        • Tamaño en pulgadas: {width_inches:.1f}" x {height_inches:.1f}"
        • DPI recomendado: 96-300 DPI
        • Formatos: PNG, JPG, JPEG, GIF, BMP

        🔧 Haga clic en ⚙ para configurar
        """

        info_label = QLabel(info_text.strip())
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            color: #7F8C8D;
            font-size: 9pt;
            font-style: italic;
            background-color: #F8F9FA;
            padding: 10px;
            border-radius: 3px;
            border: 1px dashed #BDC3C7;
        """)
        info_label.setWordWrap(True)
        self.content_layout.addWidget(info_label)

    def mousePressEvent(self, event):
        """Manejar clic en el banner"""
        if event.button() == Qt.LeftButton and self.url:
            # Abrir URL en el navegador
            try:
                webbrowser.open(self.url)
            except Exception as e:
                QMessageBox.warning(self, "Error", f"No se pudo abrir la URL: {str(e)}")

        super().mousePressEvent(event)

    def configure_banner(self):
        """Configurar el banner (requiere contraseña maestra)"""
        # Verificar si existe contraseña maestra
        master_password = self.data_manager.get_config('banner_master_password', None)

        if master_password is None:
            # Primera vez - configurar contraseña maestra
            password_dialog = PasswordDialog(is_first_time=True, parent=self)
            if password_dialog.exec_() != QDialog.Accepted:
                return

            new_password = password_dialog.get_password()

            # Guardar contraseña maestra (en producción debería estar encriptada)
            self.data_manager.set_config('banner_master_password', new_password)

            QMessageBox.information(
                self,
                "Contraseña Configurada",
                "Contraseña maestra configurada correctamente.\nRecuerde esta contraseña para futuras configuraciones."
            )
        else:
            # Solicitar contraseña maestra existente
            password_dialog = PasswordDialog(is_first_time=False, parent=self)
            if password_dialog.exec_() != QDialog.Accepted:
                return

            entered_password = password_dialog.get_password()
            if entered_password != master_password:
                QMessageBox.warning(self, "Acceso Denegado", "Contraseña maestra incorrecta.")
                return

        # Mostrar diálogo de configuración
        config_dialog = BannerConfigDialog(self.data_manager, self)
        if config_dialog.exec_() == QDialog.Accepted:
            # Guardar configuración
            config = config_dialog.get_config()
            self.data_manager.set_config('banner_config', config)

            # Recargar banner
            self.load_banner_config()

            QMessageBox.information(self, "Configuración Guardada", "El banner ha sido configurado correctamente.")

    def reset_master_password(self):
        """Resetear la contraseña maestra (método para administradores)"""
        reply = QMessageBox.question(
            self,
            "Resetear Contraseña Maestra",
            "¿Está seguro de que desea resetear la contraseña maestra?\nEsto permitirá configurar una nueva contraseña.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.data_manager.set_config('banner_master_password', None)
            QMessageBox.information(self, "Contraseña Reseteada", "La contraseña maestra ha sido eliminada.\nLa próxima configuración requerirá establecer una nueva contraseña.")

    def resizeEvent(self, event):
        """Manejar redimensionamiento del widget"""
        super().resizeEvent(event)
        # Recargar banner para ajustar imagen al nuevo tamaño
        self.load_banner_config()

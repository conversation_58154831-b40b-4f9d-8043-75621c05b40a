#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget que combina cuenta atrás, banner y cronómetro en 3 zonas iguales
"""

from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QFrame)
from PyQt5.QtCore import Qt

from countdown_widget import CountdownWidget
from stopwatch_widget import StopwatchWidget
from simple_banner_widget import SimpleBannerWidget


class TripleBannerWidget(QFrame):
    """Widget que divide la zona del banner en 3 espacios iguales"""
    
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurar la interfaz del widget"""
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet("""
            TripleBannerWidget {
                background-color: transparent;
                margin: 0px;
                padding: 0px;
            }
        """)
        
        # Layout principal horizontal
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(10)
        
        # Zona 1: Cuenta atrás (izquierda)
        self.countdown_widget = CountdownWidget()
        self.countdown_widget.setMinimumWidth(200)
        main_layout.addWidget(self.countdown_widget, 1)  # Proporción 1
        
        # Zona 2: Banner (centro)
        self.banner_widget = SimpleBannerWidget(self.data_manager)
        self.banner_widget.setMinimumWidth(200)
        main_layout.addWidget(self.banner_widget, 1)  # Proporción 1
        
        # Zona 3: Cronómetro (derecha)
        self.stopwatch_widget = StopwatchWidget()
        self.stopwatch_widget.setMinimumWidth(200)
        main_layout.addWidget(self.stopwatch_widget, 1)  # Proporción 1
        
        # Conectar señales
        self.countdown_widget.countdown_finished.connect(self.on_countdown_finished)
        self.stopwatch_widget.time_updated.connect(self.on_stopwatch_updated)
    
    def on_countdown_finished(self):
        """Manejar cuando termina la cuenta atrás"""
        print("⏰ Cuenta atrás terminada!")
        # Aquí se puede agregar lógica adicional si es necesario
    
    def on_stopwatch_updated(self, elapsed_ms):
        """Manejar actualización del cronómetro"""
        # Aquí se puede agregar lógica adicional si es necesario
        pass
    
    def adjust_layout_for_banner_height(self, banner_height):
        """Ajustar el layout cuando cambia la altura del banner"""
        # Establecer la altura de todo el widget triple
        self.setFixedHeight(banner_height)
        
        # Ajustar altura de cada componente
        self.countdown_widget.setFixedHeight(banner_height - 10)  # -10 por márgenes
        self.banner_widget.setFixedHeight(banner_height - 10)
        self.stopwatch_widget.setFixedHeight(banner_height - 10)
        
        # Notificar al contenedor padre si es necesario
        parent = self.parent()
        while parent:
            if hasattr(parent, 'adjust_splitter_for_banner'):
                parent.adjust_splitter_for_banner(banner_height)
                break
            parent = parent.parent()
    
    def get_countdown_widget(self):
        """Obtener el widget de cuenta atrás"""
        return self.countdown_widget
    
    def get_banner_widget(self):
        """Obtener el widget de banner"""
        return self.banner_widget
    
    def get_stopwatch_widget(self):
        """Obtener el widget de cronómetro"""
        return self.stopwatch_widget

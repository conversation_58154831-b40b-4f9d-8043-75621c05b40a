#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
                            QLabel, QPushButton, QToolBar, QAction, QMenu, QMessageBox,
                            QTabWidget, QCheckBox, QLineEdit, QComboBox)
from PyQt5.QtCore import Qt, QDate, QDateTime, pyqtSlot, QSize
from PyQt5.QtGui import QIcon, QColor, QBrush


class ReminderTab(QWidget):
    def __init__(self, data_manager, app_signals, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.app_signals = app_signals
        
        self.init_ui()
        
        # Conectar señales
        self.app_signals.event_added.connect(self.refresh_reminders)
        self.app_signals.event_modified.connect(self.refresh_reminders)
        self.app_signals.event_deleted.connect(self.refresh_reminders)
    
    def init_ui(self):
        """Inicializar la interfaz de usuario"""
        layout = QVBoxLayout(self)
        
        # <PERSON>a de herramientas
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(20, 20))
        
        # Campo de búsqueda
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Buscar recordatorios...")
        self.search_edit.textChanged.connect(self.search_reminders)
        toolbar.addWidget(self.search_edit)
        
        # Filtro de categorías
        self.category_combo = QComboBox()
        self.category_combo.addItem("Todas las categorías", None)
        
        # Cargar categorías
        categories = self.data_manager.get_all_categories()
        for category in categories:
            self.category_combo.addItem(category['name'], category['id'])
        
        self.category_combo.currentIndexChanged.connect(self.refresh_reminders)
        toolbar.addWidget(self.category_combo)
        
        layout.addWidget(toolbar)
        
        # Pestañas: Pendientes y Completados
        self.tabs = QTabWidget()
        
        # Pestaña de recordatorios pendientes
        pending_widget = QWidget()
        pending_layout = QVBoxLayout(pending_widget)
        
        self.pending_label = QLabel("Recordatorios pendientes")
        self.pending_label.setAlignment(Qt.AlignCenter)
        pending_layout.addWidget(self.pending_label)
        
        self.pending_list = QListWidget()
        self.pending_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.pending_list.customContextMenuRequested.connect(self.show_pending_context_menu)
        self.pending_list.itemDoubleClicked.connect(self.view_event)
        pending_layout.addWidget(self.pending_list)
        
        # Pestaña de recordatorios completados
        completed_widget = QWidget()
        completed_layout = QVBoxLayout(completed_widget)
        
        self.completed_label = QLabel("Recordatorios completados")
        self.completed_label.setAlignment(Qt.AlignCenter)
        completed_layout.addWidget(self.completed_label)
        
        self.completed_list = QListWidget()
        self.completed_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.completed_list.customContextMenuRequested.connect(self.show_completed_context_menu)
        self.completed_list.itemDoubleClicked.connect(self.view_event)
        completed_layout.addWidget(self.completed_list)
        
        # Añadir pestañas
        self.tabs.addTab(pending_widget, "Pendientes")
        self.tabs.addTab(completed_widget, "Completados")
        
        layout.addWidget(self.tabs)
        
        # Cargar los recordatorios
        self.refresh_reminders()
    
    def load_pending_reminders(self, search_query=None, category_id=None):
        """Cargar recordatorios pendientes"""
        events = self.data_manager.get_events_with_reminders()
        
        # Filtrar por búsqueda si es necesario
        if search_query:
            events = [e for e in events if search_query.lower() in e['title'].lower() or
                     (e.get('description') and search_query.lower() in e['description'].lower())]
        
        # Filtrar por categoría si es necesario
        if category_id:
            events = [e for e in events if e.get('category_id') == category_id]
        
        self.pending_list.clear()
        
        if not events:
            item = QListWidgetItem("No hay recordatorios pendientes")
            item.setFlags(Qt.NoItemFlags)
            self.pending_list.addItem(item)
            return
        
        # Ordenar por fecha y hora
        events.sort(key=lambda x: (x['date'], x.get('time_start', '')))
        
        for event in events:
            date_str = event['date']
            try:
                qdate = QDate.fromString(date_str, "yyyy-MM-dd")
                date_formatted = qdate.toString("dd/MM/yyyy")
            except:
                date_formatted = date_str
            
            time_str = event.get('time_start', '')
            
            item_text = f"{date_formatted} {time_str} - {event['title']}"
            
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, event['id'])
            
            # Añadir color si existe categoría
            if event.get('category_color'):
                item.setForeground(QBrush(QColor(event['category_color'])))
            
            self.pending_list.addItem(item)
    
    def load_completed_reminders(self, search_query=None, category_id=None):
        """Cargar recordatorios completados"""
        # En una implementación real, obtendríamos los recordatorios completados
        # Por ahora, solo mostraremos un mensaje
        self.completed_list.clear()
        
        # Obtener eventos con recordatorios enviados
        events = []
        # En una implementación real: events = self.data_manager.get_events_with_completed_reminders()
        
        # Filtrar por búsqueda si es necesario
        if search_query:
            events = [e for e in events if search_query.lower() in e['title'].lower() or
                     (e.get('description') and search_query.lower() in e['description'].lower())]
        
        # Filtrar por categoría si es necesario
        if category_id:
            events = [e for e in events if e.get('category_id') == category_id]
        
        if not events:
            item = QListWidgetItem("No hay recordatorios completados")
            item.setFlags(Qt.NoItemFlags)
            self.completed_list.addItem(item)
            return
        
        # Ordenar por fecha y hora
        events.sort(key=lambda x: (x['date'], x.get('time_start', '')))
        
        for event in events:
            date_str = event['date']
            try:
                qdate = QDate.fromString(date_str, "yyyy-MM-dd")
                date_formatted = qdate.toString("dd/MM/yyyy")
            except:
                date_formatted = date_str
            
            time_str = event.get('time_start', '')
            
            item_text = f"{date_formatted} {time_str} - {event['title']}"
            
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, event['id'])
            
            # Añadir color si existe categoría
            if event.get('category_color'):
                item.setForeground(QBrush(QColor(event['category_color'])))
            
            self.completed_list.addItem(item)
    
    def show_pending_context_menu(self, position):
        """Mostrar menú contextual para recordatorios pendientes"""
        menu = QMenu()
        
        selected_items = self.pending_list.selectedItems()
        if not selected_items:
            return
        
        item = selected_items[0]
        event_id = item.data(Qt.UserRole)
        
        if not event_id:
            return
        
        view_action = menu.addAction("Ver evento")
        complete_action = menu.addAction("Marcar como completado")
        
        action = menu.exec_(self.pending_list.mapToGlobal(position))
        
        if action == view_action:
            self.view_event()
        elif action == complete_action:
            self.mark_as_completed(event_id)
    
    def show_completed_context_menu(self, position):
        """Mostrar menú contextual para recordatorios completados"""
        menu = QMenu()
        
        selected_items = self.completed_list.selectedItems()
        if not selected_items:
            return
        
        item = selected_items[0]
        event_id = item.data(Qt.UserRole)
        
        if not event_id:
            return
        
        view_action = menu.addAction("Ver evento")
        
        action = menu.exec_(self.completed_list.mapToGlobal(position))
        
        if action == view_action:
            self.view_event()
    
    def view_event(self):
        """Ver el evento seleccionado"""
        if self.tabs.currentIndex() == 0:  # Pestaña "Pendientes"
            selected_items = self.pending_list.selectedItems()
        else:  # Pestaña "Completados"
            selected_items = self.completed_list.selectedItems()
        
        if not selected_items:
            return
        
        item = selected_items[0]
        event_id = item.data(Qt.UserRole)
        
        if not event_id:
            return
        
        event = self.data_manager.get_event(event_id)
        if not event:
            return
        
        # Mostrar detalles del evento
        QMessageBox.information(
            self, 
            f"Evento: {event['title']}", 
            f"Fecha: {event['date']}\n"
            f"Hora: {event.get('time_start', '')} - {event.get('time_end', '')}\n"
            f"Categoría: {event.get('category_name', 'Sin categoría')}\n"
            f"Descripción: {event.get('description', '')}"
        )
    
    def mark_as_completed(self, event_id):
        """Marcar un recordatorio como completado"""
        # En una implementación real, marcaríamos el recordatorio como completado
        self.data_manager.mark_reminder_sent(event_id)
        self.app_signals.reminder_completed.emit(event_id)
        self.refresh_reminders()
    
    def search_reminders(self, query):
        """Buscar recordatorios"""
        self.refresh_reminders()
    
    @pyqtSlot()
    def refresh_reminders(self):
        """Actualizar las listas de recordatorios"""
        search_query = self.search_edit.text() if self.search_edit.text() else None
        category_id = self.category_combo.currentData()
        
        self.load_pending_reminders(search_query, category_id)
        self.load_completed_reminders(search_query, category_id)

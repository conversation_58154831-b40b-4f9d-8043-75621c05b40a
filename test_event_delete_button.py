#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para el botón de eliminar eventos (cruz roja)
"""

import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget,
                            QLabel, QPushButton, QHBoxLayout, QFrame, QTextEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

from event_item_widget import EventListWidget
from data_manager import DataManager
from styles import apply_styles


class TestEventDeleteButtonWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("✕ TRADEX BOT - Prueba de Botón Eliminar Eventos")
        self.setGeometry(100, 100, 900, 700)

        # Crear data manager
        self.data_manager = DataManager()

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # Título
        title = QLabel("✕ TRADEX BOT - Prueba del Botón de Eliminar Eventos")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 15px; color: #2C3E50;")
        layout.addWidget(title)

        # Descripción de funcionalidades
        features = QLabel(
            "✅ FUNCIONALIDADES IMPLEMENTADAS:\n\n"
            "✕ BOTÓN ELIMINAR: Aspa blanca en fondo negro, cuadrado y alineado\n"
            "⚠️ CONFIRMACIÓN: Diálogo de confirmación antes de eliminar\n"
            "🗑️ ELIMINACIÓN DEFINITIVA: Todos los datos relacionados se eliminan\n"
            "🔄 ACTUALIZACIÓN AUTOMÁTICA: Los números del calendario se actualizan\n"
            "📅 SINCRONIZACIÓN: Vista de eventos y calendario sincronizados\n"
            "🎯 INTERACCIÓN: Clic en aspa elimina, clic en evento edita\n"
            "✅ CHECKBOX: Marcar completado independiente de eliminar\n"
            "🎨 DISEÑO: Botón negro cuadrado con margen del borde, todos alineados"
        )
        features.setAlignment(Qt.AlignCenter)
        features.setStyleSheet(
            "margin: 15px; color: #2980B9; font-size: 11pt; "
            "background-color: #EBF5FB; padding: 20px; border-radius: 8px;"
        )
        features.setWordWrap(True)
        layout.addWidget(features)

        # Panel principal con eventos
        main_frame = QFrame()
        main_frame.setStyleSheet(
            "border: 2px solid #3498DB; border-radius: 8px; "
            "background-color: #F8F9FA; margin: 10px;"
        )
        main_layout = QVBoxLayout(main_frame)

        # Título de la lista
        events_title = QLabel("📋 Lista de Eventos de Prueba")
        events_title.setFont(QFont("Arial", 14, QFont.Bold))
        events_title.setAlignment(Qt.AlignCenter)
        events_title.setStyleSheet("color: #2C3E50; margin: 15px;")
        main_layout.addWidget(events_title)

        # Widget de lista de eventos
        self.events_list = EventListWidget()
        self.events_list.event_completed_changed.connect(self.on_event_completed_changed)
        self.events_list.event_edit_requested.connect(self.on_event_edit_requested)
        self.events_list.event_delete_requested.connect(self.on_event_delete_requested)

        main_layout.addWidget(self.events_list)

        layout.addWidget(main_frame)

        # Panel de controles
        controls_frame = QFrame()
        controls_frame.setStyleSheet(
            "border: 1px solid #BDC3C7; border-radius: 5px; "
            "background-color: #FAFAFA; margin: 5px;"
        )
        controls_layout = QHBoxLayout(controls_frame)

        # Botones de acción
        btn_create_events = QPushButton("➕ Crear Eventos de Prueba")
        btn_create_events.setMinimumHeight(40)
        btn_create_events.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        btn_create_events.clicked.connect(self.create_test_events)
        controls_layout.addWidget(btn_create_events)

        btn_clear_events = QPushButton("🗑️ Limpiar Todos los Eventos")
        btn_clear_events.setMinimumHeight(40)
        btn_clear_events.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        btn_clear_events.clicked.connect(self.clear_all_events)
        controls_layout.addWidget(btn_clear_events)

        btn_refresh = QPushButton("🔄 Actualizar Lista")
        btn_refresh.setMinimumHeight(40)
        btn_refresh.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        btn_refresh.clicked.connect(self.refresh_events)
        controls_layout.addWidget(btn_refresh)

        layout.addWidget(controls_frame)

        # Log de eventos
        log_title = QLabel("📝 Log de Eventos:")
        log_title.setFont(QFont("Arial", 11, QFont.Bold))
        log_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        layout.addWidget(log_title)

        self.log_area = QTextEdit()
        self.log_area.setMaximumHeight(120)
        self.log_area.setPlaceholderText("Los eventos del sistema aparecerán aquí...")
        self.log_area.setStyleSheet(
            "background-color: #FAFAFA; border: 1px solid #BDC3C7; "
            "border-radius: 3px; font-family: 'Courier New'; font-size: 9pt;"
        )
        layout.addWidget(self.log_area)

        # Cargar eventos existentes
        self.refresh_events()
        self.log_event("🚀 Aplicación iniciada - Lista de eventos cargada")

    def create_test_events(self):
        """Crear eventos de prueba"""
        try:
            today = QDate.currentDate()

            # Crear varios eventos de prueba
            test_events = [
                {
                    'title': 'Reunión de Trabajo',
                    'description': 'Reunión semanal del equipo de desarrollo',
                    'date': today.toString("yyyy-MM-dd"),
                    'time_start': '09:00',
                    'time_end': '10:30',
                    'all_day': 0,
                    'priority': 3,
                    'category': 'Trabajo',
                    'category_color': '#3498DB',
                    'completed': 0
                },
                {
                    'title': 'Cita Médica',
                    'description': 'Revisión anual con el doctor',
                    'date': today.toString("yyyy-MM-dd"),
                    'time_start': '14:00',
                    'time_end': '15:00',
                    'all_day': 0,
                    'priority': 4,
                    'category': 'Salud',
                    'category_color': '#E74C3C',
                    'completed': 0
                },
                {
                    'title': 'Cumpleaños de Ana',
                    'description': 'Celebración en el restaurante',
                    'date': today.toString("yyyy-MM-dd"),
                    'time_start': '19:00',
                    'time_end': '22:00',
                    'all_day': 0,
                    'priority': 2,
                    'category': 'Personal',
                    'category_color': '#9B59B6',
                    'completed': 0
                },
                {
                    'title': 'Evento Todo el Día',
                    'description': 'Conferencia virtual de tecnología',
                    'date': today.toString("yyyy-MM-dd"),
                    'time_start': '',
                    'time_end': '',
                    'all_day': 1,
                    'priority': 1,
                    'category': 'Educación',
                    'category_color': '#27AE60',
                    'completed': 0
                }
            ]

            created_count = 0
            for event_data in test_events:
                event_id = self.data_manager.add_event(event_data)
                if event_id:
                    created_count += 1

            self.refresh_events()
            self.log_event(f"➕ {created_count} eventos de prueba creados")

        except Exception as e:
            self.log_event(f"❌ Error creando eventos: {str(e)}")

    def clear_all_events(self):
        """Limpiar todos los eventos"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self,
                "Confirmar Limpieza",
                "¿Está seguro de eliminar TODOS los eventos?\n\n"
                "⚠️ Esta acción no se puede deshacer.",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Obtener todos los eventos
                all_events = self.data_manager.get_all_events()
                deleted_count = 0

                for event in all_events:
                    if self.data_manager.delete_event(event['id']):
                        deleted_count += 1

                self.refresh_events()
                self.log_event(f"🗑️ {deleted_count} eventos eliminados")

        except Exception as e:
            self.log_event(f"❌ Error limpiando eventos: {str(e)}")

    def refresh_events(self):
        """Actualizar la lista de eventos"""
        try:
            today = QDate.currentDate()
            date_str = today.toString("yyyy-MM-dd")
            events = self.data_manager.get_events_by_date(date_str)

            self.events_list.set_events(events)
            self.log_event(f"🔄 Lista actualizada: {len(events)} eventos para hoy")

        except Exception as e:
            self.log_event(f"❌ Error actualizando eventos: {str(e)}")

    def on_event_completed_changed(self, event_id, completed):
        """Manejar cambio en estado de completado"""
        success = self.data_manager.update_event_completed(event_id, completed)
        if success:
            status = "completado" if completed else "activo"
            self.log_event(f"✅ Evento {event_id} marcado como {status}")
        else:
            self.log_event(f"❌ Error actualizando estado del evento {event_id}")

    def on_event_edit_requested(self, event_id):
        """Manejar solicitud de edición"""
        self.log_event(f"✏️ Solicitud de edición para evento {event_id}")
        # En una aplicación real, aquí se abriría el diálogo de edición

    def on_event_delete_requested(self, event_id):
        """Manejar solicitud de eliminación"""
        # El diálogo de confirmación ya se mostró en el widget
        success = self.data_manager.delete_event(event_id)
        if success:
            self.log_event(f"✕ Evento {event_id} eliminado exitosamente")
            self.refresh_events()
        else:
            self.log_event(f"❌ Error eliminando evento {event_id}")

    def log_event(self, message):
        """Agregar evento al log"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {message}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")

    # Aplicar estilos
    apply_styles(app)

    window = TestEventDeleteButtonWindow()
    window.show()

    sys.exit(app.exec_())

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import threading
import time


class SoundManager:
    """Gestor de sonidos para las alarmas"""

    def __init__(self):
        self.sounds_dir = "sounds"
        self.active_threads = []  # Lista de hilos de sonido activos
        self.stop_flag = threading.Event()  # Flag para detener sonidos
        self.ensure_sounds_directory()

    def ensure_sounds_directory(self):
        """Asegurar que existe el directorio de sonidos"""
        if not os.path.exists(self.sounds_dir):
            os.makedirs(self.sounds_dir)

    def play_alarm_sound(self, sound_type="default", volume=50, duration=30):
        """Reproducir sonido de alarma"""
        # Detener sonidos anteriores
        self.stop_all_sounds()

        # Resetear flag de parada
        self.stop_flag.clear()

        try:
            # Intentar usar diferentes métodos según la plataforma
            if sys.platform.startswith('win'):
                self._play_windows_sound(sound_type, volume, duration)
            elif sys.platform.startswith('darwin'):
                self._play_mac_sound(sound_type, volume, duration)
            else:
                self._play_linux_sound(sound_type, volume, duration)
        except Exception as e:
            print(f"Error reproduciendo sonido: {e}")
            # Fallback: usar beep del sistema
            self._play_system_beep(duration)

    def _play_windows_sound(self, sound_type, volume, duration):
        """Reproducir sonido en Windows"""
        try:
            import winsound

            # Mapear tipos de sonido a frecuencias
            sound_frequencies = {
                'default': 1000,
                'bell': 800,
                'beep': 1200,
                'chime': 600
            }

            frequency = sound_frequencies.get(sound_type, 1000)

            # Reproducir en un hilo separado para no bloquear la UI
            def play_sound():
                end_time = time.time() + duration
                while time.time() < end_time and not self.stop_flag.is_set():
                    winsound.Beep(frequency, 500)  # 500ms de duración por beep
                    if not self.stop_flag.wait(0.1):  # Pausa entre beeps, pero verificar stop_flag
                        continue
                    else:
                        break

            thread = threading.Thread(target=play_sound)
            thread.daemon = True
            self.active_threads.append(thread)
            thread.start()

        except ImportError:
            self._play_system_beep(duration)

    def _play_mac_sound(self, sound_type, volume, duration):
        """Reproducir sonido en macOS"""
        try:
            import subprocess

            # Mapear tipos de sonido a sonidos del sistema
            system_sounds = {
                'default': 'Glass',
                'bell': 'Ping',
                'beep': 'Tink',
                'chime': 'Purr'
            }

            sound_name = system_sounds.get(sound_type, 'Glass')

            def play_sound():
                end_time = time.time() + duration
                while time.time() < end_time:
                    subprocess.run(['afplay', f'/System/Library/Sounds/{sound_name}.aiff'])
                    time.sleep(1)

            thread = threading.Thread(target=play_sound)
            thread.daemon = True
            thread.start()

        except Exception:
            self._play_system_beep(duration)

    def _play_linux_sound(self, sound_type, volume, duration):
        """Reproducir sonido en Linux"""
        try:
            import subprocess

            # Intentar usar paplay (PulseAudio)
            def play_sound():
                end_time = time.time() + duration
                while time.time() < end_time:
                    try:
                        # Generar un tono usando speaker-test
                        subprocess.run(['speaker-test', '-t', 'sine', '-f', '1000', '-l', '1'],
                                     timeout=1, capture_output=True)
                    except:
                        # Fallback a beep
                        subprocess.run(['beep'], capture_output=True)
                    time.sleep(0.5)

            thread = threading.Thread(target=play_sound)
            thread.daemon = True
            thread.start()

        except Exception:
            self._play_system_beep(duration)

    def _play_system_beep(self, duration):
        """Reproducir beep del sistema como fallback"""
        def beep_loop():
            end_time = time.time() + duration
            while time.time() < end_time:
                print('\a')  # Carácter de bell
                time.sleep(1)

        thread = threading.Thread(target=beep_loop)
        thread.daemon = True
        thread.start()

    def stop_all_sounds(self):
        """Detener todos los sonidos activos"""
        print("🔇 Deteniendo todos los sonidos...")

        # Establecer flag de parada
        self.stop_flag.set()

        # Esperar a que terminen los hilos activos
        for thread in self.active_threads:
            if thread.is_alive():
                thread.join(timeout=1.0)  # Esperar máximo 1 segundo

        # Limpiar lista de hilos
        self.active_threads.clear()

        print("✅ Sonidos detenidos")

    def test_sound(self, sound_type="default"):
        """Probar un sonido brevemente"""
        self.play_alarm_sound(sound_type, volume=50, duration=2)


# Instancia global del gestor de sonidos
sound_manager = SoundManager()

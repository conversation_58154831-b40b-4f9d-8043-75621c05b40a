#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget de cuenta atrás con configuración de tiempo y alarma
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QSpinBox, QFrame, QDialog, QFormLayout)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

from sound_manager import sound_manager


class CountdownConfigDialog(QDialog):
    """Diálogo para configurar la cuenta atrás"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configurar Cuenta Atrás")
        self.setModal(True)
        self.resize(300, 200)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)
        
        # Formulario
        form_layout = QFormLayout()
        
        # Horas
        self.hours_spinbox = QSpinBox()
        self.hours_spinbox.setRange(0, 23)
        self.hours_spinbox.setValue(0)
        self.hours_spinbox.setSuffix(" h")
        form_layout.addRow("Horas:", self.hours_spinbox)
        
        # Minutos
        self.minutes_spinbox = QSpinBox()
        self.minutes_spinbox.setRange(0, 59)
        self.minutes_spinbox.setValue(5)
        self.minutes_spinbox.setSuffix(" min")
        form_layout.addRow("Minutos:", self.minutes_spinbox)
        
        # Segundos
        self.seconds_spinbox = QSpinBox()
        self.seconds_spinbox.setRange(0, 59)
        self.seconds_spinbox.setValue(0)
        self.seconds_spinbox.setSuffix(" seg")
        form_layout.addRow("Segundos:", self.seconds_spinbox)
        
        layout.addLayout(form_layout)
        
        # Botones
        buttons_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("Iniciar")
        self.ok_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.ok_button)
        
        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
    
    def get_total_seconds(self):
        """Obtener el tiempo total en segundos"""
        hours = self.hours_spinbox.value()
        minutes = self.minutes_spinbox.value()
        seconds = self.seconds_spinbox.value()
        return hours * 3600 + minutes * 60 + seconds


class CountdownWidget(QFrame):
    """Widget de cuenta atrás"""
    
    # Señales
    countdown_finished = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.remaining_seconds = 0
        self.is_running = False
        
        # Timer para la cuenta atrás
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_countdown)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurar la interfaz del widget"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setLineWidth(1)
        self.setStyleSheet("""
            CountdownWidget {
                background-color: #FFFFFF;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
            }
            CountdownWidget:hover {
                border: 2px solid #E67E22;
                background-color: #FEF9E7;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(5)
        
        # Título
        title_label = QLabel("⏱️ CUENTA ATRÁS")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        title_label.setStyleSheet("color: #E67E22; margin: 2px;")
        layout.addWidget(title_label)
        
        # Display del tiempo
        self.time_display = QLabel("00:00:00")
        self.time_display.setAlignment(Qt.AlignCenter)
        self.time_display.setFont(QFont("Arial", 16, QFont.Bold))
        self.time_display.setStyleSheet("color: #2C3E50; background-color: #F8F9FA; padding: 5px; border-radius: 3px;")
        layout.addWidget(self.time_display)
        
        # Botones
        buttons_layout = QHBoxLayout()
        
        self.config_button = QPushButton("⚙️")
        self.config_button.setFixedSize(30, 25)
        self.config_button.setToolTip("Configurar tiempo")
        self.config_button.clicked.connect(self.configure_countdown)
        self.config_button.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        buttons_layout.addWidget(self.config_button)
        
        self.start_stop_button = QPushButton("▶️")
        self.start_stop_button.setFixedSize(30, 25)
        self.start_stop_button.setToolTip("Iniciar/Pausar")
        self.start_stop_button.clicked.connect(self.toggle_countdown)
        self.start_stop_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        buttons_layout.addWidget(self.start_stop_button)
        
        self.reset_button = QPushButton("🔄")
        self.reset_button.setFixedSize(30, 25)
        self.reset_button.setToolTip("Reiniciar")
        self.reset_button.clicked.connect(self.reset_countdown)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        buttons_layout.addWidget(self.reset_button)
        
        layout.addLayout(buttons_layout)
    
    def configure_countdown(self):
        """Configurar el tiempo de la cuenta atrás"""
        dialog = CountdownConfigDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            total_seconds = dialog.get_total_seconds()
            if total_seconds > 0:
                self.remaining_seconds = total_seconds
                self.update_display()
                self.is_running = False
                self.timer.stop()
                self.start_stop_button.setText("▶️")
                self.start_stop_button.setToolTip("Iniciar")
    
    def toggle_countdown(self):
        """Iniciar/pausar la cuenta atrás"""
        if self.remaining_seconds <= 0:
            # No hay tiempo configurado
            self.configure_countdown()
            return
        
        if self.is_running:
            # Pausar
            self.timer.stop()
            self.is_running = False
            self.start_stop_button.setText("▶️")
            self.start_stop_button.setToolTip("Continuar")
        else:
            # Iniciar
            self.timer.start(1000)  # 1 segundo
            self.is_running = True
            self.start_stop_button.setText("⏸️")
            self.start_stop_button.setToolTip("Pausar")
    
    def reset_countdown(self):
        """Reiniciar la cuenta atrás"""
        self.timer.stop()
        self.is_running = False
        self.remaining_seconds = 0
        self.update_display()
        self.start_stop_button.setText("▶️")
        self.start_stop_button.setToolTip("Configurar tiempo")
    
    def update_countdown(self):
        """Actualizar la cuenta atrás"""
        if self.remaining_seconds > 0:
            self.remaining_seconds -= 1
            self.update_display()
            
            # Cambiar color cuando quedan menos de 10 segundos
            if self.remaining_seconds <= 10:
                self.time_display.setStyleSheet(
                    "color: #E74C3C; background-color: #FADBD8; padding: 5px; border-radius: 3px; font-weight: bold;"
                )
        else:
            # Tiempo terminado
            self.timer.stop()
            self.is_running = False
            self.start_stop_button.setText("▶️")
            self.start_stop_button.setToolTip("Configurar tiempo")
            
            # Mostrar que terminó
            self.time_display.setText("¡TIEMPO!")
            self.time_display.setStyleSheet(
                "color: #FFFFFF; background-color: #E74C3C; padding: 5px; border-radius: 3px; font-weight: bold;"
            )
            
            # Reproducir alarma
            self.play_alarm()
            
            # Emitir señal
            self.countdown_finished.emit()
    
    def update_display(self):
        """Actualizar el display del tiempo"""
        hours = self.remaining_seconds // 3600
        minutes = (self.remaining_seconds % 3600) // 60
        seconds = self.remaining_seconds % 60
        
        time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        self.time_display.setText(time_str)
        
        # Restaurar color normal si no está en los últimos 10 segundos
        if self.remaining_seconds > 10:
            self.time_display.setStyleSheet(
                "color: #2C3E50; background-color: #F8F9FA; padding: 5px; border-radius: 3px;"
            )
    
    def play_alarm(self):
        """Reproducir alarma cuando termina la cuenta atrás"""
        try:
            # Usar el sound_manager para reproducir la alarma
            sound_manager.play_alarm_sound()
        except Exception as e:
            print(f"Error reproduciendo alarma de cuenta atrás: {e}")

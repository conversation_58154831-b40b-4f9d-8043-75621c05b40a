#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget de cuenta atrás con configuración de tiempo y alarma
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QSpinBox, QFrame, QDialog, QFormLayout)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

from sound_manager import sound_manager


class CountdownConfigDialog(QDialog):
    """Diálogo para configurar la cuenta atrás"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configurar Cuenta Atrás")
        self.setModal(True)
        self.resize(300, 200)

        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)

        # Formulario
        form_layout = QFormLayout()

        # Horas
        self.hours_spinbox = QSpinBox()
        self.hours_spinbox.setRange(0, 23)
        self.hours_spinbox.setValue(0)
        self.hours_spinbox.setSuffix(" h")
        form_layout.addRow("Horas:", self.hours_spinbox)

        # Minutos
        self.minutes_spinbox = QSpinBox()
        self.minutes_spinbox.setRange(0, 59)
        self.minutes_spinbox.setValue(5)
        self.minutes_spinbox.setSuffix(" min")
        form_layout.addRow("Minutos:", self.minutes_spinbox)

        # Segundos
        self.seconds_spinbox = QSpinBox()
        self.seconds_spinbox.setRange(0, 59)
        self.seconds_spinbox.setValue(0)
        self.seconds_spinbox.setSuffix(" seg")
        form_layout.addRow("Segundos:", self.seconds_spinbox)

        layout.addLayout(form_layout)

        # Botones
        buttons_layout = QHBoxLayout()

        self.ok_button = QPushButton("Iniciar")
        self.ok_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.ok_button)

        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)

        layout.addLayout(buttons_layout)

    def get_total_seconds(self):
        """Obtener el tiempo total en segundos"""
        hours = self.hours_spinbox.value()
        minutes = self.minutes_spinbox.value()
        seconds = self.seconds_spinbox.value()
        return hours * 3600 + minutes * 60 + seconds


class CountdownWidget(QFrame):
    """Widget de cuenta atrás con configuración directa"""

    # Señales
    countdown_finished = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.remaining_seconds = 0
        self.is_running = False
        self.is_config_mode = True  # Modo configuración por defecto

        # Timer para la cuenta atrás
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_countdown)

        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz del widget"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setLineWidth(1)
        self.setStyleSheet("""
            CountdownWidget {
                background-color: #FFFFFF;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
            }
            CountdownWidget:hover {
                border: 2px solid #E67E22;
                background-color: #FEF9E7;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(8)

        # Título
        title_label = QLabel("⏱️ CUENTA ATRÁS")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 11, QFont.Bold))
        title_label.setStyleSheet("color: #E67E22; margin: 3px;")
        layout.addWidget(title_label)

        # Configuración directa (spinboxes)
        self.config_container = QWidget()
        config_layout = QVBoxLayout(self.config_container)
        config_layout.setContentsMargins(0, 0, 0, 0)
        config_layout.setSpacing(4)

        # Fila de spinboxes
        spinbox_layout = QHBoxLayout()
        spinbox_layout.setSpacing(5)

        # Horas
        hours_container = QVBoxLayout()
        hours_label = QLabel("H")
        hours_label.setAlignment(Qt.AlignCenter)
        hours_label.setFont(QFont("Arial", 8))
        hours_label.setStyleSheet("color: #7F8C8D;")
        self.hours_spinbox = QSpinBox()
        self.hours_spinbox.setRange(0, 23)
        self.hours_spinbox.setValue(0)
        self.hours_spinbox.setFixedSize(40, 25)
        self.hours_spinbox.setStyleSheet("""
            QSpinBox {
                border: 1px solid #BDC3C7;
                border-radius: 3px;
                padding: 2px;
                font-weight: bold;
                text-align: center;
            }
            QSpinBox:focus {
                border: 2px solid #E67E22;
            }
        """)
        hours_container.addWidget(hours_label)
        hours_container.addWidget(self.hours_spinbox)
        spinbox_layout.addLayout(hours_container)

        # Separador
        sep1 = QLabel(":")
        sep1.setAlignment(Qt.AlignCenter)
        sep1.setFont(QFont("Arial", 12, QFont.Bold))
        sep1.setStyleSheet("color: #E67E22; margin-top: 15px;")
        spinbox_layout.addWidget(sep1)

        # Minutos
        minutes_container = QVBoxLayout()
        minutes_label = QLabel("M")
        minutes_label.setAlignment(Qt.AlignCenter)
        minutes_label.setFont(QFont("Arial", 8))
        minutes_label.setStyleSheet("color: #7F8C8D;")
        self.minutes_spinbox = QSpinBox()
        self.minutes_spinbox.setRange(0, 59)
        self.minutes_spinbox.setValue(5)
        self.minutes_spinbox.setFixedSize(40, 25)
        self.minutes_spinbox.setStyleSheet("""
            QSpinBox {
                border: 1px solid #BDC3C7;
                border-radius: 3px;
                padding: 2px;
                font-weight: bold;
                text-align: center;
            }
            QSpinBox:focus {
                border: 2px solid #E67E22;
            }
        """)
        minutes_container.addWidget(minutes_label)
        minutes_container.addWidget(self.minutes_spinbox)
        spinbox_layout.addLayout(minutes_container)

        # Separador
        sep2 = QLabel(":")
        sep2.setAlignment(Qt.AlignCenter)
        sep2.setFont(QFont("Arial", 12, QFont.Bold))
        sep2.setStyleSheet("color: #E67E22; margin-top: 15px;")
        spinbox_layout.addWidget(sep2)

        # Segundos
        seconds_container = QVBoxLayout()
        seconds_label = QLabel("S")
        seconds_label.setAlignment(Qt.AlignCenter)
        seconds_label.setFont(QFont("Arial", 8))
        seconds_label.setStyleSheet("color: #7F8C8D;")
        self.seconds_spinbox = QSpinBox()
        self.seconds_spinbox.setRange(0, 59)
        self.seconds_spinbox.setValue(0)
        self.seconds_spinbox.setFixedSize(40, 25)
        self.seconds_spinbox.setStyleSheet("""
            QSpinBox {
                border: 1px solid #BDC3C7;
                border-radius: 3px;
                padding: 2px;
                font-weight: bold;
                text-align: center;
            }
            QSpinBox:focus {
                border: 2px solid #E67E22;
            }
        """)
        seconds_container.addWidget(seconds_label)
        seconds_container.addWidget(self.seconds_spinbox)
        spinbox_layout.addLayout(seconds_container)

        config_layout.addLayout(spinbox_layout)
        layout.addWidget(self.config_container)

        # Display del tiempo (oculto inicialmente)
        self.time_display = QLabel("00:00:00")
        self.time_display.setAlignment(Qt.AlignCenter)
        self.time_display.setFont(QFont("Arial", 18, QFont.Bold))
        self.time_display.setStyleSheet("color: #2C3E50; background-color: #F8F9FA; padding: 8px; border-radius: 5px;")
        self.time_display.hide()
        layout.addWidget(self.time_display)

        # Botones mejorados
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)

        self.start_button = QPushButton("▶️ INICIAR")
        self.start_button.setFixedSize(80, 32)
        self.start_button.setToolTip("Iniciar cuenta atrás")
        self.start_button.clicked.connect(self.start_countdown)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #229954;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #1E8449;
            }
        """)
        buttons_layout.addWidget(self.start_button)

        self.pause_button = QPushButton("⏸️")
        self.pause_button.setFixedSize(35, 32)
        self.pause_button.setToolTip("Pausar")
        self.pause_button.clicked.connect(self.pause_countdown)
        self.pause_button.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #E67E22;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #D35400;
            }
        """)
        self.pause_button.hide()
        buttons_layout.addWidget(self.pause_button)

        self.reset_button = QPushButton("🔄")
        self.reset_button.setFixedSize(35, 32)
        self.reset_button.setToolTip("Reiniciar")
        self.reset_button.clicked.connect(self.reset_countdown)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #C0392B;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
        """)
        buttons_layout.addWidget(self.reset_button)

        layout.addLayout(buttons_layout)

    def start_countdown(self):
        """Iniciar la cuenta atrás"""
        # Obtener tiempo de los spinboxes
        hours = self.hours_spinbox.value()
        minutes = self.minutes_spinbox.value()
        seconds = self.seconds_spinbox.value()

        total_seconds = hours * 3600 + minutes * 60 + seconds

        if total_seconds <= 0:
            return  # No hay tiempo configurado

        # Configurar cuenta atrás
        self.remaining_seconds = total_seconds
        self.is_running = True
        self.is_config_mode = False

        # Cambiar interfaz a modo ejecución
        self.config_container.hide()
        self.time_display.show()
        self.start_button.hide()
        self.pause_button.show()

        # Actualizar display y iniciar timer
        self.update_display()
        self.timer.start(1000)  # 1 segundo

    def pause_countdown(self):
        """Pausar/continuar la cuenta atrás"""
        if self.is_running:
            # Pausar
            self.timer.stop()
            self.is_running = False
            self.pause_button.setText("▶️")
            self.pause_button.setToolTip("Continuar")
        else:
            # Continuar
            self.timer.start(1000)
            self.is_running = True
            self.pause_button.setText("⏸️")
            self.pause_button.setToolTip("Pausar")

    def reset_countdown(self):
        """Reiniciar la cuenta atrás"""
        self.timer.stop()
        self.is_running = False
        self.remaining_seconds = 0
        self.is_config_mode = True

        # Volver a modo configuración
        self.time_display.hide()
        self.config_container.show()
        self.pause_button.hide()
        self.start_button.show()

        # Resetear botón de pausa
        self.pause_button.setText("⏸️")
        self.pause_button.setToolTip("Pausar")

        # Restaurar estilo normal del botón reset
        self.restore_reset_button_style()

        # Restaurar valores por defecto
        self.hours_spinbox.setValue(0)
        self.minutes_spinbox.setValue(5)
        self.seconds_spinbox.setValue(0)

    def update_countdown(self):
        """Actualizar la cuenta atrás"""
        if self.remaining_seconds > 0:
            self.remaining_seconds -= 1
            self.update_display()

            # Cambiar color cuando quedan menos de 10 segundos
            if self.remaining_seconds <= 10:
                self.time_display.setStyleSheet(
                    "color: #E74C3C; background-color: #FADBD8; padding: 8px; border-radius: 5px; font-weight: bold; font-size: 18pt;"
                )
        else:
            # Tiempo terminado
            self.timer.stop()
            self.is_running = False

            # Mostrar que terminó
            self.time_display.setText("¡TIEMPO!")
            self.time_display.setStyleSheet(
                "color: #FFFFFF; background-color: #E74C3C; padding: 8px; border-radius: 5px; font-weight: bold; font-size: 18pt; animation: blink 1s infinite;"
            )

            # Ocultar botón de pausa y mostrar reset prominente
            self.pause_button.hide()
            self.reset_button.setStyleSheet("""
                QPushButton {
                    background-color: #E74C3C;
                    color: white;
                    border: 3px solid #FFFFFF;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 14pt;
                }
                QPushButton:hover {
                    background-color: #C0392B;
                    transform: scale(1.1);
                }
            """)

            # Reproducir alarma
            self.play_alarm()

            # Emitir señal
            self.countdown_finished.emit()

    def update_display(self):
        """Actualizar el display del tiempo"""
        hours = self.remaining_seconds // 3600
        minutes = (self.remaining_seconds % 3600) // 60
        seconds = self.remaining_seconds % 60

        time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        self.time_display.setText(time_str)

        # Restaurar color normal si no está en los últimos 10 segundos
        if self.remaining_seconds > 10:
            self.time_display.setStyleSheet(
                "color: #2C3E50; background-color: #F8F9FA; padding: 8px; border-radius: 5px; font-size: 18pt;"
            )

    def restore_reset_button_style(self):
        """Restaurar el estilo normal del botón reset"""
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #C0392B;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
        """)

    def play_alarm(self):
        """Reproducir alarma cuando termina la cuenta atrás"""
        try:
            # Usar el sound_manager para reproducir la alarma
            sound_manager.play_alarm_sound()
        except Exception as e:
            print(f"Error reproduciendo alarma de cuenta atrás: {e}")

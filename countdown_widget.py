#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget de cuenta atrás con configuración de tiempo y alarma
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QSpinBox, QFrame, QDialog, QFormLayout)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

from sound_manager import sound_manager


class CountdownConfigDialog(QDialog):
    """Diálogo para configurar la cuenta atrás"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configurar Cuenta Atrás")
        self.setModal(True)
        self.resize(300, 200)

        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)

        # Formulario
        form_layout = QFormLayout()

        # Horas
        self.hours_spinbox = QSpinBox()
        self.hours_spinbox.setRange(0, 23)
        self.hours_spinbox.setValue(0)
        self.hours_spinbox.setSuffix(" h")
        form_layout.addRow("Horas:", self.hours_spinbox)

        # Minutos
        self.minutes_spinbox = QSpinBox()
        self.minutes_spinbox.setRange(0, 59)
        self.minutes_spinbox.setValue(5)
        self.minutes_spinbox.setSuffix(" min")
        form_layout.addRow("Minutos:", self.minutes_spinbox)

        # Segundos
        self.seconds_spinbox = QSpinBox()
        self.seconds_spinbox.setRange(0, 59)
        self.seconds_spinbox.setValue(0)
        self.seconds_spinbox.setSuffix(" seg")
        form_layout.addRow("Segundos:", self.seconds_spinbox)

        layout.addLayout(form_layout)

        # Botones
        buttons_layout = QHBoxLayout()

        self.ok_button = QPushButton("Iniciar")
        self.ok_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.ok_button)

        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)

        layout.addLayout(buttons_layout)

    def get_total_seconds(self):
        """Obtener el tiempo total en segundos"""
        hours = self.hours_spinbox.value()
        minutes = self.minutes_spinbox.value()
        seconds = self.seconds_spinbox.value()
        return hours * 3600 + minutes * 60 + seconds


class CountdownWidget(QFrame):
    """Widget de cuenta atrás con configuración directa"""

    # Señales
    countdown_finished = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.remaining_seconds = 0
        self.is_running = False
        self.is_config_mode = True  # Modo configuración por defecto

        # Timer para la cuenta atrás
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_countdown)

        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz del widget"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setLineWidth(1)
        self.setStyleSheet("""
            CountdownWidget {
                background-color: #FFFFFF;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
            }
            CountdownWidget:hover {
                border: 2px solid #E67E22;
                background-color: #FEF9E7;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(10)

        # Título más grande
        title_label = QLabel("⏱️ CUENTA ATRÁS")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #E67E22; margin: 5px;")
        layout.addWidget(title_label)

        # Display del tiempo grande y clickeable
        self.time_display = QLabel("00:05:00")
        self.time_display.setAlignment(Qt.AlignCenter)
        self.time_display.setFont(QFont("Arial", 28, QFont.Bold))
        self.time_display.setStyleSheet("""
            QLabel {
                color: #2C3E50;
                background-color: #F8F9FA;
                padding: 15px;
                border-radius: 8px;
                border: 2px solid #BDC3C7;
                min-height: 60px;
            }
            QLabel:hover {
                background-color: #E8F4FD;
                border: 2px solid #3498DB;
            }
        """)
        self.time_display.mousePressEvent = self.edit_time
        layout.addWidget(self.time_display)

        # Variables para el tiempo configurado
        self.configured_hours = 0
        self.configured_minutes = 5
        self.configured_seconds = 0
        self.remaining_seconds = 5 * 60  # 5 minutos por defecto

        # Botones con texto claro
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.start_button = QPushButton("INICIAR")
        self.start_button.setFixedSize(100, 40)
        self.start_button.setToolTip("Iniciar cuenta atrás")
        self.start_button.clicked.connect(self.start_countdown)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1E8449;
            }
        """)
        buttons_layout.addWidget(self.start_button)

        self.pause_button = QPushButton("PAUSA")
        self.pause_button.setFixedSize(100, 40)
        self.pause_button.setToolTip("Pausar cuenta atrás")
        self.pause_button.clicked.connect(self.pause_countdown)
        self.pause_button.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #E67E22;
            }
            QPushButton:pressed {
                background-color: #D35400;
            }
        """)
        self.pause_button.hide()
        buttons_layout.addWidget(self.pause_button)

        self.stop_button = QPushButton("TERMINAR")
        self.stop_button.setFixedSize(100, 40)
        self.stop_button.setToolTip("Terminar y reiniciar")
        self.stop_button.clicked.connect(self.stop_countdown)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
        """)
        buttons_layout.addWidget(self.stop_button)

        layout.addLayout(buttons_layout)

    def edit_time(self, event):
        """Editar el tiempo haciendo clic en el display"""
        if not self.is_running:
            dialog = CountdownConfigDialog(self)
            dialog.hours_spinbox.setValue(self.configured_hours)
            dialog.minutes_spinbox.setValue(self.configured_minutes)
            dialog.seconds_spinbox.setValue(self.configured_seconds)

            if dialog.exec_() == QDialog.Accepted:
                total_seconds = dialog.get_total_seconds()
                if total_seconds > 0:
                    self.configured_hours = dialog.hours_spinbox.value()
                    self.configured_minutes = dialog.minutes_spinbox.value()
                    self.configured_seconds = dialog.seconds_spinbox.value()
                    self.remaining_seconds = total_seconds
                    self.update_display()

    def start_countdown(self):
        """Iniciar la cuenta atrás"""
        if self.remaining_seconds <= 0:
            return  # No hay tiempo configurado

        # Iniciar cuenta atrás
        self.is_running = True

        # Cambiar botones
        self.start_button.hide()
        self.pause_button.show()

        # Iniciar timer
        self.timer.start(1000)  # 1 segundo

    def pause_countdown(self):
        """Pausar/continuar la cuenta atrás"""
        if self.is_running:
            # Pausar
            self.timer.stop()
            self.is_running = False
            self.pause_button.setText("CONTINUAR")
        else:
            # Continuar
            self.timer.start(1000)
            self.is_running = True
            self.pause_button.setText("PAUSA")

    def stop_countdown(self):
        """Terminar y reiniciar la cuenta atrás"""
        self.timer.stop()
        self.is_running = False

        # Restaurar tiempo configurado
        total_seconds = (self.configured_hours * 3600 +
                        self.configured_minutes * 60 +
                        self.configured_seconds)
        self.remaining_seconds = total_seconds

        # Volver a estado inicial
        self.pause_button.hide()
        self.start_button.show()
        self.pause_button.setText("PAUSA")

        # Restaurar estilo normal del botón stop
        self.restore_stop_button_style()

        # Actualizar display
        self.update_display()

    def update_countdown(self):
        """Actualizar la cuenta atrás"""
        if self.remaining_seconds > 0:
            self.remaining_seconds -= 1
            self.update_display()

            # Cambiar color cuando quedan menos de 10 segundos
            if self.remaining_seconds <= 10:
                self.time_display.setStyleSheet(
                    "color: #E74C3C; background-color: #FADBD8; padding: 8px; border-radius: 5px; font-weight: bold; font-size: 18pt;"
                )
        else:
            # Tiempo terminado
            self.timer.stop()
            self.is_running = False

            # Mostrar que terminó
            self.time_display.setText("¡TIEMPO!")
            self.time_display.setStyleSheet(
                "color: #FFFFFF; background-color: #E74C3C; padding: 8px; border-radius: 5px; font-weight: bold; font-size: 18pt; animation: blink 1s infinite;"
            )

            # Ocultar botón de pausa y mostrar stop prominente
            self.pause_button.hide()
            self.stop_button.setStyleSheet("""
                QPushButton {
                    background-color: #E74C3C;
                    color: white;
                    border: 3px solid #FFFFFF;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 14pt;
                }
                QPushButton:hover {
                    background-color: #C0392B;
                    transform: scale(1.1);
                }
            """)

            # Reproducir alarma
            self.play_alarm()

            # Emitir señal
            self.countdown_finished.emit()

    def update_display(self):
        """Actualizar el display del tiempo"""
        hours = self.remaining_seconds // 3600
        minutes = (self.remaining_seconds % 3600) // 60
        seconds = self.remaining_seconds % 60

        time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        self.time_display.setText(time_str)

        # Cambiar color según el estado
        if self.remaining_seconds <= 10 and self.is_running:
            # Últimos 10 segundos - rojo parpadeante
            self.time_display.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    background-color: #E74C3C;
                    padding: 15px;
                    border-radius: 8px;
                    border: 2px solid #C0392B;
                    min-height: 60px;
                    font-size: 28pt;
                    font-weight: bold;
                }
            """)
        else:
            # Color normal
            self.time_display.setStyleSheet("""
                QLabel {
                    color: #2C3E50;
                    background-color: #F8F9FA;
                    padding: 15px;
                    border-radius: 8px;
                    border: 2px solid #BDC3C7;
                    min-height: 60px;
                    font-size: 28pt;
                    font-weight: bold;
                }
                QLabel:hover {
                    background-color: #E8F4FD;
                    border: 2px solid #3498DB;
                }
            """)

    def restore_stop_button_style(self):
        """Restaurar el estilo normal del botón stop"""
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
        """)

    def play_alarm(self):
        """Reproducir alarma cuando termina la cuenta atrás"""
        try:
            # Usar el sound_manager para reproducir la alarma
            sound_manager.play_alarm_sound()
        except Exception as e:
            print(f"Error reproduciendo alarma de cuenta atrás: {e}")

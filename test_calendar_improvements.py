#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar las mejoras del calendario:
- Días de la semana más grandes y en negrita
- Indicadores de prioridad en las celdas del calendario
- Múltiples eventos por día con diferentes prioridades
"""

import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QTextEdit)
from PyQt5.QtCore import Qt

from custom_calendar import CustomCalendarWidget
from data_manager import DataManager
from styles import apply_styles


class TestCalendarImprovementsWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Mejoras del Calendario")
        self.setGeometry(100, 100, 1000, 700)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Mejoras del Calendario")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "🎯 MEJORAS IMPLEMENTADAS:\n\n"
            "✅ DÍAS DE LA SEMANA:\n"
            "• Texto más grande (14pt) y en negrita\n"
            "• Mejor contraste con fondo oscuro\n"
            "• Encabezados más prominentes\n\n"
            "✅ INDICADORES DE EVENTOS:\n"
            "• Cuadrados de colores por prioridad en cada día\n"
            "• 🔴 Muy Importante | 🟠 Importante | 🟡 Poco Importante | 🟢 Normal\n"
            "• Múltiples indicadores si hay varios eventos\n"
            "• Números en los cuadrados si hay más de un evento de la misma prioridad\n\n"
            "🔧 PRUEBA: Crea eventos con diferentes prioridades y observa los indicadores en el calendario"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #2980B9; font-size: 10pt; background-color: #EBF5FB; padding: 15px; border-radius: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Layout horizontal para calendario y controles
        main_layout = QHBoxLayout()
        
        # Calendario personalizado
        self.calendar = CustomCalendarWidget(self.data_manager)
        main_layout.addWidget(self.calendar)
        
        # Panel de controles
        controls_widget = QWidget()
        controls_widget.setMaximumWidth(300)
        controls_layout = QVBoxLayout(controls_widget)
        
        # Botones para crear eventos de prueba
        controls_layout.addWidget(QLabel("Crear Eventos de Prueba:"))
        
        btn_create_today = QPushButton("📅 Eventos para Hoy")
        btn_create_today.setMinimumHeight(40)
        btn_create_today.setStyleSheet("font-size: 11pt; background-color: #3498DB; color: white; font-weight: bold;")
        btn_create_today.clicked.connect(self.create_events_today)
        controls_layout.addWidget(btn_create_today)
        
        btn_create_week = QPushButton("📆 Eventos para Esta Semana")
        btn_create_week.setMinimumHeight(40)
        btn_create_week.setStyleSheet("font-size: 11pt; background-color: #9B59B6; color: white; font-weight: bold;")
        btn_create_week.clicked.connect(self.create_events_week)
        controls_layout.addWidget(btn_create_week)
        
        btn_create_month = QPushButton("🗓️ Eventos para Este Mes")
        btn_create_month.setMinimumHeight(40)
        btn_create_month.setStyleSheet("font-size: 11pt; background-color: #E67E22; color: white; font-weight: bold;")
        btn_create_month.clicked.connect(self.create_events_month)
        controls_layout.addWidget(btn_create_month)
        
        controls_layout.addWidget(QLabel(""))  # Espaciador
        
        # Botones de limpieza
        btn_clear_today = QPushButton("🗑️ Limpiar Hoy")
        btn_clear_today.setMinimumHeight(35)
        btn_clear_today.setStyleSheet("font-size: 10pt; background-color: #E74C3C; color: white;")
        btn_clear_today.clicked.connect(self.clear_today_events)
        controls_layout.addWidget(btn_clear_today)
        
        btn_refresh = QPushButton("🔄 Actualizar Calendario")
        btn_refresh.setMinimumHeight(35)
        btn_refresh.setStyleSheet("font-size: 10pt; background-color: #27AE60; color: white;")
        btn_refresh.clicked.connect(self.refresh_calendar)
        controls_layout.addWidget(btn_refresh)
        
        controls_layout.addStretch()
        
        # Información de colores
        colors_info = QLabel(
            "🎨 COLORES DE PRIORIDAD:\n\n"
            "🔴 Muy Importante\n"
            "🟠 Importante\n"
            "🟡 Poco Importante\n"
            "🟢 Normal\n\n"
            "Los números en los cuadrados\n"
            "indican múltiples eventos\n"
            "de la misma prioridad."
        )
        colors_info.setAlignment(Qt.AlignLeft)
        colors_info.setStyleSheet("margin: 10px; color: #2C3E50; font-size: 9pt; background-color: #F8F9FA; padding: 10px; border-radius: 5px;")
        colors_info.setWordWrap(True)
        controls_layout.addWidget(colors_info)
        
        main_layout.addWidget(controls_widget)
        layout.addLayout(main_layout)
        
        # Área de resultados
        self.results_area = QTextEdit()
        self.results_area.setMaximumHeight(150)
        self.results_area.setPlaceholderText("Los resultados de las pruebas aparecerán aquí...")
        layout.addWidget(self.results_area)
    
    def create_events_today(self):
        """Crear eventos de prueba para hoy con diferentes prioridades"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        events_data = [
            {
                'title': 'Reunión muy importante',
                'date': today,
                'time_start': '09:00:00',
                'time_end': '10:00:00',
                'priority': 4,
                'color': '#E74C3C',
                'description': 'Evento de máxima prioridad'
            },
            {
                'title': 'Presentación del proyecto',
                'date': today,
                'time_start': '11:00:00',
                'time_end': '12:00:00',
                'priority': 3,
                'color': '#E67E22',
                'description': 'Evento importante'
            },
            {
                'title': 'Revisar emails',
                'date': today,
                'time_start': '14:00:00',
                'time_end': '14:30:00',
                'priority': 2,
                'color': '#F39C12',
                'description': 'Evento de baja prioridad'
            },
            {
                'title': 'Descanso',
                'date': today,
                'time_start': '15:00:00',
                'time_end': '15:15:00',
                'priority': 1,
                'color': '#27AE60',
                'description': 'Evento normal'
            }
        ]
        
        created_count = 0
        for event_data in events_data:
            event_id = self.data_manager.add_event(event_data)
            if event_id:
                created_count += 1
        
        self.results_area.append(f"✅ Creados {created_count} eventos para hoy ({today})")
        self.refresh_calendar()
    
    def create_events_week(self):
        """Crear eventos de prueba para esta semana"""
        today = datetime.now()
        created_count = 0
        
        for i in range(7):
            event_date = (today + timedelta(days=i)).strftime("%Y-%m-%d")
            priority = (i % 4) + 1  # Rotar prioridades 1-4
            
            event_data = {
                'title': f'Evento día {i+1}',
                'date': event_date,
                'time_start': f'{9 + i}:00:00',
                'time_end': f'{10 + i}:00:00',
                'priority': priority,
                'color': ['#27AE60', '#F39C12', '#E67E22', '#E74C3C'][priority-1],
                'description': f'Evento de prueba para el día {i+1} de la semana'
            }
            
            event_id = self.data_manager.add_event(event_data)
            if event_id:
                created_count += 1
        
        self.results_area.append(f"✅ Creados {created_count} eventos para esta semana")
        self.refresh_calendar()
    
    def create_events_month(self):
        """Crear eventos de prueba para este mes"""
        today = datetime.now()
        created_count = 0
        
        # Crear eventos cada 3 días del mes
        for day in range(1, 32, 3):
            try:
                event_date = today.replace(day=day).strftime("%Y-%m-%d")
                priority = ((day // 3) % 4) + 1  # Rotar prioridades
                
                event_data = {
                    'title': f'Evento mensual {day}',
                    'date': event_date,
                    'time_start': f'{10 + (day % 8)}:00:00',
                    'time_end': f'{11 + (day % 8)}:00:00',
                    'priority': priority,
                    'color': ['#27AE60', '#F39C12', '#E67E22', '#E74C3C'][priority-1],
                    'description': f'Evento de prueba para el día {day} del mes'
                }
                
                event_id = self.data_manager.add_event(event_data)
                if event_id:
                    created_count += 1
            except ValueError:
                # Día no válido para este mes
                continue
        
        self.results_area.append(f"✅ Creados {created_count} eventos para este mes")
        self.refresh_calendar()
    
    def clear_today_events(self):
        """Limpiar eventos de hoy"""
        today = datetime.now().strftime("%Y-%m-%d")
        events = self.data_manager.get_events_by_date(today)
        
        deleted_count = 0
        for event in events:
            if self.data_manager.delete_event(event['id']):
                deleted_count += 1
        
        self.results_area.append(f"🗑️ Eliminados {deleted_count} eventos de hoy")
        self.refresh_calendar()
    
    def refresh_calendar(self):
        """Actualizar el calendario"""
        self.calendar.refresh_events()
        self.results_area.append("🔄 Calendario actualizado")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestCalendarImprovementsWindow()
    window.show()
    
    sys.exit(app.exec_())

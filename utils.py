#!/usr/bin/env python
# -*- coding: utf-8 -*-

import datetime
import pytz
from PyQt5.QtCore import QDate, QTime, QDateTime


def get_local_timezone():
    """Obtener la zona horaria local"""
    return datetime.datetime.now(datetime.timezone.utc).astimezone().tzinfo


def convert_to_timezone(dt, timezone_str):
    """Convertir una fecha y hora a una zona horaria específica"""
    if timezone_str == "local":
        return dt
    
    # Asegurar que dt tiene zona horaria
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=get_local_timezone())
    
    target_tz = pytz.timezone(timezone_str)
    return dt.astimezone(target_tz)


def format_date(date, format_str="yyyy-MM-dd"):
    """Dar formato a una fecha"""
    if isinstance(date, QDate):
        return date.toString(format_str)
    elif isinstance(date, datetime.date):
        # Convertir a QDate y luego dar formato
        qdate = QDate(date.year, date.month, date.day)
        return qdate.toString(format_str)
    return str(date)


def format_time(time, format_str="HH:mm"):
    """Dar formato a una hora"""
    if isinstance(time, QTime):
        return time.toString(format_str)
    elif isinstance(time, datetime.time):
        # Convertir a QTime y luego dar formato
        qtime = QTime(time.hour, time.minute, time.second)
        return qtime.toString(format_str)
    return str(time)


def format_datetime(dt, format_str="yyyy-MM-dd HH:mm"):
    """Dar formato a una fecha y hora"""
    if isinstance(dt, QDateTime):
        return dt.toString(format_str)
    elif isinstance(dt, datetime.datetime):
        # Convertir a QDateTime y luego dar formato
        qdatetime = QDateTime(
            QDate(dt.year, dt.month, dt.day),
            QTime(dt.hour, dt.minute, dt.second)
        )
        return qdatetime.toString(format_str)
    return str(dt)


def parse_date(date_str, format_str="yyyy-MM-dd"):
    """Analizar una cadena y convertirla en fecha"""
    try:
        return QDate.fromString(date_str, format_str)
    except:
        return None


def parse_time(time_str, format_str="HH:mm"):
    """Analizar una cadena y convertirla en hora"""
    try:
        return QTime.fromString(time_str, format_str)
    except:
        return None


def parse_datetime(datetime_str, format_str="yyyy-MM-dd HH:mm"):
    """Analizar una cadena y convertirla en fecha y hora"""
    try:
        return QDateTime.fromString(datetime_str, format_str)
    except:
        return None


def get_reminder_text(minutes):
    """Obtener texto descriptivo para un recordatorio en minutos"""
    if not minutes:
        return "Sin recordatorio"
    
    minutes = int(minutes)
    
    if minutes < 60:
        return f"{minutes} minutos antes"
    elif minutes == 60:
        return "1 hora antes"
    elif minutes < 1440:  # Menos de un día
        hours = minutes // 60
        remaining_minutes = minutes % 60
        if remaining_minutes == 0:
            return f"{hours} horas antes"
        else:
            return f"{hours} horas y {remaining_minutes} minutos antes"
    elif minutes == 1440:
        return "1 día antes"
    else:
        days = minutes // 1440
        return f"{days} días antes"

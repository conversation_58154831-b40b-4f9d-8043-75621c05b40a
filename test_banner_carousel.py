#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para el carrusel de banners
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QTextEdit, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from banner_carousel_widget import BannerCarouselWidget, BannerItem
from data_manager import DataManager
from styles import apply_styles


class TestBannerCarouselWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎠 Prueba del Carrusel de Banners")
        self.setGeometry(100, 100, 1000, 700)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("🎠 Carrusel de Banners Dinámico")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 20pt; font-weight: bold; margin: 15px; color: #2C3E50;")
        layout.addWidget(title)
        
        # Descripción de funcionalidades
        features = QLabel(
            "🚀 FUNCIONALIDADES DEL CARRUSEL:\n\n"
            "✅ MÚLTIPLES BANNERS: Agregue tantos banners como desee\n"
            "🔄 ROTACIÓN AUTOMÁTICA: Cambio automático con duración configurable\n"
            "🎨 TRANSICIONES: Deslizar, desvanecer o instantáneo\n"
            "⏸️ PAUSA AL HOVER: Se pausa automáticamente al pasar el mouse\n"
            "🖼️ SOPORTE MULTIMEDIA: Imágenes y texto alternativo\n"
            "🔗 ENLACES CLICKEABLES: Cada banner puede tener su propia URL\n"
            "📐 ALTURA CONFIGURABLE: Ajustable entre 40-200 píxeles\n"
            "➕ GESTIÓN FÁCIL: Botón + para agregar, editar, duplicar y eliminar\n"
            "⚙️ CONFIGURACIÓN AVANZADA: Control total sobre el comportamiento"
        )
        features.setAlignment(Qt.AlignCenter)
        features.setStyleSheet(
            "margin: 15px; color: #2980B9; font-size: 11pt; "
            "background-color: #EBF5FB; padding: 20px; border-radius: 8px;"
        )
        features.setWordWrap(True)
        layout.addWidget(features)
        
        # Área de simulación del calendario
        simulation_frame = QFrame()
        simulation_frame.setStyleSheet(
            "border: 2px solid #3498DB; border-radius: 8px; "
            "background-color: #F8F9FA; margin: 10px;"
        )
        sim_layout = QVBoxLayout(simulation_frame)
        
        sim_title = QLabel("📱 Simulación del Layout del Calendario")
        sim_title.setFont(QFont("Arial", 14, QFont.Bold))
        sim_title.setAlignment(Qt.AlignCenter)
        sim_title.setStyleSheet("color: #2C3E50; margin: 15px;")
        sim_layout.addWidget(sim_title)
        
        # Área principal simulada
        main_area = QWidget()
        main_area.setMinimumHeight(250)
        main_area.setStyleSheet(
            "background-color: #ECF0F1; border: 1px solid #BDC3C7; "
            "border-radius: 5px; margin: 10px;"
        )
        main_layout = QVBoxLayout(main_area)
        
        main_label = QLabel(
            "Área Principal del Calendario\n"
            "(Calendario + Lista de Eventos + Relojes Mundiales)\n\n"
            "El carrusel de banners aparece en la parte inferior\n"
            "ocupando toda la anchura disponible"
        )
        main_label.setAlignment(Qt.AlignCenter)
        main_label.setStyleSheet("color: #7F8C8D; font-size: 12pt; font-style: italic;")
        main_layout.addWidget(main_label)
        
        sim_layout.addWidget(main_area)
        
        # Carrusel de banners (la estrella del show)
        self.banner_carousel = BannerCarouselWidget(self.data_manager)
        self.banner_carousel.banner_clicked.connect(self.on_banner_clicked)
        sim_layout.addWidget(self.banner_carousel)
        
        layout.addWidget(simulation_frame)
        
        # Panel de controles y información
        controls_frame = QFrame()
        controls_frame.setStyleSheet(
            "border: 1px solid #BDC3C7; border-radius: 5px; "
            "background-color: #FAFAFA; margin: 5px;"
        )
        controls_layout = QHBoxLayout(controls_frame)
        
        # Botones de prueba
        buttons_widget = QWidget()
        buttons_layout = QVBoxLayout(buttons_widget)
        
        buttons_title = QLabel("🔧 Controles de Prueba:")
        buttons_title.setFont(QFont("Arial", 12, QFont.Bold))
        buttons_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        buttons_layout.addWidget(buttons_title)
        
        # Botón para crear banners de ejemplo
        btn_create_samples = QPushButton("🎨 Crear Banners de Ejemplo")
        btn_create_samples.setMinimumHeight(35)
        btn_create_samples.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        btn_create_samples.clicked.connect(self.create_sample_banners)
        buttons_layout.addWidget(btn_create_samples)
        
        # Botón para limpiar banners
        btn_clear = QPushButton("🗑️ Limpiar Banners")
        btn_clear.setMinimumHeight(35)
        btn_clear.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        btn_clear.clicked.connect(self.clear_banners)
        buttons_layout.addWidget(btn_clear)
        
        # Botón para configurar carrusel
        btn_configure = QPushButton("⚙️ Configurar Carrusel")
        btn_configure.setMinimumHeight(35)
        btn_configure.setStyleSheet("""
            QPushButton {
                background-color: #9B59B6;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #8E44AD;
            }
        """)
        btn_configure.clicked.connect(self.configure_carousel)
        buttons_layout.addWidget(btn_configure)
        
        buttons_layout.addStretch()
        controls_layout.addWidget(buttons_widget)
        
        # Información del carrusel
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        info_title = QLabel("📊 Información del Carrusel:")
        info_title.setFont(QFont("Arial", 12, QFont.Bold))
        info_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        info_layout.addWidget(info_title)
        
        self.info_label = QLabel("Cargando información...")
        self.info_label.setStyleSheet(
            "color: #34495E; font-size: 10pt; background-color: #F8F9FA; "
            "padding: 10px; border-radius: 5px; border: 1px solid #E0E0E0;"
        )
        self.info_label.setWordWrap(True)
        info_layout.addWidget(self.info_label)
        
        info_layout.addStretch()
        controls_layout.addWidget(info_widget)
        
        layout.addWidget(controls_frame)
        
        # Área de log de eventos
        log_title = QLabel("📝 Log de Eventos:")
        log_title.setFont(QFont("Arial", 11, QFont.Bold))
        log_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        layout.addWidget(log_title)
        
        self.log_area = QTextEdit()
        self.log_area.setMaximumHeight(120)
        self.log_area.setPlaceholderText("Los eventos del carrusel aparecerán aquí...")
        self.log_area.setStyleSheet(
            "background-color: #FAFAFA; border: 1px solid #BDC3C7; "
            "border-radius: 3px; font-family: 'Courier New';"
        )
        layout.addWidget(self.log_area)
        
        # Actualizar información inicial
        self.update_carousel_info()
        self.log_event("🎠 Carrusel de banners inicializado")
    
    def create_sample_banners(self):
        """Crear banners de ejemplo para demostración"""
        try:
            sample_banners = [
                {
                    'id': 'sample_1',
                    'enabled': True,
                    'title': 'Banner de Bienvenida',
                    'image_path': '',
                    'url': 'https://www.google.com',
                    'alt_text': '🎉 ¡Bienvenido al Carrusel de Banners!',
                    'duration': 3000
                },
                {
                    'id': 'sample_2',
                    'enabled': True,
                    'title': 'Banner Promocional',
                    'image_path': '',
                    'url': 'https://www.github.com',
                    'alt_text': '🚀 Descubre nuevas funcionalidades',
                    'duration': 4000
                },
                {
                    'id': 'sample_3',
                    'enabled': True,
                    'title': 'Banner Informativo',
                    'image_path': '',
                    'url': 'https://www.stackoverflow.com',
                    'alt_text': '💡 Consejos y trucos útiles',
                    'duration': 5000
                }
            ]
            
            # Guardar configuración con banners de ejemplo
            config = {
                'enabled': True,
                'height': 67,
                'transition_type': 'Deslizar',
                'transition_speed': 500,
                'pause_on_hover': True,
                'banners': sample_banners
            }
            
            self.data_manager.set_config('banner_carousel_config', config)
            self.banner_carousel.load_carousel_config()
            
            self.log_event("✅ Banners de ejemplo creados (3 banners)")
            self.update_carousel_info()
            
        except Exception as e:
            self.log_event(f"❌ Error creando banners: {str(e)}")
    
    def clear_banners(self):
        """Limpiar todos los banners"""
        try:
            config = {
                'enabled': True,
                'height': 67,
                'transition_type': 'Deslizar',
                'transition_speed': 500,
                'pause_on_hover': True,
                'banners': []
            }
            
            self.data_manager.set_config('banner_carousel_config', config)
            self.banner_carousel.load_carousel_config()
            
            self.log_event("🗑️ Todos los banners eliminados")
            self.update_carousel_info()
            
        except Exception as e:
            self.log_event(f"❌ Error limpiando banners: {str(e)}")
    
    def configure_carousel(self):
        """Abrir configuración del carrusel"""
        self.banner_carousel.configure_carousel()
        self.update_carousel_info()
        self.log_event("⚙️ Configuración del carrusel abierta")
    
    def on_banner_clicked(self, url):
        """Manejar clic en banner"""
        self.log_event(f"🔗 Banner clickeado: {url}")
    
    def update_carousel_info(self):
        """Actualizar información del carrusel"""
        try:
            config = self.data_manager.get_config('banner_carousel_config', {})
            
            enabled = config.get('enabled', False)
            height = config.get('height', 67)
            transition = config.get('transition_type', 'Deslizar')
            speed = config.get('transition_speed', 500)
            pause_hover = config.get('pause_on_hover', True)
            banners = config.get('banners', [])
            
            active_banners = [b for b in banners if b.get('enabled', True)]
            total_duration = sum(b.get('duration', 5000) for b in active_banners) / 1000
            
            info_text = f"""🎠 ESTADO: {'✅ Activo' if enabled else '❌ Inactivo'}
📐 ALTURA: {height} píxeles
🔄 TRANSICIÓN: {transition} ({speed}ms)
⏸️ PAUSA AL HOVER: {'✅ Sí' if pause_hover else '❌ No'}
📊 BANNERS: {len(active_banners)}/{len(banners)} activos
⏱️ CICLO COMPLETO: {total_duration:.1f} segundos
🎯 ESTADO ACTUAL: {'Rotando' if len(active_banners) > 1 else 'Estático'}"""
            
            self.info_label.setText(info_text)
            
        except Exception as e:
            self.info_label.setText(f"❌ Error obteniendo información: {str(e)}")
    
    def log_event(self, message):
        """Agregar evento al log"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {message}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestBannerCarouselWindow()
    window.show()
    
    sys.exit(app.exec_())

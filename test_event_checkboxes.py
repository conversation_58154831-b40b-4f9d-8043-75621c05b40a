#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar la funcionalidad de checkboxes en eventos:
- Checkbox al lado de cada evento
- Fondo gris claro cuando está marcado como completado
- Texto tachado para eventos completados
- Posibilidad de marcar/desmarcar eventos
"""

import sys
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QTextEdit)
from PyQt5.QtCore import Qt

from event_item_widget import EventListWidget
from data_manager import DataManager
from styles import apply_styles


class TestEventCheckboxesWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Checkboxes en Eventos")
        self.setGeometry(100, 100, 900, 700)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Checkboxes en Eventos")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "🎯 FUNCIONALIDAD DE CHECKBOXES IMPLEMENTADA:\n\n"
            "✅ CHECKBOX AL LADO DE CADA EVENTO:\n"
            "• Checkbox clickeable para marcar como completado\n"
            "• Estado se guarda en la base de datos\n"
            "• Sincronización automática con la interfaz\n\n"
            "✅ CAMBIOS VISUALES AL COMPLETAR:\n"
            "• Fondo gris muy claro (#F8F9FA)\n"
            "• Texto tachado y más tenue\n"
            "• Borde izquierdo gris en lugar del color de categoría\n\n"
            "✅ INTERACCIÓN:\n"
            "• Clic en checkbox: marcar/desmarcar completado\n"
            "• Clic en evento: abrir diálogo de edición\n"
            "• Estado persiste entre sesiones\n\n"
            "🔧 PRUEBA: Marca y desmarca eventos para ver los cambios visuales"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #2980B9; font-size: 10pt; background-color: #EBF5FB; padding: 15px; border-radius: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Layout horizontal para lista de eventos y controles
        main_layout = QHBoxLayout()
        
        # Lista de eventos con checkboxes
        events_container = QWidget()
        events_container.setMaximumWidth(500)
        events_layout = QVBoxLayout(events_container)
        
        events_label = QLabel("📋 Lista de Eventos con Checkboxes:")
        events_label.setFont(QFont("Arial", 12, QFont.Bold))
        events_layout.addWidget(events_label)
        
        self.events_list = EventListWidget()
        self.events_list.event_completed_changed.connect(self.on_event_completed_changed)
        self.events_list.event_edit_requested.connect(self.on_event_edit_requested)
        events_layout.addWidget(self.events_list)
        
        main_layout.addWidget(events_container)
        
        # Panel de controles
        controls_widget = QWidget()
        controls_widget.setMaximumWidth(350)
        controls_layout = QVBoxLayout(controls_widget)
        
        # Botones para crear eventos de prueba
        controls_layout.addWidget(QLabel("🎯 CREAR EVENTOS DE PRUEBA:"))
        
        btn_create_mixed = QPushButton("Crear Eventos Variados")
        btn_create_mixed.setMinimumHeight(40)
        btn_create_mixed.setStyleSheet("font-size: 11pt; background-color: #3498DB; color: white; font-weight: bold;")
        btn_create_mixed.clicked.connect(self.create_mixed_events)
        controls_layout.addWidget(btn_create_mixed)
        
        btn_create_priorities = QPushButton("Crear por Prioridades")
        btn_create_priorities.setMinimumHeight(40)
        btn_create_priorities.setStyleSheet("font-size: 11pt; background-color: #9B59B6; color: white; font-weight: bold;")
        btn_create_priorities.clicked.connect(self.create_priority_events)
        controls_layout.addWidget(btn_create_priorities)
        
        controls_layout.addWidget(QLabel(""))  # Espaciador
        
        # Botones de prueba de estado
        controls_layout.addWidget(QLabel("🔄 PRUEBAS DE ESTADO:"))
        
        btn_mark_all = QPushButton("Marcar Todos Completados")
        btn_mark_all.setMinimumHeight(35)
        btn_mark_all.setStyleSheet("font-size: 10pt; background-color: #27AE60; color: white;")
        btn_mark_all.clicked.connect(self.mark_all_completed)
        controls_layout.addWidget(btn_mark_all)
        
        btn_unmark_all = QPushButton("Desmarcar Todos")
        btn_unmark_all.setMinimumHeight(35)
        btn_unmark_all.setStyleSheet("font-size: 10pt; background-color: #E67E22; color: white;")
        btn_unmark_all.clicked.connect(self.unmark_all_completed)
        controls_layout.addWidget(btn_unmark_all)
        
        btn_toggle_random = QPushButton("Alternar Aleatorio")
        btn_toggle_random.setMinimumHeight(35)
        btn_toggle_random.setStyleSheet("font-size: 10pt; background-color: #8E44AD; color: white;")
        btn_toggle_random.clicked.connect(self.toggle_random_events)
        controls_layout.addWidget(btn_toggle_random)
        
        controls_layout.addWidget(QLabel(""))  # Espaciador
        
        # Utilidades
        controls_layout.addWidget(QLabel("🔧 UTILIDADES:"))
        
        btn_refresh = QPushButton("🔄 Actualizar Lista")
        btn_refresh.setMinimumHeight(30)
        btn_refresh.setStyleSheet("background-color: #34495E; color: white;")
        btn_refresh.clicked.connect(self.refresh_events)
        controls_layout.addWidget(btn_refresh)
        
        btn_clear = QPushButton("🗑️ Limpiar Eventos")
        btn_clear.setMinimumHeight(30)
        btn_clear.setStyleSheet("background-color: #E74C3C; color: white;")
        btn_clear.clicked.connect(self.clear_events)
        controls_layout.addWidget(btn_clear)
        
        controls_layout.addStretch()
        
        # Información técnica
        tech_info = QLabel(
            "📐 ESPECIFICACIONES TÉCNICAS:\n\n"
            "🗄️ BASE DE DATOS:\n"
            "• Campo 'completed' INTEGER DEFAULT 0\n"
            "• Método update_event_completed()\n"
            "• Persistencia automática\n\n"
            "🎨 ESTILOS VISUALES:\n"
            "• Completado: Fondo #F8F9FA\n"
            "• Texto tachado y color #7F8C8D\n"
            "• Borde izquierdo gris #95A5A6\n"
            "• Activo: Fondo blanco\n"
            "• Borde color de categoría\n\n"
            "🔧 INTERACCIÓN:\n"
            "• Checkbox: cambiar estado\n"
            "• Clic en evento: editar\n"
            "• Señales Qt para comunicación"
        )
        tech_info.setAlignment(Qt.AlignLeft)
        tech_info.setStyleSheet("margin: 5px; color: #2C3E50; font-size: 8pt; background-color: #F8F9FA; padding: 8px; border-radius: 3px;")
        tech_info.setWordWrap(True)
        controls_layout.addWidget(tech_info)
        
        main_layout.addWidget(controls_widget)
        layout.addLayout(main_layout)
        
        # Área de resultados
        self.results_area = QTextEdit()
        self.results_area.setMaximumHeight(120)
        self.results_area.setPlaceholderText("Los resultados de las pruebas aparecerán aquí...")
        layout.addWidget(self.results_area)
        
        # Cargar eventos existentes
        self.refresh_events()
    
    def create_mixed_events(self):
        """Crear eventos variados para prueba"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        events_data = [
            {
                'title': 'Reunión matutina',
                'date': today,
                'time_start': '09:00:00',
                'time_end': '10:00:00',
                'priority': 3,
                'color': '#E67E22',
                'description': 'Reunión de equipo para planificar el día',
                'completed': 0
            },
            {
                'title': 'Llamada con cliente',
                'date': today,
                'time_start': '11:30:00',
                'time_end': '12:00:00',
                'priority': 4,
                'color': '#E74C3C',
                'description': 'Llamada importante con cliente VIP',
                'completed': 1  # Ya completado
            },
            {
                'title': 'Almuerzo',
                'date': today,
                'time_start': '13:00:00',
                'time_end': '14:00:00',
                'priority': 1,
                'color': '#27AE60',
                'description': 'Descanso para almorzar',
                'completed': 0
            },
            {
                'title': 'Revisar emails',
                'date': today,
                'time_start': '15:00:00',
                'time_end': '15:30:00',
                'priority': 2,
                'color': '#F39C12',
                'description': 'Revisar y responder emails pendientes',
                'completed': 1  # Ya completado
            }
        ]
        
        created_count = 0
        for event_data in events_data:
            event_id = self.data_manager.add_event(event_data)
            if event_id:
                created_count += 1
        
        self.results_area.append(f"✅ Creados {created_count} eventos variados (algunos ya completados)")
        self.refresh_events()
    
    def create_priority_events(self):
        """Crear eventos de diferentes prioridades"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        priorities_data = [
            (4, "Muy Importante", "#E74C3C", "Tarea crítica urgente"),
            (3, "Importante", "#E67E22", "Tarea importante del día"),
            (2, "Poco Importante", "#F39C12", "Tarea de baja prioridad"),
            (1, "Normal", "#27AE60", "Tarea rutinaria")
        ]
        
        created_count = 0
        for i, (priority, name, color, desc) in enumerate(priorities_data):
            event_data = {
                'title': f'Evento {name}',
                'date': today,
                'time_start': f'{10 + i}:00:00',
                'time_end': f'{11 + i}:00:00',
                'priority': priority,
                'color': color,
                'description': desc,
                'completed': 0
            }
            
            event_id = self.data_manager.add_event(event_data)
            if event_id:
                created_count += 1
        
        self.results_area.append(f"✅ Creados {created_count} eventos por prioridad")
        self.refresh_events()
    
    def mark_all_completed(self):
        """Marcar todos los eventos como completados"""
        today = datetime.now().strftime("%Y-%m-%d")
        events = self.data_manager.get_events_by_date(today)
        
        updated_count = 0
        for event in events:
            if not event.get('completed', 0):
                if self.data_manager.update_event_completed(event['id'], 1):
                    updated_count += 1
        
        self.results_area.append(f"✅ Marcados {updated_count} eventos como completados")
        self.refresh_events()
    
    def unmark_all_completed(self):
        """Desmarcar todos los eventos"""
        today = datetime.now().strftime("%Y-%m-%d")
        events = self.data_manager.get_events_by_date(today)
        
        updated_count = 0
        for event in events:
            if event.get('completed', 0):
                if self.data_manager.update_event_completed(event['id'], 0):
                    updated_count += 1
        
        self.results_area.append(f"🔄 Desmarcados {updated_count} eventos")
        self.refresh_events()
    
    def toggle_random_events(self):
        """Alternar estado de eventos aleatoriamente"""
        import random
        
        today = datetime.now().strftime("%Y-%m-%d")
        events = self.data_manager.get_events_by_date(today)
        
        updated_count = 0
        for event in events:
            if random.choice([True, False]):  # 50% probabilidad
                new_state = 1 - event.get('completed', 0)  # Alternar estado
                if self.data_manager.update_event_completed(event['id'], new_state):
                    updated_count += 1
        
        self.results_area.append(f"🎲 Alternados {updated_count} eventos aleatoriamente")
        self.refresh_events()
    
    def clear_events(self):
        """Limpiar todos los eventos de hoy"""
        today = datetime.now().strftime("%Y-%m-%d")
        events = self.data_manager.get_events_by_date(today)
        
        deleted_count = 0
        for event in events:
            if self.data_manager.delete_event(event['id']):
                deleted_count += 1
        
        self.results_area.append(f"🗑️ Eliminados {deleted_count} eventos")
        self.refresh_events()
    
    def refresh_events(self):
        """Actualizar la lista de eventos"""
        today = datetime.now().strftime("%Y-%m-%d")
        events = self.data_manager.get_events_by_date(today)
        self.events_list.set_events(events)
        
        completed_count = sum(1 for event in events if event.get('completed', 0))
        total_count = len(events)
        
        self.results_area.append(f"🔄 Lista actualizada: {total_count} eventos ({completed_count} completados)")
    
    def on_event_completed_changed(self, event_id, completed):
        """Manejar cambio en el estado de completado"""
        success = self.data_manager.update_event_completed(event_id, completed)
        if success:
            status = "completado" if completed else "activo"
            self.results_area.append(f"✅ Evento {event_id} marcado como {status}")
        else:
            self.results_area.append(f"❌ Error al actualizar evento {event_id}")
    
    def on_event_edit_requested(self, event_id):
        """Manejar solicitud de edición de evento"""
        self.results_area.append(f"✏️ Solicitud de edición para evento {event_id}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestEventCheckboxesWindow()
    window.show()
    
    sys.exit(app.exec_())

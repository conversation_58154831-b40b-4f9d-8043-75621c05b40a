#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script simple para verificar PyQt5
"""

import sys

try:
    print("1. Importando PyQt5...")
    from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel
    from PyQt5.QtCore import Qt
    print("   ✅ PyQt5 importado correctamente")
    
    print("2. Creando aplicación...")
    app = QApplication(sys.argv)
    print("   ✅ QApplication creada")
    
    print("3. Creando ventana...")
    window = QMainWindow()
    window.setWindowTitle("Prueba Simple")
    window.setGeometry(100, 100, 400, 300)
    
    label = QLabel("¡PyQt5 funciona correctamente!")
    label.setAlignment(Qt.AlignCenter)
    window.setCentralWidget(label)
    print("   ✅ Ventana creada")
    
    print("4. Mostrando ventana...")
    window.show()
    print("   ✅ Ventana mostrada")
    
    print("5. Iniciando bucle de eventos...")
    sys.exit(app.exec_())
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

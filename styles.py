#!/usr/bin/env python
# -*- coding: utf-8 -*-

def apply_styles(app):
    """Aplicar estilos globales a la aplicación"""
    # Configurar un estilo base más amplio
    app.setStyleSheet("""
        /* Aumentar tamaño de fuente general */
        * {
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 10pt;
        }

        /* Estilo para etiquetas de título */
        QLabel[title="true"] {
            font-size: 13pt;
            font-weight: bold;
            color: #2C3E50;
        }

        /* Mejorar botones */
        QPushButton {
            min-height: 30px;
            padding: 5px 15px;
            background-color: #34495E;
            color: white;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #2C3E50;
        }

        QPushButton:pressed {
            background-color: #1B2631;
        }

        /* Botones de acción especial */
        QPushButton[action="primary"] {
            background-color: #3498DB;
        }

        QPushButton[action="primary"]:hover {
            background-color: #2980B9;
        }

        QPushButton[action="success"] {
            background-color: #2ECC71;
        }

        QPushButton[action="success"]:hover {
            background-color: #27AE60;
        }

        QPushButton[action="warning"] {
            background-color: #F39C12;
        }

        QPushButton[action="warning"]:hover {
            background-color: #D35400;
        }

        QPushButton[action="danger"] {
            background-color: #E74C3C;
        }

        QPushButton[action="danger"]:hover {
            background-color: #C0392B;
        }

        /* Mejorar lista de eventos */
        QListWidget {
            font-size: 10pt;
            border: 1px solid #BDC3C7;
            border-radius: 4px;
            padding: 5px;
        }

        QListWidget::item {
            border-bottom: 1px solid #EAEDED;
            padding: 5px;
        }

        QListWidget::item:selected {
            background-color: #3498DB;
            color: white;
        }

        /* Mejorar calendario */
        QCalendarWidget QToolButton {
            height: 40px;
            font-size: 16pt;
            font-weight: bold;
            background-color: #34495E;
            color: white;
            padding: 5px 10px;
        }

        QCalendarWidget QMenu {
            font-size: 14pt;
        }

        QCalendarWidget QSpinBox {
            font-size: 16pt;
            font-weight: bold;
            height: 40px;
            padding: 5px;
        }

        QCalendarWidget QWidget#qt_calendar_navigationbar {
            background-color: #34495E;
            padding: 8px;
            min-height: 50px;
        }

        QCalendarWidget QAbstractItemView {
            font-size: 11pt;
            selection-background-color: #3498DB;
            selection-color: white;
        }

        /* Hacer más grandes los botones de navegación del mes/año */
        QCalendarWidget QToolButton#qt_calendar_prevmonth,
        QCalendarWidget QToolButton#qt_calendar_nextmonth {
            font-size: 18pt;
            font-weight: bold;
            min-width: 40px;
            min-height: 40px;
        }

        /* Estilo específico para el texto del mes y año */
        QCalendarWidget QToolButton[text*="enero"],
        QCalendarWidget QToolButton[text*="febrero"],
        QCalendarWidget QToolButton[text*="marzo"],
        QCalendarWidget QToolButton[text*="abril"],
        QCalendarWidget QToolButton[text*="mayo"],
        QCalendarWidget QToolButton[text*="junio"],
        QCalendarWidget QToolButton[text*="julio"],
        QCalendarWidget QToolButton[text*="agosto"],
        QCalendarWidget QToolButton[text*="septiembre"],
        QCalendarWidget QToolButton[text*="octubre"],
        QCalendarWidget QToolButton[text*="noviembre"],
        QCalendarWidget QToolButton[text*="diciembre"] {
            font-size: 18pt;
            font-weight: bold;
            min-height: 45px;
            padding: 8px 15px;
        }

        /* Mejoras para formularios */
        QLineEdit, QTextEdit, QDateEdit, QTimeEdit, QComboBox {
            min-height: 25px;
            padding: 2px 5px;
            border: 1px solid #BDC3C7;
            border-radius: 4px;
        }

        QLineEdit:focus, QTextEdit:focus, QDateEdit:focus, QTimeEdit:focus, QComboBox:focus {
            border: 1px solid #3498DB;
        }

        /* Estilos para relojes mundiales */
        WorldClocksWidget {
            background-color: #F8F9FA;
            border: 1px solid #BDC3C7;
            border-radius: 5px;
        }

        ClockWidget {
            background-color: white;
            border: 1px solid #D5DBDB;
            border-radius: 8px;
            margin: 2px;
        }

        ClockWidget QLabel {
            color: #2C3E50;
        }

        /* Reloj digital */
        ClockWidget QLabel[objectName="time_label"] {
            font-size: 22pt;
            font-weight: bold;
            color: #2980B9;
            background-color: #EBF5FB;
            border-radius: 6px;
            padding: 12px;
            margin: 8px;
            min-height: 50px;
        }

        ClockWidget QLabel[objectName="date_label"] {
            font-size: 11pt;
            color: #5D6D7E;
            padding: 5px;
            min-height: 25px;
        }

        ClockWidget QLabel[objectName="city_label"] {
            font-size: 11pt;
            font-weight: bold;
            color: #34495E;
            background-color: #F4F6F6;
            padding: 3px;
            border-radius: 3px;
        }

        /* Botones de reloj */
        ClockWidget QPushButton {
            font-size: 9pt;
            min-height: 25px;
            padding: 2px 8px;
        }

        ClockWidget QPushButton[text*="⚙"] {
            max-width: 25px;
            max-height: 25px;
            border-radius: 12px;
            background-color: #85929E;
        }

        ClockWidget QPushButton[text*="⚙"]:hover {
            background-color: #5D6D7E;
        }

        ClockWidget QPushButton[text*="🔔"] {
            background-color: #F7DC6F;
            color: #7D6608;
        }

        ClockWidget QPushButton[text*="🔔"]:hover {
            background-color: #F4D03F;
        }

        /* Botón de cierre de reloj */
        ClockWidget QPushButton[objectName="close_btn"] {
            background-color: #E74C3C;
            color: white;
            font-weight: bold;
            border-radius: 10px;
            max-width: 20px;
            max-height: 20px;
            font-size: 12px;
        }

        ClockWidget QPushButton[objectName="close_btn"]:hover {
            background-color: #C0392B;
        }

        ClockWidget QPushButton[objectName="close_btn"]:pressed {
            background-color: #A93226;
        }

        /* ComboBox de zona horaria en relojes */
        ClockWidget QComboBox {
            font-size: 9pt;
            min-height: 20px;
            padding: 1px 3px;
        }

        /* Diálogo de alarma */
        AlarmNotificationDialog {
            background-color: #FADBD8;
            border: 3px solid #E74C3C;
        }

        AlarmNotificationDialog QLabel {
            font-weight: bold;
        }

        AlarmNotificationDialog QPushButton {
            font-size: 12pt;
            min-height: 35px;
            padding: 5px 15px;
        }

        AlarmNotificationDialog QPushButton[text*="Posponer"] {
            background-color: #F39C12;
        }

        AlarmNotificationDialog QPushButton[text*="Detener"] {
            background-color: #E74C3C;
        }

        /* Estilos para splitters (barras separadoras) */
        QSplitter::handle {
            background-color: #BDC3C7;
            border: 1px solid #95A5A6;
        }

        QSplitter::handle:horizontal {
            width: 8px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #D5DBDB, stop:1 #AEB6BF);
        }

        QSplitter::handle:vertical {
            height: 8px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #D5DBDB, stop:1 #AEB6BF);
        }

        QSplitter::handle:hover {
            background-color: #85929E;
        }

        QSplitter::handle:pressed {
            background-color: #5D6D7E;
        }

        /* Indicador visual para splitter vertical */
        QSplitter::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #D5DBDB, stop:0.5 #85929E, stop:1 #D5DBDB);
            border-radius: 2px;
            margin: 2px;
        }

        QSplitter::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #AEB6BF, stop:0.5 #5D6D7E, stop:1 #AEB6BF);
        }
    """)


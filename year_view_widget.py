#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Vista anual del calendario con todos los meses
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QScrollArea, QFrame, QGridLayout, QCalendarWidget)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QTextCharFormat, QColor


class MiniCalendarWidget(QCalendarWidget):
    """Mini calendario para la vista anual"""
    
    def __init__(self, year, month, data_manager, parent=None):
        super().__init__(parent)
        self.year = year
        self.month = month
        self.data_manager = data_manager
        
        self.setup_ui()
        self.load_events()
    
    def setup_ui(self):
        """Configurar la interfaz del mini calendario"""
        # Establecer el mes y año
        self.setCurrentPage(self.year, self.month)
        
        # Configurar tamaño
        self.setFixedSize(200, 180)
        
        # Ocultar navegación
        self.setNavigationBarVisible(False)
        
        # Estilo compacto
        self.setStyleSheet("""
            QCalendarWidget {
                background-color: white;
                border: 1px solid #D0D0D0;
                border-radius: 5px;
            }
            QCalendarWidget QAbstractItemView {
                font-size: 8pt;
                selection-background-color: #3498DB;
            }
            QCalendarWidget QWidget#qt_calendar_navigationbar {
                background-color: #3498DB;
                color: white;
            }
        """)
    
    def load_events(self):
        """Cargar eventos del mes y marcar días con eventos"""
        # Obtener todos los eventos del mes
        start_date = QDate(self.year, self.month, 1)
        end_date = QDate(self.year, self.month, start_date.daysInMonth())
        
        # Formato para días con eventos
        event_format = QTextCharFormat()
        event_format.setBackground(QColor("#E8F4FD"))
        event_format.setForeground(QColor("#2C3E50"))
        
        # Formato para días con muchos eventos
        busy_format = QTextCharFormat()
        busy_format.setBackground(QColor("#3498DB"))
        busy_format.setForeground(QColor("white"))
        
        # Revisar cada día del mes
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.toString("yyyy-MM-dd")
            events = self.data_manager.get_events_by_date(date_str)
            
            if events:
                if len(events) >= 3:
                    # Muchos eventos
                    self.setDateTextFormat(current_date, busy_format)
                else:
                    # Algunos eventos
                    self.setDateTextFormat(current_date, event_format)
            
            current_date = current_date.addDays(1)


class MonthHeaderWidget(QFrame):
    """Widget de cabecera para cada mes"""
    
    def __init__(self, year, month, parent=None):
        super().__init__(parent)
        self.year = year
        self.month = month
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurar la interfaz de la cabecera"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet("""
            QFrame {
                background-color: #34495E;
                color: white;
                border-radius: 5px 5px 0px 0px;
                margin: 0px;
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # Nombre del mes
        month_names = [
            "", "Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio",
            "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"
        ]
        
        month_label = QLabel(f"{month_names[self.month]} {self.year}")
        month_label.setFont(QFont("Arial", 10, QFont.Bold))
        month_label.setStyleSheet("color: white;")
        month_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(month_label)


class YearViewWidget(QWidget):
    """Vista anual del calendario"""
    
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.current_year = QDate.currentDate().year()
        
        self.setup_ui()
        self.load_year_view()
    
    def setup_ui(self):
        """Configurar la interfaz de la vista anual"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # Cabecera con el año
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #8E44AD;
                color: white;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 15, 10, 15)
        
        self.year_label = QLabel(str(self.current_year))
        self.year_label.setAlignment(Qt.AlignCenter)
        self.year_label.setFont(QFont("Arial", 20, QFont.Bold))
        self.year_label.setStyleSheet("color: white;")
        header_layout.addWidget(self.year_label)
        
        layout.addWidget(header_frame)
        
        # Área de scroll para los meses
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Widget contenedor de meses
        self.months_container = QWidget()
        self.months_layout = QGridLayout(self.months_container)
        self.months_layout.setSpacing(10)
        self.months_layout.setContentsMargins(10, 10, 10, 10)
        
        scroll_area.setWidget(self.months_container)
        layout.addWidget(scroll_area)
    
    def load_year_view(self):
        """Cargar la vista del año con todos los meses"""
        # Limpiar meses existentes
        while self.months_layout.count():
            child = self.months_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        # Crear widgets para cada mes (4 columnas x 3 filas)
        for month in range(1, 13):
            row = (month - 1) // 4
            col = (month - 1) % 4
            
            # Contenedor del mes
            month_container = QFrame()
            month_container.setFrameStyle(QFrame.StyledPanel)
            month_container.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #BDC3C7;
                    border-radius: 8px;
                    margin: 2px;
                }
                QFrame:hover {
                    border: 2px solid #3498DB;
                }
            """)
            
            month_layout = QVBoxLayout(month_container)
            month_layout.setContentsMargins(0, 0, 0, 0)
            month_layout.setSpacing(0)
            
            # Cabecera del mes
            month_header = MonthHeaderWidget(self.current_year, month)
            month_layout.addWidget(month_header)
            
            # Mini calendario del mes
            mini_calendar = MiniCalendarWidget(self.current_year, month, self.data_manager)
            month_layout.addWidget(mini_calendar)
            
            # Agregar al grid
            self.months_layout.addWidget(month_container, row, col)
        
        # Actualizar etiqueta del año
        self.year_label.setText(str(self.current_year))
    
    def set_year(self, year):
        """Establecer el año a mostrar"""
        self.current_year = year
        self.load_year_view()
    
    def go_to_previous_year(self):
        """Ir al año anterior"""
        self.current_year -= 1
        self.load_year_view()
    
    def go_to_next_year(self):
        """Ir al año siguiente"""
        self.current_year += 1
        self.load_year_view()
    
    def get_current_year(self):
        """Obtener el año actual"""
        return self.current_year

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para las mejoras implementadas:
- Ventana de nuevo evento amplia
- Botón eliminar del mismo tamaño que checkbox
- Pestaña AGENDA GLOBAL con eventos organizados por días
- Exportación a CSV
"""

import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QFrame, QTextEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

from agenda_global_tab import AgendaGlobalTab
from event_dialog import EventDialog
from data_manager import DataManager
from styles import apply_styles


class TestAgendaGlobalImprovementsWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔄 TRADEX BOT - Mejoras de Agenda Global")
        self.setGeometry(100, 100, 1200, 800)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("🔄 TRADEX BOT - Mejoras Implementadas")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 15px; color: #2C3E50;")
        layout.addWidget(title)
        
        # Descripción de funcionalidades
        features = QLabel(
            "✅ MEJORAS IMPLEMENTADAS:\n\n"
            "📏 EVENTOS DE UNA LÍNEA: Altura fija con márgenes mínimos\n"
            "✕ BOTÓN ELIMINAR: Mismo tamaño que checkbox (20x20px)\n"
            "🔄 DESPLAZAMIENTO: Al eliminar, eventos se mueven hacia arriba\n"
            "📋 AGENDA GLOBAL: Nueva pestaña con todos los eventos\n"
            "📅 ORGANIZACIÓN POR DÍAS: Cabeceras con día de la semana\n"
            "📊 EXPORTACIÓN CSV: Compatible con Excel\n"
            "🖼️ VENTANA AMPLIA: Diálogo de nuevo evento más grande\n"
            "🎯 FUNCIONALIDAD COMPLETA: Completar y eliminar eventos"
        )
        features.setAlignment(Qt.AlignCenter)
        features.setStyleSheet(
            "margin: 15px; color: #2980B9; font-size: 11pt; "
            "background-color: #EBF5FB; padding: 20px; border-radius: 8px;"
        )
        features.setWordWrap(True)
        layout.addWidget(features)
        
        # Panel principal con agenda global
        main_frame = QFrame()
        main_frame.setStyleSheet(
            "border: 2px solid #3498DB; border-radius: 8px; "
            "background-color: #F8F9FA; margin: 10px;"
        )
        main_layout = QVBoxLayout(main_frame)
        
        # Título de la agenda
        agenda_title = QLabel("📋 AGENDA GLOBAL - Vista de Prueba")
        agenda_title.setFont(QFont("Arial", 14, QFont.Bold))
        agenda_title.setAlignment(Qt.AlignCenter)
        agenda_title.setStyleSheet("color: #2C3E50; margin: 15px;")
        main_layout.addWidget(agenda_title)
        
        # Widget de agenda global
        from signals import ApplicationSignals
        self.app_signals = ApplicationSignals()
        self.agenda_global = AgendaGlobalTab(self.data_manager, self.app_signals)
        main_layout.addWidget(self.agenda_global)
        
        layout.addWidget(main_frame)
        
        # Panel de controles
        controls_frame = QFrame()
        controls_frame.setStyleSheet(
            "border: 1px solid #BDC3C7; border-radius: 5px; "
            "background-color: #FAFAFA; margin: 5px;"
        )
        controls_layout = QHBoxLayout(controls_frame)
        
        # Botones de acción
        btn_new_event = QPushButton("➕ Nuevo Evento (Ventana Amplia)")
        btn_new_event.setMinimumHeight(40)
        btn_new_event.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        btn_new_event.clicked.connect(self.create_new_event)
        controls_layout.addWidget(btn_new_event)
        
        btn_create_sample = QPushButton("🎯 Crear Eventos de Ejemplo")
        btn_create_sample.setMinimumHeight(40)
        btn_create_sample.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        btn_create_sample.clicked.connect(self.create_sample_events)
        controls_layout.addWidget(btn_create_sample)
        
        btn_export_csv = QPushButton("📊 Exportar a CSV")
        btn_export_csv.setMinimumHeight(40)
        btn_export_csv.setStyleSheet("""
            QPushButton {
                background-color: #9B59B6;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #8E44AD;
            }
        """)
        btn_export_csv.clicked.connect(self.export_csv)
        controls_layout.addWidget(btn_export_csv)
        
        btn_clear_all = QPushButton("🗑️ Limpiar Todos")
        btn_clear_all.setMinimumHeight(40)
        btn_clear_all.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        btn_clear_all.clicked.connect(self.clear_all_events)
        controls_layout.addWidget(btn_clear_all)
        
        layout.addWidget(controls_frame)
        
        # Log de eventos
        log_title = QLabel("📝 Log de Eventos:")
        log_title.setFont(QFont("Arial", 11, QFont.Bold))
        log_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        layout.addWidget(log_title)
        
        self.log_area = QTextEdit()
        self.log_area.setMaximumHeight(100)
        self.log_area.setPlaceholderText("Los eventos del sistema aparecerán aquí...")
        self.log_area.setStyleSheet(
            "background-color: #FAFAFA; border: 1px solid #BDC3C7; "
            "border-radius: 3px; font-family: 'Courier New'; font-size: 9pt;"
        )
        layout.addWidget(self.log_area)
        
        # Estado inicial
        self.log_event("🚀 Aplicación iniciada - Agenda Global cargada")
        self.log_event("📋 Funcionalidades: Eventos de una línea, botón eliminar 20x20px, organización por días")
    
    def create_new_event(self):
        """Crear nuevo evento con ventana amplia"""
        try:
            dialog = EventDialog(self.data_manager, self)
            self.log_event(f"📏 Ventana de evento abierta: {dialog.width()}x{dialog.height()}px")
            
            if dialog.exec_():
                event_data = dialog.get_event_data()
                event_id = self.data_manager.add_event(event_data)
                if event_id:
                    self.agenda_global.refresh_agenda()
                    self.log_event(f"➕ Nuevo evento creado: {event_data['title']}")
                else:
                    self.log_event("❌ Error creando evento")
            else:
                self.log_event("❌ Creación de evento cancelada")
                
        except Exception as e:
            self.log_event(f"❌ Error en diálogo de evento: {str(e)}")
    
    def create_sample_events(self):
        """Crear eventos de ejemplo para diferentes días"""
        try:
            today = QDate.currentDate()
            
            # Crear eventos para varios días
            sample_events = [
                # Hoy
                {
                    'title': 'Reunión de Trabajo',
                    'description': 'Reunión semanal del equipo',
                    'date': today.toString("yyyy-MM-dd"),
                    'time_start': '09:00:00',
                    'time_end': '10:30:00',
                    'all_day': 0,
                    'priority': 3,
                    'category_id': None,
                    'color': '#3498DB',
                    'completed': 0
                },
                {
                    'title': 'Almuerzo con Cliente',
                    'description': 'Presentación de propuesta',
                    'date': today.toString("yyyy-MM-dd"),
                    'time_start': '13:00:00',
                    'time_end': '14:30:00',
                    'all_day': 0,
                    'priority': 4,
                    'category_id': None,
                    'color': '#E74C3C',
                    'completed': 0
                },
                # Mañana
                {
                    'title': 'Cita Médica',
                    'description': 'Revisión anual',
                    'date': today.addDays(1).toString("yyyy-MM-dd"),
                    'time_start': '10:00:00',
                    'time_end': '11:00:00',
                    'all_day': 0,
                    'priority': 2,
                    'category_id': None,
                    'color': '#27AE60',
                    'completed': 0
                },
                {
                    'title': 'Conferencia Virtual',
                    'description': 'Evento de tecnología',
                    'date': today.addDays(1).toString("yyyy-MM-dd"),
                    'time_start': '00:00:00',
                    'time_end': '23:59:59',
                    'all_day': 1,
                    'priority': 1,
                    'category_id': None,
                    'color': '#9B59B6',
                    'completed': 0
                },
                # Pasado mañana
                {
                    'title': 'Cumpleaños de Ana',
                    'description': 'Celebración familiar',
                    'date': today.addDays(2).toString("yyyy-MM-dd"),
                    'time_start': '19:00:00',
                    'time_end': '22:00:00',
                    'all_day': 0,
                    'priority': 2,
                    'category_id': None,
                    'color': '#F39C12',
                    'completed': 0
                }
            ]
            
            created_count = 0
            for event_data in sample_events:
                event_id = self.data_manager.add_event(event_data)
                if event_id:
                    created_count += 1
            
            self.agenda_global.refresh_agenda()
            self.log_event(f"🎯 {created_count} eventos de ejemplo creados en diferentes días")
            
        except Exception as e:
            self.log_event(f"❌ Error creando eventos de ejemplo: {str(e)}")
    
    def export_csv(self):
        """Exportar agenda a CSV"""
        try:
            self.agenda_global.export_to_csv()
            self.log_event("📊 Exportación a CSV iniciada")
        except Exception as e:
            self.log_event(f"❌ Error en exportación CSV: {str(e)}")
    
    def clear_all_events(self):
        """Limpiar todos los eventos"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self,
                "Confirmar Limpieza",
                "¿Está seguro de eliminar TODOS los eventos?\n\n"
                "⚠️ Esta acción no se puede deshacer.",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                all_events = self.data_manager.get_all_events()
                deleted_count = 0
                
                for event in all_events:
                    if self.data_manager.delete_event(event['id']):
                        deleted_count += 1
                
                self.agenda_global.refresh_agenda()
                self.log_event(f"🗑️ {deleted_count} eventos eliminados")
                
        except Exception as e:
            self.log_event(f"❌ Error limpiando eventos: {str(e)}")
    
    def log_event(self, message):
        """Agregar evento al log"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {message}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestAgendaGlobalImprovementsWindow()
    window.show()
    
    sys.exit(app.exec_())

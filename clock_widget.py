#!/usr/bin/env python
# -*- coding: utf-8 -*-

import math
import datetime
import pytz
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QComboBox, QFrame, QMenu, QAction)
from PyQt5.QtCore import Qt, QTimer, QTime, QPoint, pyqtSignal
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont, QPolygon


class ClockWidget(QFrame):
    """Widget que muestra un reloj digital con zona horaria configurable"""

    alarm_triggered = pyqtSignal(str, str)  # timezone, alarm_time
    close_requested = pyqtSignal(object)  # self - señal para solicitar cierre

    def __init__(self, timezone="Europe/Madrid", city_name="Madrid", parent=None):
        super().__init__(parent)
        self.timezone = timezone
        self.city_name = city_name
        self.clock_type = "digital"  # Solo digital
        self.alarms = []  # Lista de alarmas activas

        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(1)
        # Mantener tamaño fijo de los relojes individuales
        self.setMinimumSize(220, 180)
        self.setMaximumSize(280, 250)

        self.setup_ui()
        self.setup_timer()

    def setup_ui(self):
        """Configurar la interfaz del reloj"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Encabezado con nombre de ciudad y controles
        header_layout = QHBoxLayout()

        self.city_label = QLabel(self.city_name)
        self.city_label.setObjectName("city_label")
        self.city_label.setAlignment(Qt.AlignCenter)
        self.city_label.setFont(QFont("Arial", 10, QFont.Bold))
        header_layout.addWidget(self.city_label)

        # Espaciador para empujar el botón de cierre a la derecha
        header_layout.addStretch()

        # Botón de cierre
        self.close_btn = QPushButton("✕")
        self.close_btn.setObjectName("close_btn")
        self.close_btn.setMaximumSize(20, 20)
        self.close_btn.setToolTip("Eliminar este reloj")
        self.close_btn.clicked.connect(self.request_close)
        header_layout.addWidget(self.close_btn)

        layout.addLayout(header_layout)

        # Área del reloj (solo digital)
        self.time_label = QLabel()
        self.time_label.setObjectName("time_label")
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setFont(QFont("Arial", 20, QFont.Bold))
        # Restaurar altura mínima original para mantener tamaño fijo
        self.time_label.setMinimumHeight(50)
        self.time_label.setStyleSheet("padding: 10px; margin: 5px;")
        layout.addWidget(self.time_label)

        self.date_label = QLabel()
        self.date_label.setObjectName("date_label")
        self.date_label.setAlignment(Qt.AlignCenter)
        self.date_label.setFont(QFont("Arial", 11))
        # Restaurar altura mínima original para mantener tamaño fijo
        self.date_label.setMinimumHeight(25)
        layout.addWidget(self.date_label)

        # Selector de zona horaria
        self.timezone_combo = QComboBox()
        self.populate_timezones()
        self.timezone_combo.currentTextChanged.connect(self.change_timezone)
        layout.addWidget(self.timezone_combo)

        # Botón de alarma
        self.alarm_btn = QPushButton("🔔 Alarma")
        self.alarm_btn.clicked.connect(self.configure_alarm)
        layout.addWidget(self.alarm_btn)

    def request_close(self):
        """Solicitar el cierre de este reloj"""
        self.close_requested.emit(self)

    def populate_timezones(self):
        """Poblar el combo con zonas horarias principales"""
        timezones = [
            ("Madrid", "Europe/Madrid"),
            ("São Paulo", "America/Sao_Paulo"),
            ("Tokio", "Asia/Tokyo"),
            ("Londres", "Europe/London"),
            ("Sydney", "Australia/Sydney"),
            ("Nueva York", "America/New_York"),
            ("Los Ángeles", "America/Los_Angeles"),
            ("Singapur", "Asia/Singapore"),
            ("París", "Europe/Paris"),
            ("Berlín", "Europe/Berlin"),
            ("Moscú", "Europe/Moscow"),
            ("Dubai", "Asia/Dubai"),
            ("Hong Kong", "Asia/Hong_Kong"),
            ("Mumbai", "Asia/Kolkata"),
            ("Ciudad de México", "America/Mexico_City"),
            ("Buenos Aires", "America/Argentina/Buenos_Aires"),
            ("Cairo", "Africa/Cairo"),
            ("Johannesburgo", "Africa/Johannesburg"),
        ]

        for city, tz in timezones:
            self.timezone_combo.addItem(city, tz)

        # Seleccionar la zona horaria actual
        index = self.timezone_combo.findData(self.timezone)
        if index >= 0:
            self.timezone_combo.setCurrentIndex(index)

    def setup_timer(self):
        """Configurar el timer para actualizar el reloj"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # Actualizar cada segundo
        self.update_time()  # Actualización inicial

    def update_time(self):
        """Actualizar la hora mostrada"""
        try:
            # Obtener la hora actual en la zona horaria especificada
            tz = pytz.timezone(self.timezone)
            now = datetime.datetime.now(tz)

            # Formato digital
            time_str = now.strftime("%H:%M:%S")
            date_str = now.strftime("%d/%m/%Y")

            self.time_label.setText(time_str)
            self.date_label.setText(date_str)

            # Verificar alarmas
            self.check_alarms(now)

        except Exception as e:
            print(f"Error actualizando reloj {self.city_name}: {e}")



    def change_timezone(self, city_name):
        """Cambiar la zona horaria del reloj"""
        timezone_data = self.timezone_combo.currentData()
        if timezone_data:
            self.timezone = timezone_data
            self.city_name = city_name
            self.city_label.setText(city_name)
            self.update_time()



    def configure_alarm(self):
        """Configurar una alarma para este reloj"""
        from alarm_dialog import AlarmDialog
        dialog = AlarmDialog(self.timezone, self.city_name, self)
        if dialog.exec_():
            alarm_data = dialog.get_alarm_data()
            self.alarms.append(alarm_data)
            print(f"Alarma configurada para {self.city_name}: {alarm_data}")

    def check_alarms(self, current_time):
        """Verificar si alguna alarma debe activarse"""
        current_time_str = current_time.strftime("%H:%M")

        for alarm in self.alarms[:]:  # Copia de la lista para poder modificarla
            if alarm['time'] == current_time_str and alarm['enabled']:
                self.alarm_triggered.emit(self.timezone, alarm['time'])
                # Si es una alarma de una sola vez, eliminarla
                if not alarm.get('repeat', False):
                    self.alarms.remove(alarm)

    def get_config(self):
        """Obtener la configuración actual del reloj"""
        return {
            'timezone': self.timezone,
            'city_name': self.city_name,
            'clock_type': 'digital',  # Siempre digital
            'alarms': self.alarms
        }

    def set_config(self, config):
        """Establecer la configuración del reloj"""
        self.timezone = config.get('timezone', self.timezone)
        self.city_name = config.get('city_name', self.city_name)
        # Ignorar clock_type ya que siempre es digital
        self.alarms = config.get('alarms', [])

        # Actualizar la interfaz
        self.city_label.setText(self.city_name)
        # Actualizar el combo de zona horaria
        index = self.timezone_combo.findData(self.timezone)
        if index >= 0:
            self.timezone_combo.setCurrentIndex(index)
        self.update_time()

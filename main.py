#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QAction,
                             QMessageBox, QFileDialog, QMenu, QSystemTrayIcon)
from PyQt5.QtCore import Qt, QSettings, QTimer
from PyQt5.QtGui import QIcon

from data_manager import DataManager
from signals import ApplicationSignals
from calendar_tab import CalendarTab
from reminders_tab import ReminderTab
from notes_tab import NotesTab
from settings_dialog import SettingsDialog
from notification_service import NotificationService
from styles import apply_styles

class CalendarApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CYPHER Calendar")
        self.setGeometry(100, 100, 1000, 700)

        # Inicializar configuración
        self.settings = QSettings("CYPHERSoft", "CalendarApp")
        self.restoreGeometry(self.settings.value("geometry", bytes()))
        self.restoreState(self.settings.value("windowState", bytes()))

        # Inicializar señales de la aplicación
        self.app_signals = ApplicationSignals()

        # Inicializar gestión de datos
        self.data_manager = DataManager()

        # Inicializar el servicio de notificaciones
        self.notification_service = NotificationService(self.data_manager, self.app_signals)

        # Configurar la interfaz
        self.setup_ui()
        self.setup_menu()
        self.setup_tray_icon()

        # Iniciar el servicio de notificaciones
        self.notification_service.start()

        # Mostrar la ventana
        self.show()

    def setup_ui(self):
        """Configurar la interfaz de usuario principal"""
        # Crear el widget de pestañas
        self.tabs = QTabWidget()
        self.setCentralWidget(self.tabs)

        # Inicializar módulos con acceso al gestor de datos y señales
        self.tab_calendar = CalendarTab(self.data_manager, self.app_signals)
        self.tab_reminders = ReminderTab(self.data_manager, self.app_signals)
        self.tab_notes = NotesTab(self.data_manager, self.app_signals)

        # Añadir pestañas
        self.tabs.addTab(self.tab_calendar, "Calendario")
        self.tabs.addTab(self.tab_reminders, "Recordatorios")
        self.tabs.addTab(self.tab_notes, "Notas")

        # Conectar señales para actualización entre pestañas
        self.app_signals.event_added.connect(self.tab_reminders.refresh_reminders)
        self.app_signals.event_modified.connect(self.tab_reminders.refresh_reminders)
        self.app_signals.event_deleted.connect(self.tab_reminders.refresh_reminders)
        self.app_signals.reminder_triggered.connect(self.show_reminder_notification)

    def setup_menu(self):
        """Configurar el menú de la aplicación"""
        # Menú Archivo
        menu_file = self.menuBar().addMenu("&Archivo")

        # Acción Exportar
        export_action = QAction("&Exportar datos...", self)
        export_action.setShortcut("Ctrl+E")
        export_action.triggered.connect(self.export_data)
        menu_file.addAction(export_action)

        # Acción Importar
        import_action = QAction("&Importar datos...", self)
        import_action.setShortcut("Ctrl+I")
        import_action.triggered.connect(self.import_data)
        menu_file.addAction(import_action)

        menu_file.addSeparator()

        # Acción Configuración
        settings_action = QAction("&Configuración", self)
        settings_action.setShortcut("Ctrl+P")
        settings_action.triggered.connect(self.show_settings)
        menu_file.addAction(settings_action)

        menu_file.addSeparator()

        # Acción Salir
        exit_action = QAction("&Salir", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        menu_file.addAction(exit_action)

        # Menú Ayuda
        menu_help = self.menuBar().addMenu("&Ayuda")

        # Acción Acerca de
        about_action = QAction("&Acerca de", self)
        about_action.triggered.connect(self.show_about)
        menu_help.addAction(about_action)

    def setup_tray_icon(self):
        """Configurar el icono de bandeja del sistema"""
        self.tray_icon = QSystemTrayIcon(self)
        self.tray_icon.setIcon(QIcon("icons/calendar.png"))

        # Menú para el icono de bandeja
        tray_menu = QMenu()

        show_action = QAction("Mostrar", self)
        show_action.triggered.connect(self.show)
        tray_menu.addAction(show_action)

        tray_menu.addSeparator()

        exit_action = QAction("Salir", self)
        exit_action.triggered.connect(self.close)
        tray_menu.addAction(exit_action)

        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)
        self.tray_icon.show()

    def tray_icon_activated(self, reason):
        """Manejar la activación del icono de bandeja"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show()
            self.activateWindow()

    def export_data(self):
        """Exportar los datos de la aplicación"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Exportar datos", "", "Archivo ZIP (*.zip)"
        )
        if file_path:
            success = self.data_manager.export_data(file_path)
            if success:
                QMessageBox.information(self, "Exportación completada",
                    "Los datos se han exportado correctamente.")
            else:
                QMessageBox.warning(self, "Error en exportación",
                    "No se pudieron exportar los datos.")

    def import_data(self):
        """Importar datos a la aplicación"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Importar datos", "", "Archivo ZIP (*.zip)"
        )
        if file_path:
            confirm = QMessageBox.question(
                self, "Confirmar importación",
                "La importación reemplazará todos los datos actuales. ¿Desea continuar?",
                QMessageBox.Yes | QMessageBox.No
            )
            if confirm == QMessageBox.Yes:
                success = self.data_manager.import_data(file_path)
                if success:
                    # Actualizar todas las pestañas
                    self.tab_calendar.refresh_events()
                    self.tab_reminders.refresh_reminders()
                    self.tab_notes.refresh_notes()
                    QMessageBox.information(self, "Importación completada",
                        "Los datos se han importado correctamente.")
                else:
                    QMessageBox.warning(self, "Error en importación",
                        "No se pudieron importar los datos.")

    def show_settings(self):
        """Mostrar el diálogo de configuración"""
        dialog = SettingsDialog(self.data_manager)
        if dialog.exec_():
            # Actualizar la configuración de la aplicación
            self.notification_service.update_settings()

    def show_about(self):
        """Mostrar el diálogo 'Acerca de'"""
        QMessageBox.about(self, "Acerca de CYPHER Calendar",
            "CYPHER Calendar v1.0\n\n"
            "Una aplicación de calendario, recordatorios y notas.\n\n"
            "Desarrollado por CYPHER")

    def show_reminder_notification(self, event_id):
        """Mostrar una notificación para un recordatorio"""
        event = self.data_manager.get_event(event_id)
        if event:
            self.tray_icon.showMessage(
                "Recordatorio: " + event['title'],
                event['description'],
                QSystemTrayIcon.Information,
                10000  # 10 segundos
            )

    def closeEvent(self, event):
        """Controlar el cierre de la aplicación"""
        # Guardar la geometría de la ventana
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())

        # Minimizar a bandeja en lugar de cerrar si está habilitado
        minimize_to_tray = self.data_manager.get_config("minimize_to_tray", False)
        if minimize_to_tray and not QApplication.instance().isSavingSession():
            event.ignore()
            self.hide()
            self.tray_icon.showMessage(
                "CYPHER Calendar",
                "La aplicación sigue ejecutándose en segundo plano.",
                QSystemTrayIcon.Information,
                3000
            )
        else:
            # Detener el servicio de notificaciones
            self.notification_service.stop()
            event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setApplicationName("CYPHER Calendar")
    app.setQuitOnLastWindowClosed(False)  # Para permitir que se ejecute en segundo plano

    # Establecer el estilo de la aplicación
    app.setStyle("Fusion")

    # Aplicar estilos personalizados
    apply_styles(app)

    window = CalendarApp()
    sys.exit(app.exec_())



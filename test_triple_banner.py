#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para el widget triple banner:
- Cuenta atrás (izquierda)
- Banner (centro) 
- Cronómetro (derecha)
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QFrame, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from triple_banner_widget import TripleBannerWidget
from data_manager import DataManager
from styles import apply_styles


class TestTripleBannerWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔄 TRADEX BOT - Prueba de Widget Triple Banner")
        self.setGeometry(100, 100, 1200, 600)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("🔄 TRADEX BOT - Widget Triple Banner")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 15px; color: #2C3E50;")
        layout.addWidget(title)
        
        # Descripción de funcionalidades
        features = QLabel(
            "✅ WIDGET TRIPLE BANNER IMPLEMENTADO:\n\n"
            "⏱️ ZONA IZQUIERDA - CUENTA ATRÁS:\n"
            "• Configurar tiempo (horas, minutos, segundos)\n"
            "• Iniciar/pausar cuenta atrás\n"
            "• Alarma cuando termina el tiempo\n"
            "• Reiniciar contador\n\n"
            "🖼️ ZONA CENTRO - BANNER:\n"
            "• Banner tal como está diseñado\n"
            "• Configuración de imagen y URL\n"
            "• Altura configurable\n\n"
            "⏱️ ZONA DERECHA - CRONÓMETRO:\n"
            "• Iniciar/pausar cronómetro\n"
            "• Registrar vueltas\n"
            "• Precisión de milisegundos\n"
            "• Reiniciar cronómetro"
        )
        features.setAlignment(Qt.AlignCenter)
        features.setStyleSheet(
            "margin: 15px; color: #2980B9; font-size: 11pt; "
            "background-color: #EBF5FB; padding: 20px; border-radius: 8px;"
        )
        features.setWordWrap(True)
        layout.addWidget(features)
        
        # Panel principal con widget triple
        main_frame = QFrame()
        main_frame.setStyleSheet(
            "border: 2px solid #3498DB; border-radius: 8px; "
            "background-color: #F8F9FA; margin: 10px;"
        )
        main_layout = QVBoxLayout(main_frame)
        
        # Título del widget triple
        triple_title = QLabel("🔄 Widget Triple Banner - Vista de Prueba")
        triple_title.setFont(QFont("Arial", 14, QFont.Bold))
        triple_title.setAlignment(Qt.AlignCenter)
        triple_title.setStyleSheet("color: #2C3E50; margin: 15px;")
        main_layout.addWidget(triple_title)
        
        # Widget triple banner
        self.triple_banner = TripleBannerWidget(self.data_manager)
        self.triple_banner.setFixedHeight(100)  # Altura de prueba
        main_layout.addWidget(self.triple_banner)
        
        layout.addWidget(main_frame)
        
        # Panel de controles
        controls_frame = QFrame()
        controls_frame.setStyleSheet(
            "border: 1px solid #BDC3C7; border-radius: 5px; "
            "background-color: #FAFAFA; margin: 5px;"
        )
        controls_layout = QHBoxLayout(controls_frame)
        
        # Botones de prueba
        btn_test_countdown = QPushButton("⏱️ Probar Cuenta Atrás")
        btn_test_countdown.setMinimumHeight(40)
        btn_test_countdown.setStyleSheet("""
            QPushButton {
                background-color: #E67E22;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #D35400;
            }
        """)
        btn_test_countdown.clicked.connect(self.test_countdown)
        controls_layout.addWidget(btn_test_countdown)
        
        btn_test_banner = QPushButton("🖼️ Configurar Banner")
        btn_test_banner.setMinimumHeight(40)
        btn_test_banner.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        btn_test_banner.clicked.connect(self.test_banner)
        controls_layout.addWidget(btn_test_banner)
        
        btn_test_stopwatch = QPushButton("⏱️ Probar Cronómetro")
        btn_test_stopwatch.setMinimumHeight(40)
        btn_test_stopwatch.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        btn_test_stopwatch.clicked.connect(self.test_stopwatch)
        controls_layout.addWidget(btn_test_stopwatch)
        
        btn_adjust_height = QPushButton("📐 Ajustar Altura")
        btn_adjust_height.setMinimumHeight(40)
        btn_adjust_height.setStyleSheet("""
            QPushButton {
                background-color: #9B59B6;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #8E44AD;
            }
        """)
        btn_adjust_height.clicked.connect(self.adjust_height)
        controls_layout.addWidget(btn_adjust_height)
        
        layout.addWidget(controls_frame)
        
        # Log de eventos
        log_title = QLabel("📝 Log de Eventos:")
        log_title.setFont(QFont("Arial", 11, QFont.Bold))
        log_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        layout.addWidget(log_title)
        
        self.log_area = QTextEdit()
        self.log_area.setMaximumHeight(100)
        self.log_area.setPlaceholderText("Los eventos del sistema aparecerán aquí...")
        self.log_area.setStyleSheet(
            "background-color: #FAFAFA; border: 1px solid #BDC3C7; "
            "border-radius: 3px; font-family: 'Courier New'; font-size: 9pt;"
        )
        layout.addWidget(self.log_area)
        
        # Conectar señales del widget triple
        self.triple_banner.countdown_widget.countdown_finished.connect(self.on_countdown_finished)
        self.triple_banner.stopwatch_widget.time_updated.connect(self.on_stopwatch_updated)
        
        # Estado inicial
        self.log_event("🚀 Aplicación de prueba iniciada")
        self.log_event("🔄 Widget triple banner cargado con 3 zonas")
        self.log_event("⏱️ Cuenta atrás y cronómetro listos para usar")
        
        # Variables para pruebas
        self.current_height = 100
    
    def test_countdown(self):
        """Probar funcionalidades de cuenta atrás"""
        try:
            countdown = self.triple_banner.get_countdown_widget()
            
            # Simular configuración de 10 segundos
            countdown.remaining_seconds = 10
            countdown.update_display()
            
            self.log_event("⏱️ Cuenta atrás configurada para 10 segundos")
            self.log_event("▶️ Use los botones del widget para iniciar/pausar/reiniciar")
            
        except Exception as e:
            self.log_event(f"❌ Error probando cuenta atrás: {str(e)}")
    
    def test_banner(self):
        """Probar configuración del banner"""
        try:
            banner = self.triple_banner.get_banner_widget()
            banner.configure_banner()
            self.log_event("🖼️ Diálogo de configuración del banner abierto")
            
        except Exception as e:
            self.log_event(f"❌ Error configurando banner: {str(e)}")
    
    def test_stopwatch(self):
        """Probar funcionalidades del cronómetro"""
        try:
            stopwatch = self.triple_banner.get_stopwatch_widget()
            
            if not stopwatch.is_running:
                stopwatch.toggle_stopwatch()
                self.log_event("⏱️ Cronómetro iniciado")
            else:
                stopwatch.toggle_stopwatch()
                self.log_event("⏸️ Cronómetro pausado")
            
        except Exception as e:
            self.log_event(f"❌ Error probando cronómetro: {str(e)}")
    
    def adjust_height(self):
        """Ajustar altura del widget triple"""
        try:
            # Alternar entre diferentes alturas
            heights = [67, 100, 150, 200]
            current_index = heights.index(self.current_height) if self.current_height in heights else 0
            next_index = (current_index + 1) % len(heights)
            new_height = heights[next_index]
            
            self.triple_banner.adjust_layout_for_banner_height(new_height)
            self.current_height = new_height
            
            self.log_event(f"📐 Altura ajustada a {new_height}px")
            
        except Exception as e:
            self.log_event(f"❌ Error ajustando altura: {str(e)}")
    
    def on_countdown_finished(self):
        """Manejar cuando termina la cuenta atrás"""
        self.log_event("🔔 ¡CUENTA ATRÁS TERMINADA! Alarma sonando")
    
    def on_stopwatch_updated(self, elapsed_ms):
        """Manejar actualización del cronómetro"""
        # Solo loguear cada 5 segundos para no saturar
        if elapsed_ms % 5000 < 50:  # Aproximadamente cada 5 segundos
            seconds = elapsed_ms / 1000.0
            self.log_event(f"⏱️ Cronómetro: {seconds:.1f} segundos")
    
    def log_event(self, message):
        """Agregar evento al log"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {message}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestTripleBannerWindow()
    window.show()
    
    sys.exit(app.exec_())

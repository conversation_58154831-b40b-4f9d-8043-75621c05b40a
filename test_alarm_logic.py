#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar la lógica corregida de alarmas:
- Sonido simultáneo con ventana popup
- Posposición con tiempo configurable
- Detención correcta del sonido
"""

import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QTimeEdit, QSpinBox)
from PyQt5.QtCore import Qt, QTime, QTimer

from world_clocks_widget import WorldClocksWidget
from clock_widget import ClockWidget
from alarm_dialog import AlarmNotificationDialog
from sound_manager import sound_manager
from data_manager import DataManager
from styles import apply_styles


class TestAlarmLogicWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Lógica de Alarmas Corregida")
        self.setGeometry(100, 100, 900, 600)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Lógica de Alarmas")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "🔧 CORRECCIONES IMPLEMENTADAS:\n"
            "✅ Sonido inicia SIMULTÁNEAMENTE con la ventana popup\n"
            "✅ Opciones de posposición: 1, 5, 10, 15, 30 minutos\n"
            "✅ Sonido se detiene al posponer o finalizar\n"
            "✅ Alarmas pospuestas se programan automáticamente\n"
            "✅ Control completo de hilos de sonido\n\n"
            "🎯 PRUEBA: Configura una alarma para 1-2 minutos en el futuro"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #27AE60; font-size: 10pt; background-color: #F0F8F0; padding: 10px; border-radius: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Widget de relojes mundiales
        self.world_clocks = WorldClocksWidget(self.data_manager)
        layout.addWidget(self.world_clocks)
        
        # Controles de prueba
        test_controls = QHBoxLayout()
        
        # Prueba de alarma inmediata
        test_controls.addWidget(QLabel("Prueba rápida:"))
        
        self.test_time_edit = QTimeEdit()
        current_time = QTime.currentTime().addSecs(60)  # 1 minuto en el futuro
        self.test_time_edit.setTime(current_time)
        test_controls.addWidget(self.test_time_edit)
        
        btn_quick_alarm = QPushButton("Crear Alarma de Prueba")
        btn_quick_alarm.clicked.connect(self.create_test_alarm)
        test_controls.addWidget(btn_quick_alarm)
        
        test_controls.addStretch()
        
        # Prueba de sonido
        btn_test_sound = QPushButton("Probar Sonido")
        btn_test_sound.clicked.connect(self.test_sound)
        test_controls.addWidget(btn_test_sound)
        
        btn_stop_sound = QPushButton("Detener Sonido")
        btn_stop_sound.clicked.connect(self.stop_sound)
        test_controls.addWidget(btn_stop_sound)
        
        layout.addLayout(test_controls)
        
        # Información de estado
        self.status_label = QLabel("Estado: Listo para pruebas")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("margin: 10px; color: #2980B9; font-size: 12pt; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        # Timer para actualizar estado
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # Actualizar cada segundo
    
    def create_test_alarm(self):
        """Crear una alarma de prueba"""
        test_time = self.test_time_edit.time().toString("HH:mm")
        
        # Buscar el primer reloj disponible
        if self.world_clocks.clocks:
            first_clock = self.world_clocks.clocks[0]
            
            # Crear alarma de prueba
            test_alarm = {
                'time': test_time,
                'enabled': True,
                'repeat': False,
                'days': [],
                'sound': 'default',
                'volume': 70,
                'duration': 10,  # 10 segundos para prueba
                'message': f'Alarma de prueba para {test_time}',
                'timezone': first_clock.timezone,
                'city': first_clock.city_name
            }
            
            # Agregar al reloj
            first_clock.alarms.append(test_alarm)
            self.world_clocks.save_clocks_config()
            
            self.status_label.setText(f"✅ Alarma de prueba creada para {test_time} en {first_clock.city_name}")
            print(f"Alarma de prueba creada: {test_alarm}")
        else:
            self.status_label.setText("❌ No hay relojes disponibles")
    
    def test_sound(self):
        """Probar el sonido directamente"""
        self.status_label.setText("🔔 Probando sonido...")
        sound_manager.play_alarm_sound("default", volume=50, duration=5)
    
    def stop_sound(self):
        """Detener el sonido"""
        self.status_label.setText("🔇 Deteniendo sonido...")
        sound_manager.stop_all_sounds()
    
    def update_status(self):
        """Actualizar información de estado"""
        current_time = QTime.currentTime().toString("HH:mm:ss")
        
        # Contar alarmas activas
        total_alarms = 0
        for clock in self.world_clocks.clocks:
            total_alarms += len([alarm for alarm in clock.alarms if alarm['enabled']])
        
        if total_alarms > 0:
            self.status_label.setText(f"⏰ Hora actual: {current_time} | Alarmas activas: {total_alarms}")
        else:
            self.status_label.setText(f"🕐 Hora actual: {current_time} | Sin alarmas activas")
    
    def test_alarm_dialog_directly(self):
        """Probar el diálogo de alarma directamente"""
        test_alarm_data = {
            'time': '12:00',
            'enabled': True,
            'repeat': False,
            'days': [],
            'sound': 'default',
            'volume': 70,
            'duration': 15,
            'message': 'Esta es una prueba directa del diálogo de alarma',
            'timezone': 'Europe/Madrid',
            'city': 'Madrid'
        }
        
        # Mostrar diálogo con sonido
        alarm_dialog = AlarmNotificationDialog(test_alarm_data, sound_manager, self)
        result = alarm_dialog.exec_()
        
        # Mostrar resultado
        action_result = alarm_dialog.get_result()
        print(f"Resultado de la alarma: {action_result}")
        
        if action_result['action'] == 'snooze':
            self.status_label.setText(f"⏰ Alarma pospuesta {action_result['snooze_minutes']} minutos")
        elif action_result['action'] == 'stop':
            self.status_label.setText("🔇 Alarma detenida")
        else:
            self.status_label.setText("❓ Acción desconocida")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestAlarmLogicWindow()
    window.show()
    
    # Agregar botón para prueba directa del diálogo
    direct_test_btn = QPushButton("Probar Diálogo Directamente")
    direct_test_btn.clicked.connect(window.test_alarm_dialog_directly)
    window.layout().addWidget(direct_test_btn)
    
    sys.exit(app.exec_())

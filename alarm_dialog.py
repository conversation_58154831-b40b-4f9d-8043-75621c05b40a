#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QTimeEdit, QCheckBox, QPushButton, QComboBox,
                            QGroupBox, QFormLayout, QSpinBox, QTextEdit,
                            QDialogButtonBox)
from PyQt5.QtCore import Qt, QTime
from PyQt5.QtGui import QFont


class AlarmDialog(QDialog):
    """Diálogo para configurar alarmas de reloj"""

    def __init__(self, timezone, city_name, parent=None):
        super().__init__(parent)
        self.timezone = timezone
        self.city_name = city_name

        self.setWindowTitle(f"Configurar Alarma - {city_name}")
        self.setModal(True)
        self.setMinimumSize(400, 300)

        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)

        # Título
        title_label = QLabel(f"Nueva alarma para {self.city_name}")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Grupo de configuración de tiempo
        time_group = QGroupBox("Hora de la alarma")
        time_layout = QFormLayout(time_group)

        self.time_edit = QTimeEdit()
        self.time_edit.setTime(QTime.currentTime())
        self.time_edit.setDisplayFormat("HH:mm")
        time_layout.addRow("Hora:", self.time_edit)

        layout.addWidget(time_group)

        # Grupo de opciones
        options_group = QGroupBox("Opciones")
        options_layout = QFormLayout(options_group)

        # Repetir alarma
        self.repeat_check = QCheckBox("Repetir diariamente")
        options_layout.addRow("", self.repeat_check)

        # Días de la semana (solo si se repite)
        self.days_layout = QHBoxLayout()
        self.day_checks = {}
        days = ["Lun", "Mar", "Mié", "Jue", "Vie", "Sáb", "Dom"]
        for day in days:
            check = QCheckBox(day)
            self.day_checks[day] = check
            self.days_layout.addWidget(check)

        self.days_widget = QGroupBox("Días de repetición")
        self.days_widget.setLayout(self.days_layout)
        self.days_widget.setEnabled(False)
        options_layout.addRow("", self.days_widget)

        # Conectar el checkbox de repetir con los días
        self.repeat_check.toggled.connect(self.days_widget.setEnabled)

        # Sonido de alarma
        self.sound_combo = QComboBox()
        self.sound_combo.addItem("Sonido por defecto", "default")
        self.sound_combo.addItem("Campana", "bell")
        self.sound_combo.addItem("Beep", "beep")
        self.sound_combo.addItem("Chime", "chime")
        options_layout.addRow("Sonido:", self.sound_combo)

        # Volumen
        self.volume_spin = QSpinBox()
        self.volume_spin.setRange(1, 100)
        self.volume_spin.setValue(50)
        self.volume_spin.setSuffix("%")
        options_layout.addRow("Volumen:", self.volume_spin)

        # Duración de la alarma
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(5, 300)
        self.duration_spin.setValue(30)
        self.duration_spin.setSuffix(" segundos")
        options_layout.addRow("Duración:", self.duration_spin)

        layout.addWidget(options_group)

        # Grupo de mensaje
        message_group = QGroupBox("Mensaje de alarma")
        message_layout = QVBoxLayout(message_group)

        self.message_edit = QTextEdit()
        self.message_edit.setPlaceholderText("Mensaje opcional para mostrar con la alarma...")
        self.message_edit.setMaximumHeight(80)
        message_layout.addWidget(self.message_edit)

        layout.addWidget(message_group)

        # Botones
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        layout.addWidget(button_box)

    def get_alarm_data(self):
        """Obtener los datos de la alarma configurada"""
        # Obtener días seleccionados
        selected_days = []
        if self.repeat_check.isChecked():
            for day, check in self.day_checks.items():
                if check.isChecked():
                    selected_days.append(day)

        return {
            'time': self.time_edit.time().toString("HH:mm"),
            'enabled': True,
            'repeat': self.repeat_check.isChecked(),
            'days': selected_days,
            'sound': self.sound_combo.currentData(),
            'volume': self.volume_spin.value(),
            'duration': self.duration_spin.value(),
            'message': self.message_edit.toPlainText().strip(),
            'timezone': self.timezone,
            'city': self.city_name
        }


class AlarmNotificationDialog(QDialog):
    """Diálogo que se muestra cuando se activa una alarma"""

    def __init__(self, alarm_data, sound_manager=None, parent=None):
        super().__init__(parent)
        self.alarm_data = alarm_data
        self.sound_manager = sound_manager
        self.snooze_minutes = 5  # Tiempo de posposición por defecto
        self.result_action = None  # 'stop', 'snooze', o None

        self.setWindowTitle("¡ALARMA!")
        self.setModal(True)
        self.setMinimumSize(400, 280)

        # Hacer que la ventana aparezca siempre encima
        self.setWindowFlags(Qt.Dialog | Qt.WindowStaysOnTopHint)

        self.setup_ui()
        self.setup_timer()

        # Iniciar sonido inmediatamente
        self.start_alarm_sound()

    def setup_ui(self):
        """Configurar la interfaz de la notificación"""
        layout = QVBoxLayout(self)

        # Título de alarma
        title_label = QLabel("🔔 ALARMA ACTIVADA 🔔")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: red; background-color: yellow; padding: 10px;")
        layout.addWidget(title_label)

        # Información de la alarma
        info_label = QLabel(f"Hora: {self.alarm_data['time']}\nCiudad: {self.alarm_data['city']}")
        info_label.setFont(QFont("Arial", 12))
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)

        # Mensaje personalizado si existe
        if self.alarm_data.get('message'):
            message_label = QLabel(self.alarm_data['message'])
            message_label.setFont(QFont("Arial", 10))
            message_label.setAlignment(Qt.AlignCenter)
            message_label.setWordWrap(True)
            message_label.setStyleSheet("background-color: lightblue; padding: 5px; border-radius: 5px;")
            layout.addWidget(message_label)

        # Opciones de posposición
        snooze_group = QGroupBox("Opciones de posposición")
        snooze_layout = QHBoxLayout(snooze_group)

        snooze_layout.addWidget(QLabel("Posponer por:"))

        self.snooze_combo = QComboBox()
        self.snooze_combo.addItem("1 minuto", 1)
        self.snooze_combo.addItem("5 minutos", 5)
        self.snooze_combo.addItem("10 minutos", 10)
        self.snooze_combo.addItem("15 minutos", 15)
        self.snooze_combo.addItem("30 minutos", 30)
        self.snooze_combo.setCurrentIndex(1)  # 5 minutos por defecto
        self.snooze_combo.currentTextChanged.connect(self.update_snooze_time)
        snooze_layout.addWidget(self.snooze_combo)

        layout.addWidget(snooze_group)

        # Botones principales
        button_layout = QHBoxLayout()

        self.snooze_btn = QPushButton("🕐 Posponer (5 min)")
        self.snooze_btn.clicked.connect(self.snooze_alarm)
        self.snooze_btn.setStyleSheet("font-size: 12pt; padding: 8px;")
        button_layout.addWidget(self.snooze_btn)

        self.stop_btn = QPushButton("🔇 Detener Alarma")
        self.stop_btn.clicked.connect(self.stop_alarm)
        self.stop_btn.setDefault(True)
        self.stop_btn.setStyleSheet("font-size: 12pt; padding: 8px;")
        button_layout.addWidget(self.stop_btn)

        layout.addLayout(button_layout)

    def setup_timer(self):
        """Configurar timer para auto-cerrar la alarma"""
        from PyQt5.QtCore import QTimer

        duration = self.alarm_data.get('duration', 30) * 1000  # Convertir a milisegundos
        self.auto_close_timer = QTimer()
        self.auto_close_timer.timeout.connect(self.stop_alarm)
        self.auto_close_timer.start(duration)

    def start_alarm_sound(self):
        """Iniciar el sonido de la alarma"""
        if self.sound_manager:
            sound_type = self.alarm_data.get('sound', 'default')
            volume = self.alarm_data.get('volume', 50)
            duration = self.alarm_data.get('duration', 30)

            print(f"🔔 Iniciando sonido de alarma: {sound_type} al {volume}% de volumen")
            self.sound_manager.play_alarm_sound(sound_type, volume, duration)

    def stop_alarm_sound(self):
        """Detener el sonido de la alarma"""
        if self.sound_manager:
            print("🔇 Deteniendo sonido de alarma")
            self.sound_manager.stop_all_sounds()

    def update_snooze_time(self):
        """Actualizar el tiempo de posposición seleccionado"""
        self.snooze_minutes = self.snooze_combo.currentData()
        self.snooze_btn.setText(f"🕐 Posponer ({self.snooze_minutes} min)")

    def snooze_alarm(self):
        """Posponer la alarma por el tiempo seleccionado"""
        self.snooze_minutes = self.snooze_combo.currentData()
        self.result_action = 'snooze'

        print(f"Alarma pospuesta {self.snooze_minutes} minutos")

        # Detener sonido
        self.stop_alarm_sound()

        # Detener timer de auto-cierre
        if hasattr(self, 'auto_close_timer'):
            self.auto_close_timer.stop()

        self.accept()

    def stop_alarm(self):
        """Detener la alarma completamente"""
        self.result_action = 'stop'

        print("Alarma detenida por el usuario")

        # Detener sonido
        self.stop_alarm_sound()

        # Detener timer de auto-cierre
        if hasattr(self, 'auto_close_timer'):
            self.auto_close_timer.stop()

        self.accept()

    def get_result(self):
        """Obtener el resultado de la acción del usuario"""
        return {
            'action': self.result_action,
            'snooze_minutes': self.snooze_minutes if self.result_action == 'snooze' else None
        }

    def closeEvent(self, event):
        """Manejar el cierre de la ventana"""
        # Si se cierra la ventana sin acción específica, detener la alarma
        if self.result_action is None:
            self.result_action = 'stop'
            self.stop_alarm_sound()

        if hasattr(self, 'auto_close_timer'):
            self.auto_close_timer.stop()

        event.accept()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget de cronómetro con funcionalidad de iniciar, pausar y reiniciar
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont


class StopwatchWidget(QFrame):
    """Widget de cronómetro"""

    # Señales
    time_updated = pyqtSignal(int)  # Emite el tiempo transcurrido en milisegundos

    def __init__(self, parent=None):
        super().__init__(parent)
        self.elapsed_milliseconds = 0
        self.is_running = False

        # Timer para el cronómetro (actualización cada 10ms para precisión)
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_stopwatch)

        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz del widget"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setLineWidth(1)
        self.setStyleSheet("""
            StopwatchWidget {
                background-color: #FFFFFF;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
            }
            StopwatchWidget:hover {
                border: 2px solid #27AE60;
                background-color: #E8F8F5;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(8)

        # Título
        title_label = QLabel("⏱️ CRONÓMETRO")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 11, QFont.Bold))
        title_label.setStyleSheet("color: #27AE60; margin: 3px;")
        layout.addWidget(title_label)

        # Display del tiempo mejorado
        self.time_display = QLabel("00:00:00.0")
        self.time_display.setAlignment(Qt.AlignCenter)
        self.time_display.setFont(QFont("Arial", 18, QFont.Bold))
        self.time_display.setStyleSheet("color: #2C3E50; background-color: #F8F9FA; padding: 8px; border-radius: 5px;")
        layout.addWidget(self.time_display)

        # Botones mejorados
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)

        self.start_stop_button = QPushButton("▶️ INICIAR")
        self.start_stop_button.setFixedSize(80, 32)
        self.start_stop_button.setToolTip("Iniciar cronómetro")
        self.start_stop_button.clicked.connect(self.toggle_stopwatch)
        self.start_stop_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #229954;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #1E8449;
            }
        """)
        buttons_layout.addWidget(self.start_stop_button)

        self.lap_button = QPushButton("📍")
        self.lap_button.setFixedSize(35, 32)
        self.lap_button.setToolTip("Registrar vuelta")
        self.lap_button.clicked.connect(self.record_lap)
        self.lap_button.setEnabled(False)
        self.lap_button.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #E67E22;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #D35400;
            }
            QPushButton:disabled {
                background-color: #BDC3C7;
                transform: none;
            }
        """)
        buttons_layout.addWidget(self.lap_button)

        self.reset_button = QPushButton("🔄")
        self.reset_button.setFixedSize(35, 32)
        self.reset_button.setToolTip("Reiniciar cronómetro")
        self.reset_button.clicked.connect(self.reset_stopwatch)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #C0392B;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
        """)
        buttons_layout.addWidget(self.reset_button)

        layout.addLayout(buttons_layout)

        # Tiempo de última vuelta mejorado
        self.lap_display = QLabel("")
        self.lap_display.setAlignment(Qt.AlignCenter)
        self.lap_display.setFont(QFont("Arial", 9, QFont.Bold))
        self.lap_display.setStyleSheet("""
            color: #7F8C8D;
            background-color: #F8F9FA;
            padding: 4px;
            border-radius: 3px;
            margin: 2px;
        """)
        self.lap_display.hide()  # Oculto por defecto
        layout.addWidget(self.lap_display)

        # Variables para vueltas
        self.lap_count = 0
        self.last_lap_time = 0

    def toggle_stopwatch(self):
        """Iniciar/pausar el cronómetro"""
        if self.is_running:
            # Pausar
            self.timer.stop()
            self.is_running = False
            self.start_stop_button.setText("▶️ CONTINUAR")
            self.start_stop_button.setToolTip("Continuar cronómetro")
            self.lap_button.setEnabled(False)
        else:
            # Iniciar
            self.timer.start(10)  # Actualizar cada 10ms
            self.is_running = True
            self.start_stop_button.setText("⏸️ PAUSAR")
            self.start_stop_button.setToolTip("Pausar cronómetro")
            self.lap_button.setEnabled(True)

    def reset_stopwatch(self):
        """Reiniciar el cronómetro"""
        self.timer.stop()
        self.is_running = False
        self.elapsed_milliseconds = 0
        self.lap_count = 0
        self.last_lap_time = 0

        self.update_display()
        self.start_stop_button.setText("▶️ INICIAR")
        self.start_stop_button.setToolTip("Iniciar cronómetro")
        self.lap_button.setEnabled(False)
        self.lap_display.hide()

    def record_lap(self):
        """Registrar una vuelta"""
        if self.is_running:
            self.lap_count += 1
            current_time = self.elapsed_milliseconds
            lap_time = current_time - self.last_lap_time
            self.last_lap_time = current_time

            # Mostrar tiempo de la vuelta
            lap_seconds = lap_time / 1000.0
            minutes = int(lap_seconds // 60)
            seconds = lap_seconds % 60

            lap_text = f"🏁 Vuelta {self.lap_count}: {minutes:02d}:{seconds:06.3f}"
            self.lap_display.setText(lap_text)
            self.lap_display.show()

            # Efecto visual temporal
            self.lap_display.setStyleSheet("""
                color: #27AE60;
                background-color: #E8F8F5;
                padding: 4px;
                border-radius: 3px;
                margin: 2px;
                border: 1px solid #27AE60;
            """)

            # Restaurar estilo normal después de 2 segundos
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(2000, self.restore_lap_display_style)

    def update_stopwatch(self):
        """Actualizar el cronómetro"""
        self.elapsed_milliseconds += 10  # Incrementar 10ms
        self.update_display()
        self.time_updated.emit(self.elapsed_milliseconds)

    def restore_lap_display_style(self):
        """Restaurar el estilo normal del display de vueltas"""
        self.lap_display.setStyleSheet("""
            color: #7F8C8D;
            background-color: #F8F9FA;
            padding: 4px;
            border-radius: 3px;
            margin: 2px;
        """)

    def update_display(self):
        """Actualizar el display del tiempo"""
        total_seconds = self.elapsed_milliseconds / 1000.0

        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = total_seconds % 60

        # Formato: HH:MM:SS.d (con 1 decimal)
        time_str = f"{hours:02d}:{minutes:02d}:{seconds:06.3f}"
        self.time_display.setText(time_str)

        # Cambiar color según el tiempo transcurrido
        if total_seconds > 3600:  # Más de 1 hora
            self.time_display.setStyleSheet(
                "color: #E74C3C; background-color: #F8F9FA; padding: 8px; border-radius: 5px; font-weight: bold; font-size: 18pt;"
            )
        elif total_seconds > 600:  # Más de 10 minutos
            self.time_display.setStyleSheet(
                "color: #F39C12; background-color: #F8F9FA; padding: 8px; border-radius: 5px; font-weight: bold; font-size: 18pt;"
            )
        else:  # Menos de 10 minutos
            self.time_display.setStyleSheet(
                "color: #2C3E50; background-color: #F8F9FA; padding: 8px; border-radius: 5px; font-size: 18pt;"
            )

    def get_elapsed_time(self):
        """Obtener el tiempo transcurrido en milisegundos"""
        return self.elapsed_milliseconds

    def get_elapsed_time_formatted(self):
        """Obtener el tiempo transcurrido formateado"""
        total_seconds = self.elapsed_milliseconds / 1000.0
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = total_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}"

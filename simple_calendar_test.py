#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Versión simplificada para probar el calendario con vistas
"""

import sys
import os

# Verificar que estamos en el directorio correcto
print(f"Directorio actual: {os.getcwd()}")
print(f"Archivos en directorio: {os.listdir('.')}")

try:
    # Importaciones básicas
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                                QWidget, QLabel, QPushButton, QFrame)
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    
    print("✅ PyQt5 importado correctamente")
    
    # Verificar si existen nuestros archivos
    required_files = [
        'data_manager.py',
        'custom_calendar.py', 
        'calendar_view_selector.py',
        'day_view_widget.py',
        'week_view_widget.py',
        'year_view_widget.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Archivos faltantes: {missing_files}")
    else:
        print("✅ Todos los archivos necesarios están presentes")
    
    class SimpleCalendarWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("🗓️ Calendario con Vistas - Prueba Simple")
            self.setGeometry(100, 100, 800, 600)
            
            # Widget central
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # Layout principal
            main_layout = QHBoxLayout(central_widget)
            
            # Panel izquierdo simulado
            left_panel = QFrame()
            left_panel.setFrameStyle(QFrame.StyledPanel)
            left_panel.setStyleSheet("background-color: #F0F0F0; border: 1px solid #CCC;")
            left_panel.setMaximumWidth(120)
            
            left_layout = QVBoxLayout(left_panel)
            
            # Título
            title = QLabel("VISTAS")
            title.setAlignment(Qt.AlignCenter)
            title.setFont(QFont("Arial", 10, QFont.Bold))
            left_layout.addWidget(title)
            
            # Botones simulados
            buttons = ["📆 DÍA", "📅 SEMANA", "🗓️ MES", "📊 AÑO"]
            for btn_text in buttons:
                btn = QPushButton(btn_text)
                btn.setMinimumHeight(50)
                btn.clicked.connect(lambda checked, text=btn_text: self.view_clicked(text))
                left_layout.addWidget(btn)
            
            left_layout.addStretch()
            main_layout.addWidget(left_panel)
            
            # Panel central
            center_panel = QFrame()
            center_panel.setFrameStyle(QFrame.StyledPanel)
            center_panel.setStyleSheet("background-color: white; border: 1px solid #CCC;")
            
            center_layout = QVBoxLayout(center_panel)
            
            # Navegación
            nav_layout = QHBoxLayout()
            nav_layout.addWidget(QPushButton("◀ Anterior"))
            
            self.period_label = QLabel("Diciembre 2024")
            self.period_label.setAlignment(Qt.AlignCenter)
            self.period_label.setFont(QFont("Arial", 14, QFont.Bold))
            nav_layout.addWidget(self.period_label)
            
            nav_layout.addWidget(QPushButton("Siguiente ▶"))
            center_layout.addLayout(nav_layout)
            
            # Área de calendario
            self.calendar_area = QLabel("Área del calendario\n\nSelecciona una vista del panel izquierdo")
            self.calendar_area.setAlignment(Qt.AlignCenter)
            self.calendar_area.setStyleSheet("background-color: #F8F8F8; border: 1px dashed #CCC; padding: 20px;")
            center_layout.addWidget(self.calendar_area)
            
            main_layout.addWidget(center_panel)
            
            print("✅ Ventana creada correctamente")
        
        def view_clicked(self, view_text):
            self.calendar_area.setText(f"Vista seleccionada: {view_text}\n\nEsta es una simulación.\nLa implementación real está en los archivos creados.")
            print(f"Vista seleccionada: {view_text}")
    
    if __name__ == "__main__":
        print("Iniciando aplicación...")
        app = QApplication(sys.argv)
        
        window = SimpleCalendarWindow()
        window.show()
        
        print("✅ Aplicación iniciada. Ventana visible.")
        print("Presiona Ctrl+C en la terminal para cerrar.")
        
        sys.exit(app.exec_())

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

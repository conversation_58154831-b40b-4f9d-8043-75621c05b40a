#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar las mejoras del sistema de eventos:
- Sistema de prioridades con colores
- Botón de categoría corregido
- Ordenamiento por prioridad y hora
- Edición al hacer clic en eventos
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QTextEdit)
from PyQt5.QtCore import Qt

from event_dialog import EventDialog
from data_manager import DataManager
from styles import apply_styles


class TestEventImprovementsWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Mejoras del Sistema de Eventos")
        self.setGeometry(100, 100, 900, 700)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Mejoras del Sistema de Eventos")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "🎯 MEJORAS IMPLEMENTADAS:\n\n"
            "✅ SISTEMA DE PRIORIDADES:\n"
            "• 🟢 Normal (Verde) - Prioridad 1\n"
            "• 🟡 Poco Importante (Amarillo) - Prioridad 2\n"
            "• 🟠 Importante (Naranja) - Prioridad 3\n"
            "• 🔴 Muy Importante (Rojo) - Prioridad 4\n\n"
            "✅ ORDENAMIENTO INTELIGENTE:\n"
            "• Los eventos se ordenan por prioridad (mayor a menor)\n"
            "• Dentro de la misma prioridad, por hora de inicio\n\n"
            "✅ INTERFAZ MEJORADA:\n"
            "• Botón '+ Nueva Categoría' con tamaño adecuado\n"
            "• Indicadores visuales de prioridad en la lista\n"
            "• Edición al hacer clic en eventos\n\n"
            "🔧 PRUEBA: Crea eventos con diferentes prioridades y observa el ordenamiento"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #2980B9; font-size: 10pt; background-color: #EBF5FB; padding: 15px; border-radius: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Botones de prueba
        buttons_layout = QHBoxLayout()
        
        btn_create_normal = QPushButton("🟢 Crear Evento Normal")
        btn_create_normal.setMinimumSize(150, 50)
        btn_create_normal.setStyleSheet("font-size: 11pt; background-color: #27AE60; color: white; font-weight: bold;")
        btn_create_normal.clicked.connect(lambda: self.create_test_event(1))
        buttons_layout.addWidget(btn_create_normal)
        
        btn_create_low = QPushButton("🟡 Crear Poco Importante")
        btn_create_low.setMinimumSize(150, 50)
        btn_create_low.setStyleSheet("font-size: 11pt; background-color: #F39C12; color: white; font-weight: bold;")
        btn_create_low.clicked.connect(lambda: self.create_test_event(2))
        buttons_layout.addWidget(btn_create_low)
        
        btn_create_high = QPushButton("🟠 Crear Importante")
        btn_create_high.setMinimumSize(150, 50)
        btn_create_high.setStyleSheet("font-size: 11pt; background-color: #E67E22; color: white; font-weight: bold;")
        btn_create_high.clicked.connect(lambda: self.create_test_event(3))
        buttons_layout.addWidget(btn_create_high)
        
        btn_create_critical = QPushButton("🔴 Crear Muy Importante")
        btn_create_critical.setMinimumSize(150, 50)
        btn_create_critical.setStyleSheet("font-size: 11pt; background-color: #E74C3C; color: white; font-weight: bold;")
        btn_create_critical.clicked.connect(lambda: self.create_test_event(4))
        buttons_layout.addWidget(btn_create_critical)
        
        layout.addLayout(buttons_layout)
        
        # Botones adicionales
        extra_buttons_layout = QHBoxLayout()
        
        btn_test_dialog = QPushButton("📝 Probar Diálogo Completo")
        btn_test_dialog.setMinimumSize(200, 40)
        btn_test_dialog.setStyleSheet("font-size: 11pt; background-color: #9B59B6; color: white; font-weight: bold;")
        btn_test_dialog.clicked.connect(self.test_full_dialog)
        extra_buttons_layout.addWidget(btn_test_dialog)
        
        btn_show_events = QPushButton("📋 Mostrar Eventos de Hoy")
        btn_show_events.setMinimumSize(200, 40)
        btn_show_events.setStyleSheet("font-size: 11pt; background-color: #34495E; color: white; font-weight: bold;")
        btn_show_events.clicked.connect(self.show_today_events)
        extra_buttons_layout.addWidget(btn_show_events)
        
        layout.addLayout(extra_buttons_layout)
        
        # Área de resultados
        self.results_area = QTextEdit()
        self.results_area.setMaximumHeight(200)
        self.results_area.setPlaceholderText("Los resultados de las pruebas aparecerán aquí...")
        layout.addWidget(self.results_area)
        
        # Información técnica
        tech_info = QLabel(
            "📋 INFORMACIÓN TÉCNICA:\n\n"
            "🗄️ BASE DE DATOS:\n"
            "• Campo 'priority' agregado a la tabla events\n"
            "• Valores: 1=Normal, 2=Poco Importante, 3=Importante, 4=Muy Importante\n"
            "• Ordenamiento: ORDER BY priority DESC, time_start\n\n"
            "🎨 INTERFAZ:\n"
            "• Botón de categoría redimensionado (120px mínimo)\n"
            "• Indicadores de prioridad con emojis de colores\n"
            "• Sección de prioridad con radio buttons\n\n"
            "🔧 FUNCIONALIDADES:\n"
            "• Método update_event_priority() para cambios rápidos\n"
            "• Ordenamiento automático por prioridad y hora\n"
            "• Edición de eventos al hacer clic"
        )
        tech_info.setAlignment(Qt.AlignLeft)
        tech_info.setStyleSheet("margin: 10px; color: #2C3E50; font-size: 9pt; background-color: #F8F9FA; padding: 10px; border-radius: 5px;")
        tech_info.setWordWrap(True)
        layout.addWidget(tech_info)
    
    def create_test_event(self, priority):
        """Crear un evento de prueba con la prioridad especificada"""
        from datetime import datetime, timedelta
        import random
        
        priority_names = {1: "Normal", 2: "Poco Importante", 3: "Importante", 4: "Muy Importante"}
        priority_colors = {1: "#27AE60", 2: "#F39C12", 3: "#E67E22", 4: "#E74C3C"}
        
        # Generar hora aleatoria
        base_time = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
        random_hours = random.randint(0, 8)
        random_minutes = random.randint(0, 59)
        event_time = base_time + timedelta(hours=random_hours, minutes=random_minutes)
        
        # Crear datos del evento
        event_data = {
            'title': f'Evento {priority_names[priority]} - {event_time.strftime("%H:%M")}',
            'date': datetime.now().strftime("%Y-%m-%d"),
            'time_start': event_time.strftime("%H:%M:%S"),
            'time_end': (event_time + timedelta(hours=1)).strftime("%H:%M:%S"),
            'all_day': 0,
            'category_id': None,
            'reminder': None,
            'color': priority_colors[priority],
            'description': f'Este es un evento de prueba con prioridad {priority_names[priority]}. '
                          f'Creado automáticamente para probar el sistema de ordenamiento.',
            'priority': priority
        }
        
        # Guardar en la base de datos
        event_id = self.data_manager.add_event(event_data)
        
        if event_id:
            self.results_area.append(f"✅ Evento creado: {event_data['title']} (ID: {event_id}, Prioridad: {priority})")
            self.results_area.append(f"   Hora: {event_data['time_start']} - {event_data['time_end']}")
            self.results_area.append("")
        else:
            self.results_area.append(f"❌ Error al crear evento con prioridad {priority}")
    
    def test_full_dialog(self):
        """Probar el diálogo completo con todas las mejoras"""
        dialog = EventDialog(self.data_manager, self)
        
        if dialog.exec_():
            event_data = dialog.get_event_data()
            event_id = self.data_manager.add_event(event_data)
            
            if event_id:
                priority_names = {1: "Normal", 2: "Poco Importante", 3: "Importante", 4: "Muy Importante"}
                priority_name = priority_names.get(event_data.get('priority', 1), "Desconocida")
                
                self.results_area.append(f"✅ Evento creado desde diálogo:")
                self.results_area.append(f"   Título: {event_data['title']}")
                self.results_area.append(f"   Prioridad: {priority_name} ({event_data.get('priority', 1)})")
                self.results_area.append(f"   Fecha: {event_data['date']}")
                self.results_area.append(f"   Categoría ID: {event_data.get('category_id', 'Sin categoría')}")
                self.results_area.append("")
            else:
                self.results_area.append("❌ Error al guardar evento desde diálogo")
        else:
            self.results_area.append("ℹ️ Creación de evento cancelada")
    
    def show_today_events(self):
        """Mostrar eventos de hoy ordenados por prioridad"""
        from datetime import datetime
        
        today = datetime.now().strftime("%Y-%m-%d")
        events = self.data_manager.get_events_by_date(today)
        
        self.results_area.append(f"📅 EVENTOS DE HOY ({today}):")
        self.results_area.append("=" * 50)
        
        if not events:
            self.results_area.append("No hay eventos para hoy")
        else:
            priority_names = {1: "Normal", 2: "Poco Importante", 3: "Importante", 4: "Muy Importante"}
            priority_icons = {1: "🟢", 2: "🟡", 3: "🟠", 4: "🔴"}
            
            for i, event in enumerate(events, 1):
                priority = event.get('priority', 1)
                priority_name = priority_names.get(priority, "Desconocida")
                priority_icon = priority_icons.get(priority, "🟢")
                
                time_str = f"{event.get('time_start', '')} - {event.get('time_end', '')}"
                if event.get('all_day', 0):
                    time_str = "Todo el día"
                
                self.results_area.append(f"{i}. {priority_icon} {event['title']}")
                self.results_area.append(f"   Prioridad: {priority_name} | Hora: {time_str}")
                
                if event.get('description'):
                    desc_preview = event['description'][:60] + "..." if len(event['description']) > 60 else event['description']
                    self.results_area.append(f"   Descripción: {desc_preview}")
                
                self.results_area.append("")
        
        self.results_area.append("=" * 50)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestEventImprovementsWindow()
    window.show()
    
    sys.exit(app.exec_())

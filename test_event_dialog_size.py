#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar el tamaño inicial de la ventana de evento
equivalente a 5x5 cuadrados de días del calendario
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget,
                            QLabel, QPushButton, QHBoxLayout, QFrame, QTextEdit,
                            QGridLayout, QGroupBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from event_dialog import EventDialog
from data_manager import DataManager
from styles import apply_styles


class TestEventDialogSizeWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔍 TRADEX BOT - Prueba de Tamaño de Ventana de Evento")
        self.setGeometry(100, 100, 1000, 700)

        # Crear data manager
        self.data_manager = DataManager()

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # Título
        title = QLabel("🔍 TRADEX BOT - Prueba de Tamaño de Ventana de Evento")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 15px; color: #2C3E50;")
        layout.addWidget(title)

        # Información sobre el tamaño
        info_frame = QGroupBox("📐 Información de Dimensiones")
        info_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498DB;
                border-radius: 8px;
                margin: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #2C3E50;
            }
        """)
        info_layout = QVBoxLayout(info_frame)

        # Información de dimensiones
        calendar_info = QLabel(
            "📊 TAMAÑO DE VENTANA ESPECIFICADO:\n\n"
            "🖼️ VENTANA DE EVENTO:\n"
            "• Ancho: 1600 píxeles\n"
            "• Alto: 1200 píxeles\n"
            "• Tamaño mínimo: 800×600 píxeles\n"
            "• Redimensionable: Sí\n\n"
            "📐 CARACTERÍSTICAS:\n"
            "• Formato: 4:3 (1600÷1200 = 1.33)\n"
            "• Área total: 1,920,000 píxeles\n"
            "• Tamaño amplio para contenido completo\n\n"
            "✅ TAMAÑO INICIAL: 1600px × 1200px"
        )
        calendar_info.setStyleSheet(
            "color: #2C3E50; font-size: 11pt; padding: 15px; "
            "background-color: #F8F9FA; border-radius: 5px; margin: 5px;"
        )
        calendar_info.setWordWrap(True)
        info_layout.addWidget(calendar_info)

        layout.addWidget(info_frame)

        # Panel de pruebas
        test_frame = QFrame()
        test_frame.setStyleSheet(
            "border: 2px solid #27AE60; border-radius: 8px; "
            "background-color: #F8F9FA; margin: 10px;"
        )
        test_layout = QVBoxLayout(test_frame)

        # Título del panel de pruebas
        test_title = QLabel("🧪 Panel de Pruebas")
        test_title.setFont(QFont("Arial", 14, QFont.Bold))
        test_title.setAlignment(Qt.AlignCenter)
        test_title.setStyleSheet("color: #27AE60; margin: 15px;")
        test_layout.addWidget(test_title)

        # Botones de prueba
        buttons_layout = QHBoxLayout()

        btn_new_event = QPushButton("➕ Nuevo Evento (5x5 Cuadrados)")
        btn_new_event.setMinimumHeight(50)
        btn_new_event.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                padding: 15px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        btn_new_event.clicked.connect(self.test_new_event_dialog)
        buttons_layout.addWidget(btn_new_event)

        btn_edit_event = QPushButton("✏️ Editar Evento (5x5 Cuadrados)")
        btn_edit_event.setMinimumHeight(50)
        btn_edit_event.setStyleSheet("""
            QPushButton {
                background-color: #E67E22;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                padding: 15px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #D35400;
            }
        """)
        btn_edit_event.clicked.connect(self.test_edit_event_dialog)
        buttons_layout.addWidget(btn_edit_event)

        btn_multiple_tests = QPushButton("🔄 Múltiples Pruebas")
        btn_multiple_tests.setMinimumHeight(50)
        btn_multiple_tests.setStyleSheet("""
            QPushButton {
                background-color: #9B59B6;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                padding: 15px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #8E44AD;
            }
        """)
        btn_multiple_tests.clicked.connect(self.test_multiple_dialogs)
        buttons_layout.addWidget(btn_multiple_tests)

        test_layout.addLayout(buttons_layout)
        layout.addWidget(test_frame)

        # Comparación visual
        comparison_frame = QGroupBox("📊 Comparación de Tamaños")
        comparison_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #E74C3C;
                border-radius: 8px;
                margin: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #2C3E50;
            }
        """)
        comparison_layout = QGridLayout(comparison_frame)

        # Comparación de tamaños
        sizes_info = [
            ("Pequeño", "800×600px", "#27AE60"),
            ("Mediano", "1024×768px", "#F39C12"),
            ("Grande", "1280×960px", "#E67E22"),
            ("Ventana Evento", "1600×1200px", "#E74C3C")
        ]

        for i, (label, size, color) in enumerate(sizes_info):
            size_widget = QFrame()
            size_widget.setStyleSheet(f"""
                QFrame {{
                    background-color: {color};
                    border: 2px solid #2C3E50;
                    border-radius: 5px;
                    margin: 5px;
                }}
            """)
            size_widget.setFixedSize(80, 60)

            size_label = QLabel(f"{label}\n{size}")
            size_label.setAlignment(Qt.AlignCenter)
            size_label.setStyleSheet("color: #2C3E50; font-weight: bold; font-size: 9pt;")

            comparison_layout.addWidget(size_widget, 0, i)
            comparison_layout.addWidget(size_label, 1, i)

        layout.addWidget(comparison_frame)

        # Log de resultados
        log_title = QLabel("📝 Log de Pruebas:")
        log_title.setFont(QFont("Arial", 12, QFont.Bold))
        log_title.setStyleSheet("color: #2C3E50; margin: 10px 5px 5px 5px;")
        layout.addWidget(log_title)

        self.log_area = QTextEdit()
        self.log_area.setMaximumHeight(120)
        self.log_area.setPlaceholderText("Los resultados de las pruebas aparecerán aquí...")
        self.log_area.setStyleSheet(
            "background-color: #FAFAFA; border: 2px solid #BDC3C7; "
            "border-radius: 5px; font-family: 'Courier New'; font-size: 10pt; margin: 5px;"
        )
        layout.addWidget(self.log_area)

        # Estado inicial
        self.log_event("🚀 Aplicación de prueba iniciada")
        self.log_event("📐 Tamaño objetivo: 1600×1200px (especificación)")
        self.log_event("🔍 Listo para probar ventanas de evento")

    def test_new_event_dialog(self):
        """Probar diálogo de nuevo evento"""
        try:
            dialog = EventDialog(self.data_manager, self)

            # Obtener dimensiones
            width = dialog.width()
            height = dialog.height()

            self.log_event(f"➕ NUEVO EVENTO - Tamaño: {width}×{height}px")
            self.log_event(f"   📊 Objetivo: 1600×1200px | Diferencia: {abs(width-1600)}×{abs(height-1200)}px")

            # Mostrar diálogo
            result = dialog.exec_()
            if result:
                self.log_event("✅ Diálogo aceptado")
            else:
                self.log_event("❌ Diálogo cancelado")

        except Exception as e:
            self.log_event(f"❌ Error en nuevo evento: {str(e)}")

    def test_edit_event_dialog(self):
        """Probar diálogo de editar evento"""
        try:
            # Crear evento de ejemplo
            sample_event = {
                'id': 1,
                'title': 'Evento de Prueba',
                'description': 'Descripción de prueba para verificar tamaño',
                'date': '2025-01-15',
                'time_start': '10:00:00',
                'time_end': '11:00:00',
                'all_day': 0,
                'priority': 3,
                'category_id': None,
                'color': '#3498DB',
                'completed': 0
            }

            dialog = EventDialog(self.data_manager, self, event=sample_event)

            # Obtener dimensiones
            width = dialog.width()
            height = dialog.height()

            self.log_event(f"✏️ EDITAR EVENTO - Tamaño: {width}×{height}px")
            self.log_event(f"   📊 Objetivo: 1600×1200px | Diferencia: {abs(width-1600)}×{abs(height-1200)}px")

            # Mostrar diálogo
            result = dialog.exec_()
            if result:
                self.log_event("✅ Diálogo aceptado")
            else:
                self.log_event("❌ Diálogo cancelado")

        except Exception as e:
            self.log_event(f"❌ Error en editar evento: {str(e)}")

    def test_multiple_dialogs(self):
        """Probar múltiples diálogos para verificar consistencia"""
        try:
            self.log_event("🔄 INICIANDO PRUEBAS MÚLTIPLES...")

            sizes = []
            for i in range(3):
                dialog = EventDialog(self.data_manager, self)
                width = dialog.width()
                height = dialog.height()
                sizes.append((width, height))

                self.log_event(f"   Prueba {i+1}: {width}×{height}px")
                dialog.close()  # Cerrar sin mostrar

            # Verificar consistencia
            if all(size == sizes[0] for size in sizes):
                self.log_event("✅ CONSISTENCIA: Todos los diálogos tienen el mismo tamaño")
            else:
                self.log_event("⚠️ INCONSISTENCIA: Los diálogos tienen tamaños diferentes")

            avg_width = sum(size[0] for size in sizes) / len(sizes)
            avg_height = sum(size[1] for size in sizes) / len(sizes)
            self.log_event(f"📊 PROMEDIO: {avg_width:.0f}×{avg_height:.0f}px")

        except Exception as e:
            self.log_event(f"❌ Error en pruebas múltiples: {str(e)}")

    def log_event(self, message):
        """Agregar evento al log"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {message}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")

    # Aplicar estilos
    apply_styles(app)

    window = TestEventDialogSizeWindow()
    window.show()

    sys.exit(app.exec_())

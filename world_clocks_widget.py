#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QFrame, QScrollArea, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from clock_widget import ClockWidget
from alarm_dialog import AlarmNotificationDialog
from sound_manager import sound_manager
from banner_widget import BannerWidget


class WorldClocksWidget(QFrame):
    """Widget que contiene múltiples relojes mundiales"""

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.clocks = []

        self.setFrameStyle(QFrame.StyledPanel)
        self.setLineWidth(1)
        # Tamaño mínimo razonable para mostrar al menos un reloj
        # pero permitir redimensionamiento del contenedor
        self.setMinimumSize(250, 200)

        self.setup_ui()
        self.load_clocks_config()

    def setup_ui(self):
        """Configurar la interfaz del widget"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Título
        title_layout = QHBoxLayout()

        title_label = QLabel("🌍 Relojes Mundiales")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # Botón para agregar reloj
        self.add_clock_btn = QPushButton("+ Agregar Reloj")
        self.add_clock_btn.clicked.connect(self.add_clock)
        title_layout.addWidget(self.add_clock_btn)

        layout.addLayout(title_layout)

        # Área de scroll para los relojes
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Widget contenedor para los relojes
        self.clocks_container = QWidget()
        self.clocks_layout = QHBoxLayout(self.clocks_container)
        self.clocks_layout.setContentsMargins(5, 5, 5, 5)
        self.clocks_layout.setSpacing(10)

        scroll_area.setWidget(self.clocks_container)
        layout.addWidget(scroll_area)

        # Banner configurable en la parte inferior
        self.banner = BannerWidget(self.data_manager)
        layout.addWidget(self.banner)

        # Crear relojes por defecto
        self.create_default_clocks()

    def adjust_layout_for_banner_height(self, banner_height):
        """Ajustar el layout cuando cambia la altura del banner"""
        # Calcular nueva altura disponible para relojes
        # El banner ocupa su altura configurada, los relojes se ajustan proporcionalmente

        # Altura total disponible (asumiendo altura de calendario de 6 filas * 67px = 402px)
        total_calendar_height = 6 * 67  # 402px

        # Altura disponible para relojes = altura total - altura del banner
        available_height = total_calendar_height - banner_height

        # Los relojes deben ocupar desde la segunda fila desde abajo
        # Esto significa que deben ocupar 2 filas de altura proporcional
        clocks_target_height = min(available_height, 2 * 67)  # Máximo 2 filas

        # Ajustar altura mínima del scroll area de relojes
        if hasattr(self, 'scroll_area'):
            self.scroll_area.setMinimumHeight(clocks_target_height)
            self.scroll_area.setMaximumHeight(clocks_target_height)

        # Notificar al contenedor padre (calendar_tab) para ajustar splitter
        parent = self.parent()
        while parent:
            if hasattr(parent, 'adjust_splitter_for_banner'):
                parent.adjust_splitter_for_banner(banner_height)
                break
            parent = parent.parent()

    def create_default_clocks(self):
        """Crear los relojes por defecto (Madrid y São Paulo)"""
        # Reloj 1: Madrid
        madrid_clock = ClockWidget(
            timezone="Europe/Madrid",
            city_name="Madrid"
        )
        self.add_clock_widget(madrid_clock)

        # Reloj 2: São Paulo
        sao_paulo_clock = ClockWidget(
            timezone="America/Sao_Paulo",
            city_name="São Paulo"
        )
        self.add_clock_widget(sao_paulo_clock)

    def add_clock_widget(self, clock_widget):
        """Agregar un widget de reloj al contenedor"""
        # Conectar señales
        clock_widget.alarm_triggered.connect(self.handle_alarm)
        clock_widget.close_requested.connect(self.remove_clock)

        # Agregar al layout
        self.clocks_layout.addWidget(clock_widget)
        self.clocks.append(clock_widget)

        # Limitar el número máximo de relojes
        if len(self.clocks) >= 6:
            self.add_clock_btn.setEnabled(False)

    def add_clock(self):
        """Agregar un nuevo reloj"""
        if len(self.clocks) >= 6:
            QMessageBox.warning(self, "Límite alcanzado",
                              "No se pueden agregar más de 6 relojes.")
            return

        # Crear un nuevo reloj con configuración por defecto
        new_clock = ClockWidget(
            timezone="Europe/London",
            city_name="Londres"
        )
        self.add_clock_widget(new_clock)

        # Guardar configuración
        self.save_clocks_config()

    def remove_clock(self, clock_widget):
        """Eliminar un reloj (llamado por la señal close_requested)"""
        if len(self.clocks) <= 2:
            QMessageBox.warning(self, "Mínimo requerido",
                              "Debe mantener al menos 2 relojes.")
            return

        # Confirmar eliminación
        reply = QMessageBox.question(self, "Confirmar eliminación",
                                   f"¿Está seguro de eliminar el reloj de {clock_widget.city_name}?",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            # Remover del layout y lista
            self.clocks_layout.removeWidget(clock_widget)
            self.clocks.remove(clock_widget)
            clock_widget.deleteLater()

            # Habilitar botón de agregar si estaba deshabilitado
            if len(self.clocks) < 6:
                self.add_clock_btn.setEnabled(True)

            # Guardar configuración
            self.save_clocks_config()

    def handle_alarm(self, timezone, alarm_time):
        """Manejar cuando se activa una alarma"""
        # Encontrar el reloj que activó la alarma
        triggering_clock = None
        for clock in self.clocks:
            if clock.timezone == timezone:
                triggering_clock = clock
                break

        if triggering_clock:
            # Buscar la alarma específica
            for alarm in triggering_clock.alarms:
                if alarm['time'] == alarm_time and alarm['enabled']:
                    # Mostrar diálogo de alarma CON sonido simultáneo
                    alarm_dialog = AlarmNotificationDialog(alarm, sound_manager, self)
                    alarm_dialog.exec_()

                    # Obtener resultado de la acción del usuario
                    result = alarm_dialog.get_result()

                    if result['action'] == 'snooze':
                        # Programar nueva alarma para después del tiempo de posposición
                        self.schedule_snooze_alarm(alarm, result['snooze_minutes'])
                    elif result['action'] == 'stop':
                        # Si es una alarma de una sola vez, eliminarla
                        if not alarm.get('repeat', False):
                            triggering_clock.alarms.remove(alarm)
                            self.save_clocks_config()

                    break

    def schedule_snooze_alarm(self, original_alarm, snooze_minutes):
        """Programar una alarma pospuesta"""
        from datetime import datetime, timedelta
        import pytz

        # Calcular la nueva hora de alarma
        tz = pytz.timezone(original_alarm['timezone'])
        now = datetime.now(tz)
        snooze_time = now + timedelta(minutes=snooze_minutes)
        new_alarm_time = snooze_time.strftime("%H:%M")

        # Crear una nueva alarma temporal (no repetitiva)
        snooze_alarm = original_alarm.copy()
        snooze_alarm['time'] = new_alarm_time
        snooze_alarm['repeat'] = False  # Las alarmas pospuestas no se repiten
        snooze_alarm['enabled'] = True

        # Encontrar el reloj correspondiente y agregar la alarma pospuesta
        for clock in self.clocks:
            if clock.timezone == original_alarm['timezone']:
                clock.alarms.append(snooze_alarm)
                print(f"⏰ Alarma pospuesta para {new_alarm_time} en {clock.city_name}")
                break

        # Guardar configuración
        self.save_clocks_config()



    def get_clocks_config(self):
        """Obtener la configuración de todos los relojes"""
        config = []
        for clock in self.clocks:
            config.append(clock.get_config())
        return config

    def set_clocks_config(self, config):
        """Establecer la configuración de los relojes"""
        # Limpiar relojes existentes
        for clock in self.clocks[:]:
            self.clocks_layout.removeWidget(clock)
            clock.deleteLater()
        self.clocks.clear()

        # Crear relojes según la configuración
        for clock_config in config:
            clock = ClockWidget(
                timezone=clock_config.get('timezone', 'Europe/Madrid'),
                city_name=clock_config.get('city_name', 'Madrid')
            )
            clock.set_config(clock_config)
            self.add_clock_widget(clock)

    def save_clocks_config(self):
        """Guardar la configuración de los relojes"""
        config = self.get_clocks_config()
        self.data_manager.set_config('world_clocks', config)
        self.data_manager.save_config()

    def load_clocks_config(self):
        """Cargar la configuración de los relojes"""
        config = self.data_manager.get_config('world_clocks', None)
        if config:
            # Limpiar relojes por defecto
            for clock in self.clocks[:]:
                self.clocks_layout.removeWidget(clock)
                clock.deleteLater()
            self.clocks.clear()

            # Cargar configuración guardada
            self.set_clocks_config(config)

    def closeEvent(self, event):
        """Guardar configuración al cerrar"""
        self.save_clocks_config()
        event.accept()

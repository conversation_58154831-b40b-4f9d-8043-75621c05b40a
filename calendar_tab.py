#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QCalendarWidget, QListWidget,
                            QListWidgetItem, QPushButton, QLabel, QLineEdit, QMessageBox,
                            QMenu, QAction, QSplitter, QFrame, QToolBar, QSizePolicy, QScrollArea)
from PyQt5.QtCore import Qt, QDate, QSize, QTime, QDateTime, pyqtSlot
from PyQt5.QtGui import QIcon, QColor, QBrush, QFont

from event_dialog import EventDialog
from world_clocks_widget import WorldClocksWidget
from custom_calendar import CustomCalendarWidget
from event_item_widget import EventListWidget


class CalendarTab(QWidget):
    def __init__(self, data_manager, app_signals, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.app_signals = app_signals

        # Fecha actualmente seleccionada
        self.selected_date = QDate.currentDate()

        self.init_ui()
        self.load_events()

        # Conectar señales
        self.app_signals.event_added.connect(self.refresh_events)
        self.app_signals.event_modified.connect(self.refresh_events)
        self.app_signals.event_deleted.connect(self.refresh_events)

    def init_ui(self):
        """Inicializar la interfaz de usuario"""
        main_layout = QVBoxLayout(self)

        # Crear una barra de herramientas
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(20, 20))

        # Botón para crear nuevo evento
        new_event_action = QAction(QIcon("icons/event_add.png"), "Nuevo evento", self)
        new_event_action.triggered.connect(self.add_event)
        toolbar.addAction(new_event_action)

        toolbar.addSeparator()

        # Campo de búsqueda
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Buscar eventos...")
        self.search_edit.textChanged.connect(self.search_events)
        toolbar.addWidget(self.search_edit)

        main_layout.addWidget(toolbar)

        # Crear un divisor principal horizontal
        main_splitter = QSplitter(Qt.Horizontal)

        # Lado izquierdo: Calendario
        calendar_widget = QWidget()
        calendar_layout = QVBoxLayout(calendar_widget)
        calendar_layout.setContentsMargins(0, 0, 0, 0)

        self.calendar = CustomCalendarWidget(self.data_manager)
        self.calendar.clicked.connect(self.date_changed)

        calendar_layout.addWidget(self.calendar)

        # Lado derecho: Divisor vertical para eventos y relojes (adaptativo)
        right_splitter = QSplitter(Qt.Vertical)
        right_splitter.setHandleWidth(8)  # Hacer la barra más gruesa para facilitar el arrastre
        right_splitter.setChildrenCollapsible(True)  # Permitir colapso completo si el usuario lo desea

        # Parte superior derecha: Eventos del día
        events_widget = QWidget()
        # Remover altura mínima fija para permitir redimensionamiento completo
        events_layout = QVBoxLayout(events_widget)
        events_layout.setContentsMargins(0, 0, 0, 0)

        self.date_label = QLabel()
        self.date_label.setAlignment(Qt.AlignCenter)
        self.date_label.setFont(QFont("Arial", 12, QFont.Bold))
        events_layout.addWidget(self.date_label)

        # Crear área de scroll para los eventos
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Widget personalizado para eventos con checkboxes
        self.events_list = EventListWidget()
        self.events_list.event_completed_changed.connect(self.on_event_completed_changed)
        self.events_list.event_edit_requested.connect(self.on_event_edit_requested)
        self.events_list.event_delete_requested.connect(self.on_event_delete_requested)

        scroll_area.setWidget(self.events_list)
        events_layout.addWidget(scroll_area)

        events_buttons = QHBoxLayout()

        self.btn_add = QPushButton("Nuevo evento")
        self.btn_add.clicked.connect(self.add_event)
        events_buttons.addWidget(self.btn_add)

        self.btn_edit = QPushButton("Editar")
        self.btn_edit.clicked.connect(self.edit_event)
        self.btn_edit.setEnabled(False)
        events_buttons.addWidget(self.btn_edit)

        self.btn_delete = QPushButton("Eliminar")
        self.btn_delete.clicked.connect(self.delete_event)
        self.btn_delete.setEnabled(False)
        events_buttons.addWidget(self.btn_delete)

        events_layout.addLayout(events_buttons)

        # Parte inferior derecha: Relojes mundiales
        self.world_clocks = WorldClocksWidget(self.data_manager)
        # Remover altura mínima fija para permitir redimensionamiento completo

        # Añadir widgets al splitter vertical derecho
        right_splitter.addWidget(events_widget)
        right_splitter.addWidget(self.world_clocks)

        # Configurar el comportamiento del splitter
        right_splitter.setStretchFactor(0, 1)  # Eventos pueden estirarse
        right_splitter.setStretchFactor(1, 1)  # Relojes también pueden estirarse

        # Establecer tamaños iniciales (adaptables por el usuario)
        # La zona de relojes debe comenzar desde la segunda fila desde abajo del calendario
        # Calculamos: calendario tiene ~6 filas de 67px = ~402px total
        # Los relojes deben ocupar desde la 2da fila desde abajo = 2 * 67px = 134px
        calendar_total_height = 6 * 67  # 402px
        clocks_height = 2 * 67  # 134px (2 filas desde abajo)
        events_height = calendar_total_height - clocks_height  # 268px (4 filas superiores)

        right_splitter.setSizes([events_height, clocks_height])

        # Permitir que el usuario guarde las proporciones
        right_splitter.splitterMoved.connect(self.save_splitter_state)

        # Guardar referencia al splitter para poder restaurar su estado
        self.right_splitter = right_splitter

        # Añadir widgets al splitter principal
        main_splitter.addWidget(calendar_widget)
        main_splitter.addWidget(right_splitter)

        # Configurar el splitter principal también
        main_splitter.setHandleWidth(8)
        main_splitter.setChildrenCollapsible(False)
        main_splitter.setStretchFactor(0, 2)  # Calendario tiene más peso
        main_splitter.setStretchFactor(1, 1)  # Panel derecho tiene menos peso

        # Establecer tamaños relativos del splitter principal (60% calendario, 40% derecha)
        main_splitter.setSizes([int(self.width() * 0.6), int(self.width() * 0.4)])

        main_layout.addWidget(main_splitter)

        # Eliminar carrusel por ahora - volver al banner simple

        # Cargar estado guardado del splitter
        self.load_splitter_state()

        # Las conexiones ya están hechas en el widget personalizado

        # Establecer la fecha actual
        self.update_date_label()

    def update_date_label(self):
        """Actualizar la etiqueta de fecha con la fecha seleccionada"""
        self.date_label.setText(self.selected_date.toString("dddd, d 'de' MMMM 'de' yyyy"))

    def date_changed(self, date):
        """Manejar cambio de fecha seleccionada"""
        self.selected_date = date
        self.update_date_label()
        self.load_events()

    def load_events(self):
        """Cargar eventos para la fecha seleccionada"""
        date_str = self.selected_date.toString("yyyy-MM-dd")
        events = self.data_manager.get_events_by_date(date_str)

        # Usar el nuevo widget de eventos
        self.events_list.set_events(events)

    def on_event_completed_changed(self, event_id, completed):
        """Manejar cambio en el estado de completado de un evento"""
        success = self.data_manager.update_event_completed(event_id, completed)
        if success:
            print(f"Evento {event_id} marcado como {'completado' if completed else 'activo'}")
            # Actualizar calendario para reflejar cambios
            self.calendar.refresh_events()
        else:
            print(f"Error al actualizar estado de evento {event_id}")

    def on_event_edit_requested(self, event_id):
        """Manejar solicitud de edición de evento"""
        event = self.data_manager.get_event(event_id)
        if not event:
            return

        dialog = EventDialog(self.data_manager, self, event=event)

        if dialog.exec_():
            event_data = dialog.get_event_data()
            success = self.data_manager.update_event(event_id, event_data)
            if success:
                self.app_signals.event_modified.emit(event_id)
                self.load_events()
                self.calendar.refresh_events()

    def on_event_delete_requested(self, event_id):
        """Manejar solicitud de eliminación de evento desde el botón X"""
        # El diálogo de confirmación ya se mostró en el widget del evento
        # Aquí solo ejecutamos la eliminación
        success = self.data_manager.delete_event(event_id)
        if success:
            print(f"Evento {event_id} eliminado exitosamente")
            # Emitir señal de eliminación
            self.app_signals.event_deleted.emit(event_id)
            # Actualizar la vista
            self.load_events()
            # Actualizar el calendario (esto actualizará los números en los cuadrados)
            self.calendar.refresh_events()
        else:
            print(f"Error al eliminar evento {event_id}")
            QMessageBox.warning(
                self,
                "Error",
                "No se pudo eliminar el evento. Inténtelo de nuevo."
            )

    def enable_buttons(self):
        """Habilitar o deshabilitar botones según la selección"""
        # Con el nuevo widget, los botones siempre están habilitados
        # ya que la edición se hace por clic directo en el evento
        self.btn_edit.setEnabled(False)  # Deshabilitado porque se edita por clic
        self.btn_delete.setEnabled(False)  # Mantenemos solo el botón de agregar

    def on_event_clicked(self, item):
        """Manejar clic en un evento para abrir el diálogo de edición"""
        if item.flags() & Qt.ItemIsEnabled:  # Solo si el item está habilitado
            self.edit_event()

    def add_event(self):
        """Añadir un nuevo evento"""
        dialog = EventDialog(self.data_manager, self)
        dialog.date_edit.setDate(self.selected_date)

        if dialog.exec_():
            event_data = dialog.get_event_data()
            event_id = self.data_manager.add_event(event_data)
            if event_id:
                self.app_signals.event_added.emit(event_id)
                self.load_events()
                self.calendar.refresh_events()

    def edit_event(self):
        """Editar el evento seleccionado"""
        selected_items = self.events_list.selectedItems()
        if not selected_items:
            return

        item = selected_items[0]
        event_id = item.data(Qt.UserRole)

        if not event_id:
            return

        event = self.data_manager.get_event(event_id)
        if not event:
            return

        dialog = EventDialog(self.data_manager, self, event=event)

        if dialog.exec_():
            event_data = dialog.get_event_data()
            success = self.data_manager.update_event(event_id, event_data)
            if success:
                self.app_signals.event_modified.emit(event_id)
                self.load_events()
                self.calendar.refresh_events()

    def delete_event(self):
        """Eliminar el evento seleccionado"""
        selected_items = self.events_list.selectedItems()
        if not selected_items:
            return

        item = selected_items[0]
        event_id = item.data(Qt.UserRole)

        if not event_id:
            return

        confirm = QMessageBox.question(
            self, "Confirmar eliminación",
            "¿Está seguro de eliminar este evento?",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            success = self.data_manager.delete_event(event_id)
            if success:
                self.app_signals.event_deleted.emit(event_id)
                self.load_events()
                self.calendar.refresh_events()

    def show_context_menu(self, position):
        """Mostrar menú contextual para la lista de eventos"""
        selected_items = self.events_list.selectedItems()
        if not selected_items:
            return

        item = selected_items[0]
        event_id = item.data(Qt.UserRole)

        if not event_id:
            return

        menu = QMenu()
        edit_action = menu.addAction("Editar")
        delete_action = menu.addAction("Eliminar")

        action = menu.exec_(self.events_list.mapToGlobal(position))

        if action == edit_action:
            self.edit_event()
        elif action == delete_action:
            self.delete_event()

    def search_events(self, query):
        """Buscar eventos que coincidan con la consulta"""
        if not query:
            self.calendar.setSelectedDate(self.selected_date)
            self.load_events()
            return

        # Si hay una consulta, buscar en todos los eventos
        events = self.data_manager.search_events(query)

        self.events_list.clear()

        if not events:
            item = QListWidgetItem("No se encontraron eventos")
            item.setFlags(Qt.NoItemFlags)
            self.events_list.addItem(item)
            self.date_label.setText("Resultados de búsqueda")
            return

        self.date_label.setText(f"Resultados de búsqueda: {query}")

        for event in events:
            date_str = event.get('date', '')
            time_str = ""
            if event.get('all_day', 0):
                time_str = "Todo el día"
            else:
                start_time = event.get('time_start', '')
                end_time = event.get('time_end', '')
                if start_time and end_time:
                    time_str = f"{start_time} - {end_time}"

            item_text = f"{date_str} | {time_str}: {event['title']}"

            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, event['id'])

            # Añadir color si existe categoría
            if event.get('category_color'):
                item.setForeground(QBrush(QColor(event['category_color'])))

            self.events_list.addItem(item)

    @pyqtSlot()
    def refresh_events(self):
        """Actualizar la lista de eventos"""
        # Si estamos en modo búsqueda, actualizar resultados de búsqueda
        if self.search_edit.text():
            self.search_events(self.search_edit.text())
        else:
            self.load_events()

        # Actualizar calendario
        self.calendar.refresh_events()

    def save_splitter_state(self):
        """Guardar el estado del splitter vertical (eventos/relojes)"""
        if hasattr(self, 'right_splitter'):
            sizes = self.right_splitter.sizes()
            self.data_manager.set_config('calendar_splitter_sizes', sizes)

    def load_splitter_state(self):
        """Cargar el estado guardado del splitter vertical"""
        if hasattr(self, 'right_splitter'):
            saved_sizes = self.data_manager.get_config('calendar_splitter_sizes', None)
            if saved_sizes and len(saved_sizes) == 2:
                # Aplicar tamaños guardados sin restricciones artificiales
                # Permitir cualquier tamaño que el usuario haya configurado
                if all(size >= 0 for size in saved_sizes):  # Solo verificar que no sean negativos
                    self.right_splitter.setSizes(saved_sizes)

    def adjust_splitter_for_banner(self, banner_height):
        """Ajustar el splitter cuando cambia la altura del banner"""
        if hasattr(self, 'right_splitter'):
            # Calcular nuevas proporciones basadas en la altura del banner
            calendar_total_height = 6 * 67  # 402px total del calendario

            # El banner ocupa su altura configurada
            # Los relojes deben ocupar desde la segunda fila desde abajo
            clocks_height = min(2 * 67, calendar_total_height - banner_height)  # Máximo 2 filas
            events_height = calendar_total_height - clocks_height

            # Aplicar nuevos tamaños manteniendo proporcionalidad
            self.right_splitter.setSizes([events_height, clocks_height])

            # Guardar nueva configuración
            self.save_splitter_state()

        # Método eliminado - no hay carrusel

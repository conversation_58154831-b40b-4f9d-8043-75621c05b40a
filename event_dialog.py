#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QDialog, QFormLayout, QLabel, QLineEdit, QTextEdit,
                            QDateEdit, QTimeEdit, QComboBox, QCheckBox, QPushButton,
                            QDialogButtonBox, QVBoxLayout, QHBoxLayout, QColorDialog,
                            QGroupBox, QGridLayout, QSizePolicy, QInputDialog, QMessageBox,
                            QScrollArea, QFrame, QTabWidget, QButtonGroup, QRadioButton)
from PyQt5.QtCore import Qt, QDate, QTime
from PyQt5.QtGui import QColor, QFont


class EventDialog(QDialog):
    def __init__(self, data_manager, parent=None, event=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.event = event

        if event:
            self.setWindowTitle("Editar evento")
        else:
            self.setWindowTitle("Nuevo evento")

        # Hacer la ventana más amplia y ocupar el espacio de la zona de eventos
        self.resize(700, 600)
        self.setMinimumSize(600, 500)

        # Permitir redimensionamiento libre
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Crear categorías por defecto si no existen
        self.ensure_default_categories()

        self.init_ui()

        # Si estamos editando un evento, rellenar los campos
        if event:
            self.fill_form()

    def ensure_default_categories(self):
        """Crear categorías por defecto si no existen"""
        existing_categories = self.data_manager.get_all_categories()
        existing_names = [cat['name'] for cat in existing_categories]

        default_categories = [
            ("Importante", "#E74C3C"),      # Rojo
            ("Negocios", "#3498DB"),        # Azul
            ("Familia", "#E67E22"),         # Naranja
            ("Médico", "#27AE60"),          # Verde
            ("Personal", "#9B59B6"),        # Púrpura
            ("Trabajo", "#34495E"),         # Gris oscuro
            ("Educación", "#F39C12"),       # Amarillo
            ("Viajes", "#1ABC9C"),          # Turquesa
        ]

        for name, color in default_categories:
            if name not in existing_names:
                self.data_manager.add_category(name, color)

    def init_ui(self):
        """Inicializar la interfaz de usuario mejorada"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)

        # Título de la ventana con estilo
        title_label = QLabel("📅 " + ("Editar Evento" if self.event else "Nuevo Evento"))
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2C3E50; margin: 10px; padding: 10px;")
        main_layout.addWidget(title_label)

        # Crear área de scroll para el contenido
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Widget contenedor del formulario
        form_widget = QFrame()
        form_widget.setFrameStyle(QFrame.NoFrame)
        form_layout = QVBoxLayout(form_widget)
        form_layout.setSpacing(20)

        # === SECCIÓN 1: INFORMACIÓN BÁSICA ===
        basic_group = QGroupBox("📝 Información Básica")
        basic_group.setFont(QFont("Arial", 12, QFont.Bold))
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(10)

        # Título del evento
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("Título del evento")
        self.title_edit.setMinimumHeight(35)
        self.title_edit.setFont(QFont("Arial", 11))
        basic_layout.addRow("Título:", self.title_edit)

        form_layout.addWidget(basic_group)

        # === SECCIÓN 2: FECHA Y HORA ===
        date_time_group = QGroupBox("🕐 Fecha y Hora")
        date_time_group.setFont(QFont("Arial", 12, QFont.Bold))
        date_time_layout = QFormLayout(date_time_group)
        date_time_layout.setSpacing(10)

        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setMinimumHeight(35)
        date_time_layout.addRow("Fecha:", self.date_edit)

        self.all_day_check = QCheckBox("Todo el día")
        self.all_day_check.toggled.connect(self.toggle_time_fields)
        date_time_layout.addRow("", self.all_day_check)

        time_layout = QHBoxLayout()

        self.time_start = QTimeEdit()
        self.time_start.setTime(QTime.currentTime())
        self.time_start.setMinimumHeight(35)
        time_layout.addWidget(QLabel("Inicio:"))
        time_layout.addWidget(self.time_start)

        self.time_end = QTimeEdit()
        self.time_end.setTime(QTime.currentTime().addSecs(3600))  # +1 hora
        self.time_end.setMinimumHeight(35)
        time_layout.addWidget(QLabel("Fin:"))
        time_layout.addWidget(self.time_end)

        date_time_layout.addRow("Hora:", time_layout)

        form_layout.addWidget(date_time_group)

        # === SECCIÓN 3: CATEGORÍA Y CONFIGURACIÓN ===
        config_group = QGroupBox("🏷️ Categoría y Configuración")
        config_group.setFont(QFont("Arial", 12, QFont.Bold))
        config_layout = QFormLayout(config_group)
        config_layout.setSpacing(10)

        # Categoría con botón para agregar nueva
        category_layout = QHBoxLayout()
        self.category_combo = QComboBox()
        self.category_combo.addItem("Sin categoría", None)
        self.category_combo.setMinimumHeight(35)

        # Cargar categorías
        categories = self.data_manager.get_all_categories()
        for category in categories:
            self.category_combo.addItem(f"{category['name']}", category['id'])

        category_layout.addWidget(self.category_combo)

        # Botón para agregar nueva categoría
        add_category_btn = QPushButton("+ Nueva Categoría")
        add_category_btn.setMinimumWidth(120)
        add_category_btn.setMinimumHeight(35)
        add_category_btn.clicked.connect(self.add_new_category)
        category_layout.addWidget(add_category_btn)

        config_layout.addRow("Categoría:", category_layout)

        # Recordatorio
        self.reminder_combo = QComboBox()
        self.reminder_combo.addItem("Sin recordatorio", None)
        self.reminder_combo.addItem("5 minutos antes", "5")
        self.reminder_combo.addItem("15 minutos antes", "15")
        self.reminder_combo.addItem("30 minutos antes", "30")
        self.reminder_combo.addItem("1 hora antes", "60")
        self.reminder_combo.addItem("2 horas antes", "120")
        self.reminder_combo.addItem("1 día antes", "1440")
        self.reminder_combo.setMinimumHeight(35)
        config_layout.addRow("Recordatorio:", self.reminder_combo)

        # Color
        color_layout = QHBoxLayout()
        self.color_preview = QLabel()
        self.color_preview.setFixedSize(40, 35)
        self.color_preview.setStyleSheet("background-color: #1E90FF; border: 2px solid gray; border-radius: 5px;")
        self.current_color = QColor("#1E90FF")

        color_button = QPushButton("Seleccionar Color")
        color_button.clicked.connect(self.select_color)
        color_button.setMinimumHeight(35)

        color_layout.addWidget(self.color_preview)
        color_layout.addWidget(color_button)
        color_layout.addStretch()

        config_layout.addRow("Color:", color_layout)

        form_layout.addWidget(config_group)

        # === SECCIÓN 4: PRIORIDAD ===
        priority_group = QGroupBox("⚡ Prioridad del Evento")
        priority_group.setFont(QFont("Arial", 12, QFont.Bold))
        priority_layout = QVBoxLayout(priority_group)
        priority_layout.setSpacing(10)

        # Crear grupo de botones de radio para prioridad
        self.priority_group = QButtonGroup()

        # Crear layout horizontal para los botones de prioridad
        priority_buttons_layout = QHBoxLayout()

        # Prioridad 1: Normal (Verde)
        self.priority_normal = QRadioButton("🟢 Normal")
        self.priority_normal.setFont(QFont("Arial", 11))
        self.priority_normal.setStyleSheet("QRadioButton { color: #27AE60; font-weight: bold; }")
        self.priority_group.addButton(self.priority_normal, 1)
        priority_buttons_layout.addWidget(self.priority_normal)

        # Prioridad 2: Poco Importante (Amarillo)
        self.priority_low = QRadioButton("🟡 Poco Importante")
        self.priority_low.setFont(QFont("Arial", 11))
        self.priority_low.setStyleSheet("QRadioButton { color: #F39C12; font-weight: bold; }")
        self.priority_group.addButton(self.priority_low, 2)
        priority_buttons_layout.addWidget(self.priority_low)

        # Prioridad 3: Importante (Naranja)
        self.priority_high = QRadioButton("🟠 Importante")
        self.priority_high.setFont(QFont("Arial", 11))
        self.priority_high.setStyleSheet("QRadioButton { color: #E67E22; font-weight: bold; }")
        self.priority_group.addButton(self.priority_high, 3)
        priority_buttons_layout.addWidget(self.priority_high)

        # Prioridad 4: Muy Importante (Rojo)
        self.priority_critical = QRadioButton("🔴 Muy Importante")
        self.priority_critical.setFont(QFont("Arial", 11))
        self.priority_critical.setStyleSheet("QRadioButton { color: #E74C3C; font-weight: bold; }")
        self.priority_group.addButton(self.priority_critical, 4)
        priority_buttons_layout.addWidget(self.priority_critical)

        # Establecer Normal como selección por defecto
        self.priority_normal.setChecked(True)

        priority_layout.addLayout(priority_buttons_layout)

        # Descripción de prioridades
        priority_desc = QLabel("La prioridad determina el orden de los eventos: Muy Importante → Importante → Poco Importante → Normal")
        priority_desc.setFont(QFont("Arial", 9))
        priority_desc.setStyleSheet("color: #7F8C8D; font-style: italic;")
        priority_desc.setWordWrap(True)
        priority_layout.addWidget(priority_desc)

        form_layout.addWidget(priority_group)

        # === SECCIÓN 5: DESCRIPCIÓN ===
        desc_group = QGroupBox("📄 Descripción")
        desc_group.setFont(QFont("Arial", 12, QFont.Bold))
        desc_layout = QVBoxLayout(desc_group)

        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("Descripción detallada del evento...")
        self.description_edit.setMinimumHeight(120)
        self.description_edit.setFont(QFont("Arial", 11))
        desc_layout.addWidget(self.description_edit)

        form_layout.addWidget(desc_group)

        # Configurar scroll area
        scroll_area.setWidget(form_widget)
        main_layout.addWidget(scroll_area)

        # === BOTONES ===
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        cancel_btn = QPushButton("Cancelar")
        cancel_btn.setMinimumSize(100, 40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        save_btn = QPushButton("Guardar Evento")
        save_btn.setMinimumSize(120, 40)
        save_btn.setDefault(True)
        save_btn.clicked.connect(self.accept)
        save_btn.setStyleSheet("QPushButton { background-color: #3498DB; color: white; font-weight: bold; }")
        buttons_layout.addWidget(save_btn)

        main_layout.addLayout(buttons_layout)

    def add_new_category(self):
        """Agregar una nueva categoría personalizada"""
        from PyQt5.QtWidgets import QInputDialog, QMessageBox

        # Solicitar nombre de la categoría
        name, ok = QInputDialog.getText(
            self,
            "Nueva Categoría",
            "Nombre de la categoría:",
            text=""
        )

        if ok and name.strip():
            name = name.strip()

            # Verificar que no exista ya
            existing_categories = self.data_manager.get_all_categories()
            existing_names = [cat['name'].lower() for cat in existing_categories]

            if name.lower() in existing_names:
                QMessageBox.warning(self, "Categoría existente",
                                  f"La categoría '{name}' ya existe.")
                return

            # Seleccionar color para la nueva categoría
            color = QColorDialog.getColor(QColor("#3498DB"), self, "Color de la categoría")
            if not color.isValid():
                color = QColor("#3498DB")  # Color por defecto

            # Agregar la categoría
            category_id = self.data_manager.add_category(name, color.name())

            if category_id:
                # Actualizar el combo
                self.category_combo.addItem(name, category_id)
                # Seleccionar la nueva categoría
                self.category_combo.setCurrentIndex(self.category_combo.count() - 1)

                QMessageBox.information(self, "Categoría creada",
                                      f"La categoría '{name}' ha sido creada exitosamente.")
            else:
                QMessageBox.critical(self, "Error",
                                   "No se pudo crear la categoría.")

    def toggle_time_fields(self, checked):
        """Habilitar/deshabilitar campos de hora según 'Todo el día'"""
        self.time_start.setEnabled(not checked)
        self.time_end.setEnabled(not checked)

    def select_color(self):
        """Seleccionar color para el evento"""
        color = QColorDialog.getColor(self.current_color, self, "Seleccionar color")
        if color.isValid():
            self.current_color = color
            self.color_preview.setStyleSheet(f"background-color: {color.name()}; border: 2px solid gray; border-radius: 5px;")

    def fill_form(self):
        """Rellenar el formulario con los datos del evento existente"""
        if not self.event:
            return

        self.title_edit.setText(self.event['title'])

        date_str = self.event['date']
        try:
            date = QDate.fromString(date_str, "yyyy-MM-dd")
            self.date_edit.setDate(date)
        except:
            pass

        # Todo el día
        self.all_day_check.setChecked(self.event.get('all_day', 0) == 1)

        # Hora inicio
        time_start = self.event.get('time_start', '')
        if time_start:
            try:
                time = QTime.fromString(time_start, "HH:mm:ss")
                self.time_start.setTime(time)
            except:
                pass

        # Hora fin
        time_end = self.event.get('time_end', '')
        if time_end:
            try:
                time = QTime.fromString(time_end, "HH:mm:ss")
                self.time_end.setTime(time)
            except:
                pass

        # Categoría
        category_id = self.event.get('category_id')
        if category_id:
            index = self.category_combo.findData(category_id)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)

        # Recordatorio
        reminder = self.event.get('reminder')
        if reminder:
            index = self.reminder_combo.findData(reminder)
            if index >= 0:
                self.reminder_combo.setCurrentIndex(index)

        # Color
        color = self.event.get('color', '#1E90FF')
        self.current_color = QColor(color)
        self.color_preview.setStyleSheet(f"background-color: {color}; border: 1px solid gray;")

        # Descripción
        self.description_edit.setText(self.event.get('description', ''))

        # Prioridad
        priority = self.event.get('priority', 1)
        if priority == 1:
            self.priority_normal.setChecked(True)
        elif priority == 2:
            self.priority_low.setChecked(True)
        elif priority == 3:
            self.priority_high.setChecked(True)
        elif priority == 4:
            self.priority_critical.setChecked(True)
        else:
            self.priority_normal.setChecked(True)  # Por defecto

    def get_event_data(self):
        """Obtener los datos del evento desde el formulario"""
        # Obtener prioridad seleccionada
        priority = 1  # Normal por defecto
        if self.priority_group.checkedButton():
            priority = self.priority_group.id(self.priority_group.checkedButton())

        event_data = {
            'title': self.title_edit.text(),
            'date': self.date_edit.date().toString("yyyy-MM-dd"),
            'all_day': 1 if self.all_day_check.isChecked() else 0,
            'time_start': "00:00:00" if self.all_day_check.isChecked() else self.time_start.time().toString("HH:mm:ss"),
            'time_end': "23:59:59" if self.all_day_check.isChecked() else self.time_end.time().toString("HH:mm:ss"),
            'category_id': self.category_combo.currentData(),
            'reminder': self.reminder_combo.currentData(),
            'color': self.current_color.name(),
            'description': self.description_edit.toPlainText(),
            'priority': priority
        }

        return event_data

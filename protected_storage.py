#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema de almacenamiento protegido para archivos del programa
"""

import os
import shutil
import hashlib
from pathlib import Path

try:
    from file_encryption import FileEncryption
    ENCRYPTION_AVAILABLE = True
except ImportError:
    ENCRYPTION_AVAILABLE = False


class ProtectedStorage:
    """Gestor de almacenamiento protegido para archivos del programa"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.storage_dir = Path("protected_storage")
        self.images_dir = self.storage_dir / "images"
        self.encryption = FileEncryption() if ENCRYPTION_AVAILABLE else None
        
        # Crear directorios si no existen
        self.setup_directories()
    
    def setup_directories(self):
        """Crear directorios de almacenamiento protegido"""
        try:
            self.storage_dir.mkdir(exist_ok=True)
            self.images_dir.mkdir(exist_ok=True)
            
            # Crear archivo .gitignore para proteger el contenido
            gitignore_path = self.storage_dir / ".gitignore"
            if not gitignore_path.exists():
                with open(gitignore_path, 'w') as f:
                    f.write("# Archivos protegidos por contraseña maestra\n")
                    f.write("*\n")
                    f.write("!.gitignore\n")
            
            print(f"✅ Directorios de almacenamiento protegido creados: {self.storage_dir}")
            
        except Exception as e:
            print(f"❌ Error creando directorios protegidos: {str(e)}")
    
    def get_master_password(self):
        """Obtener la contraseña maestra"""
        return self.data_manager.get_config('banner_master_password', None)
    
    def is_access_authorized(self):
        """Verificar si el acceso está autorizado (hay contraseña maestra)"""
        return self.get_master_password() is not None
    
    def copy_image_to_storage(self, source_path, custom_name=None):
        """Copiar imagen al almacenamiento protegido"""
        if not self.is_access_authorized():
            raise PermissionError("Acceso no autorizado. Configure la contraseña maestra primero.")
        
        try:
            source_path = Path(source_path)
            
            if not source_path.exists():
                raise FileNotFoundError(f"Archivo fuente no encontrado: {source_path}")
            
            # Generar nombre único para la imagen
            if custom_name:
                filename = custom_name
            else:
                # Usar hash del contenido + extensión original
                with open(source_path, 'rb') as f:
                    content_hash = hashlib.md5(f.read()).hexdigest()[:8]
                filename = f"banner_image_{content_hash}{source_path.suffix}"
            
            destination_path = self.images_dir / filename
            
            # Copiar archivo
            shutil.copy2(source_path, destination_path)
            
            # Encriptar archivo si está disponible
            if self.encryption and ENCRYPTION_AVAILABLE:
                master_password = self.get_master_password()
                if master_password:
                    success = self.encryption.encrypt_file(str(destination_path), master_password)
                    if not success:
                        print(f"⚠️ Advertencia: No se pudo encriptar {filename}")
            
            print(f"✅ Imagen copiada al almacenamiento protegido: {filename}")
            return str(destination_path)
            
        except Exception as e:
            print(f"❌ Error copiando imagen: {str(e)}")
            raise
    
    def get_image_path(self, filename):
        """Obtener ruta de imagen en almacenamiento protegido"""
        if not self.is_access_authorized():
            return None
        
        image_path = self.images_dir / filename
        
        # Si el archivo está encriptado, desencriptarlo temporalmente
        if self.encryption and ENCRYPTION_AVAILABLE:
            if self.encryption.is_file_encrypted(str(image_path)):
                master_password = self.get_master_password()
                if master_password:
                    # Crear archivo temporal desencriptado
                    temp_path = image_path.with_suffix(image_path.suffix + '.temp')
                    
                    # Copiar archivo encriptado a temporal
                    shutil.copy2(image_path, temp_path)
                    
                    # Desencriptar archivo temporal
                    if self.encryption.decrypt_file(str(temp_path), master_password):
                        return str(temp_path)
                    else:
                        # Si falla la desencriptación, eliminar temporal
                        if temp_path.exists():
                            temp_path.unlink()
                        return None
        
        return str(image_path) if image_path.exists() else None
    
    def cleanup_temp_files(self):
        """Limpiar archivos temporales"""
        try:
            for temp_file in self.images_dir.glob("*.temp"):
                temp_file.unlink()
        except Exception as e:
            print(f"⚠️ Error limpiando archivos temporales: {str(e)}")
    
    def list_images(self):
        """Listar imágenes disponibles en almacenamiento protegido"""
        if not self.is_access_authorized():
            return []
        
        try:
            images = []
            for image_file in self.images_dir.iterdir():
                if image_file.is_file() and not image_file.name.endswith('.temp'):
                    # Verificar si es una imagen válida
                    if image_file.suffix.lower() in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']:
                        images.append({
                            'filename': image_file.name,
                            'path': str(image_file),
                            'size': image_file.stat().st_size,
                            'encrypted': self.encryption.is_file_encrypted(str(image_file)) if self.encryption else False
                        })
            
            return images
            
        except Exception as e:
            print(f"❌ Error listando imágenes: {str(e)}")
            return []
    
    def delete_image(self, filename):
        """Eliminar imagen del almacenamiento protegido"""
        if not self.is_access_authorized():
            raise PermissionError("Acceso no autorizado.")
        
        try:
            image_path = self.images_dir / filename
            if image_path.exists():
                image_path.unlink()
                print(f"✅ Imagen eliminada: {filename}")
                return True
            else:
                print(f"⚠️ Imagen no encontrada: {filename}")
                return False
                
        except Exception as e:
            print(f"❌ Error eliminando imagen: {str(e)}")
            return False
    
    def get_storage_info(self):
        """Obtener información del almacenamiento protegido"""
        info = {
            'storage_dir': str(self.storage_dir),
            'images_dir': str(self.images_dir),
            'authorized': self.is_access_authorized(),
            'encryption_available': ENCRYPTION_AVAILABLE,
            'total_images': 0,
            'encrypted_images': 0,
            'total_size': 0
        }
        
        if self.is_access_authorized():
            images = self.list_images()
            info['total_images'] = len(images)
            info['encrypted_images'] = sum(1 for img in images if img['encrypted'])
            info['total_size'] = sum(img['size'] for img in images)
        
        return info
    
    def encrypt_all_images(self):
        """Encriptar todas las imágenes en almacenamiento"""
        if not self.is_access_authorized() or not self.encryption:
            return {'error': 'No autorizado o encriptación no disponible'}
        
        master_password = self.get_master_password()
        if not master_password:
            return {'error': 'No hay contraseña maestra'}
        
        results = {'encrypted': [], 'failed': [], 'skipped': []}
        
        for image_file in self.images_dir.iterdir():
            if image_file.is_file() and not image_file.name.endswith('.temp'):
                if self.encryption.is_file_encrypted(str(image_file)):
                    results['skipped'].append(image_file.name)
                else:
                    if self.encryption.encrypt_file(str(image_file), master_password):
                        results['encrypted'].append(image_file.name)
                    else:
                        results['failed'].append(image_file.name)
        
        return results
    
    def decrypt_all_images(self, password):
        """Desencriptar todas las imágenes en almacenamiento"""
        if not self.encryption:
            return {'error': 'Encriptación no disponible'}
        
        results = {'decrypted': [], 'failed': [], 'skipped': []}
        
        for image_file in self.images_dir.iterdir():
            if image_file.is_file() and not image_file.name.endswith('.temp'):
                if not self.encryption.is_file_encrypted(str(image_file)):
                    results['skipped'].append(image_file.name)
                else:
                    if self.encryption.decrypt_file(str(image_file), password):
                        results['decrypted'].append(image_file.name)
                    else:
                        results['failed'].append(image_file.name)
        
        return results


# Funciones de utilidad
def get_protected_storage(data_manager):
    """Obtener instancia de almacenamiento protegido"""
    return ProtectedStorage(data_manager)


def copy_image_to_protected_storage(data_manager, source_path, custom_name=None):
    """Función de utilidad para copiar imagen al almacenamiento protegido"""
    storage = ProtectedStorage(data_manager)
    return storage.copy_image_to_storage(source_path, custom_name)


if __name__ == "__main__":
    # Script de prueba
    print("🔒 Sistema de Almacenamiento Protegido")
    print("=" * 40)
    
    # Crear instancia de prueba (requiere data_manager real)
    print("💡 Para usar el almacenamiento protegido:")
    print("   1. Configure la contraseña maestra en el banner")
    print("   2. Use copy_image_to_storage() para agregar imágenes")
    print("   3. Use get_image_path() para acceder a imágenes")
    print("   4. Los archivos se encriptan automáticamente")
    print(f"   5. Directorio: {Path('protected_storage').absolute()}")

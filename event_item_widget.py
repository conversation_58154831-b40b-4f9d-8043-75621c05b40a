#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget personalizado para items de eventos con checkbox de completado
"""

from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel,
                            QCheckBox, QFrame, QPushButton)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QCursor


class EventItemWidget(QWidget):
    """Widget personalizado para mostrar un evento con checkbox de completado"""

    # Señales
    completed_changed = pyqtSignal(int, bool)  # event_id, completed
    event_clicked = pyqtSignal(int)  # event_id para editar
    event_delete_requested = pyqtSignal(int)  # event_id para eliminar

    def __init__(self, event_data, parent=None):
        super().__init__(parent)
        self.event_data = event_data
        self.event_id = event_data['id']

        self.setup_ui()
        self.update_appearance()

    def setup_ui(self):
        """Configurar la interfaz del widget"""
        # Layout principal horizontal
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(5, 3, 5, 3)
        main_layout.setSpacing(8)

        # Checkbox de completado (más grande)
        self.completed_checkbox = QCheckBox("✓")  # Usar texto como marca
        self.completed_checkbox.setChecked(bool(self.event_data.get('completed', 0)))
        self.completed_checkbox.stateChanged.connect(self.on_completed_changed)
        self.completed_checkbox.setToolTip("Marcar como completado")

        # Hacer el checkbox más grande (el doble) y personalizar
        self.completed_checkbox.setStyleSheet("""
            QCheckBox {
                spacing: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 4px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #BDC3C7;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #27AE60;
                background-color: #27AE60;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #3498DB;
            }
            QCheckBox::indicator:unchecked:hover {
                background-color: #F8F9FA;
            }
            QCheckBox:checked {
                color: #27AE60;
            }
            QCheckBox:unchecked {
                color: transparent;
            }
        """)

        main_layout.addWidget(self.completed_checkbox, 0, Qt.AlignTop)

        # Indicador de prioridad
        self.priority_label = QLabel()
        self.priority_label.setFixedSize(16, 16)
        self.update_priority_indicator()
        main_layout.addWidget(self.priority_label, 0, Qt.AlignTop)

        # Contenido del evento
        content_layout = QVBoxLayout()
        content_layout.setSpacing(2)
        content_layout.setContentsMargins(0, 2, 0, 0)  # Pequeño margen superior para alinear con checkbox

        # Línea principal: hora y título
        self.main_label = QLabel()
        self.main_label.setFont(QFont("Arial", 11, QFont.Bold))  # Ligeramente más grande
        self.main_label.setMinimumHeight(20)  # Altura mínima para alinear con checkbox
        self.update_main_text()
        content_layout.addWidget(self.main_label)

        # Línea secundaria: descripción (si existe)
        self.description_label = QLabel()
        self.description_label.setFont(QFont("Arial", 9))  # Ligeramente más grande
        self.description_label.setStyleSheet("color: #7F8C8D; font-style: italic;")
        self.update_description_text()
        content_layout.addWidget(self.description_label)

        main_layout.addLayout(content_layout)
        main_layout.addStretch()

        # Botón de eliminar (cruz roja)
        self.delete_button = QPushButton("✕")
        self.delete_button.setFixedSize(20, 20)
        self.delete_button.setToolTip("Eliminar evento definitivamente")
        self.delete_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
                transform: scale(1.1);
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
        """)
        self.delete_button.clicked.connect(self.on_delete_requested)
        main_layout.addWidget(self.delete_button, 0, Qt.AlignTop)

        # Hacer el área de contenido clickeable para editar
        self.setStyleSheet("""
            EventItemWidget {
                border-radius: 3px;
                padding: 2px;
            }
            EventItemWidget:hover {
                background-color: #ECF0F1;
            }
        """)

    def update_priority_indicator(self):
        """Actualizar el indicador visual de prioridad"""
        priority = self.event_data.get('priority', 1)
        priority_config = {
            4: {"icon": "🔴", "tooltip": "Muy Importante"},
            3: {"icon": "🟠", "tooltip": "Importante"},
            2: {"icon": "🟡", "tooltip": "Poco Importante"},
            1: {"icon": "🟢", "tooltip": "Normal"}
        }

        config = priority_config.get(priority, priority_config[1])
        self.priority_label.setText(config["icon"])
        self.priority_label.setToolTip(f"Prioridad: {config['tooltip']}")

    def update_main_text(self):
        """Actualizar el texto principal del evento"""
        # Formatear hora
        time_str = ""
        if self.event_data.get('all_day', 0):
            time_str = "Todo el día"
        else:
            start_time = self.event_data.get('time_start', '')
            end_time = self.event_data.get('time_end', '')
            if start_time and end_time:
                # Mostrar solo HH:MM
                start_formatted = start_time[:5] if len(start_time) >= 5 else start_time
                end_formatted = end_time[:5] if len(end_time) >= 5 else end_time
                time_str = f"{start_formatted} - {end_formatted}"

        # Texto principal
        title = self.event_data.get('title', 'Sin título')
        main_text = f"{time_str}: {title}"

        self.main_label.setText(main_text)

    def update_description_text(self):
        """Actualizar el texto de descripción"""
        description = self.event_data.get('description', '').strip()
        if description:
            # Limitar descripción a 60 caracteres
            if len(description) > 60:
                description = description[:57] + "..."
            self.description_label.setText(description)
            self.description_label.show()
        else:
            self.description_label.hide()

    def update_appearance(self):
        """Actualizar la apariencia según el estado de completado"""
        completed = bool(self.event_data.get('completed', 0))

        if completed:
            # Estilo para evento completado
            self.setStyleSheet("""
                EventItemWidget {
                    background-color: #F8F9FA;
                    border-radius: 3px;
                    padding: 2px;
                    border-left: 3px solid #95A5A6;
                }
                EventItemWidget:hover {
                    background-color: #E9ECEF;
                }
            """)

            # Texto tachado y más tenue
            self.main_label.setStyleSheet("color: #7F8C8D; text-decoration: line-through;")
            self.description_label.setStyleSheet("color: #BDC3C7; font-style: italic; text-decoration: line-through;")

        else:
            # Estilo para evento activo
            category_color = self.event_data.get('category_color', '#2C3E50')

            self.setStyleSheet(f"""
                EventItemWidget {{
                    background-color: white;
                    border-radius: 3px;
                    padding: 2px;
                    border-left: 3px solid {category_color};
                }}
                EventItemWidget:hover {{
                    background-color: #ECF0F1;
                }}
            """)

            # Texto normal
            text_color = category_color if category_color else '#2C3E50'
            self.main_label.setStyleSheet(f"color: {text_color}; text-decoration: none;")
            self.description_label.setStyleSheet("color: #7F8C8D; font-style: italic; text-decoration: none;")

    def on_completed_changed(self, state):
        """Manejar cambio en el estado de completado"""
        completed = state == Qt.Checked

        # Actualizar datos del evento
        self.event_data['completed'] = 1 if completed else 0

        # Actualizar apariencia
        self.update_appearance()

        # Emitir señal
        self.completed_changed.emit(self.event_id, completed)

        print(f"Evento {self.event_id} marcado como {'completado' if completed else 'activo'}")

    def on_delete_requested(self):
        """Manejar solicitud de eliminación del evento"""
        from PyQt5.QtWidgets import QMessageBox

        # Confirmar eliminación
        reply = QMessageBox.question(
            self,
            "Confirmar Eliminación",
            f"¿Está seguro de que desea eliminar definitivamente el evento?\n\n"
            f"Título: {self.event_data.get('title', 'Sin título')}\n"
            f"Fecha: {self.event_data.get('date', '')}\n\n"
            f"⚠️ Esta acción no se puede deshacer.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Emitir señal de eliminación
            self.event_delete_requested.emit(self.event_id)
            print(f"Solicitud de eliminación para evento {self.event_id}")

    def mousePressEvent(self, event):
        """Manejar clic en el widget para editar evento"""
        if event.button() == Qt.LeftButton:
            # Solo emitir señal de edición si no se hizo clic en el checkbox o botón de eliminar
            if (not self.completed_checkbox.geometry().contains(event.pos()) and
                not self.delete_button.geometry().contains(event.pos())):
                self.event_clicked.emit(self.event_id)
        super().mousePressEvent(event)

    def update_event_data(self, event_data):
        """Actualizar los datos del evento"""
        self.event_data = event_data

        # Actualizar checkbox sin emitir señal
        self.completed_checkbox.blockSignals(True)
        self.completed_checkbox.setChecked(bool(event_data.get('completed', 0)))
        self.completed_checkbox.blockSignals(False)

        # Actualizar contenido
        self.update_priority_indicator()
        self.update_main_text()
        self.update_description_text()
        self.update_appearance()

    def set_completed(self, completed):
        """Establecer el estado de completado sin emitir señal"""
        self.completed_checkbox.blockSignals(True)
        self.completed_checkbox.setChecked(completed)
        self.completed_checkbox.blockSignals(False)

        self.event_data['completed'] = 1 if completed else 0
        self.update_appearance()


class EventListWidget(QWidget):
    """Widget contenedor para una lista de eventos con checkboxes"""

    # Señales
    event_completed_changed = pyqtSignal(int, bool)  # event_id, completed
    event_edit_requested = pyqtSignal(int)  # event_id
    event_delete_requested = pyqtSignal(int)  # event_id

    def __init__(self, parent=None):
        super().__init__(parent)
        self.event_widgets = {}  # Diccionario de event_id -> EventItemWidget

        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(2)

    def add_event(self, event_data):
        """Agregar un evento a la lista"""
        event_widget = EventItemWidget(event_data)
        event_widget.completed_changed.connect(self.event_completed_changed.emit)
        event_widget.event_clicked.connect(self.event_edit_requested.emit)
        event_widget.event_delete_requested.connect(self.event_delete_requested.emit)

        self.layout.addWidget(event_widget)
        self.event_widgets[event_data['id']] = event_widget

    def remove_event(self, event_id):
        """Remover un evento de la lista"""
        if event_id in self.event_widgets:
            widget = self.event_widgets[event_id]
            self.layout.removeWidget(widget)
            widget.deleteLater()
            del self.event_widgets[event_id]

    def update_event(self, event_data):
        """Actualizar un evento en la lista"""
        event_id = event_data['id']
        if event_id in self.event_widgets:
            self.event_widgets[event_id].update_event_data(event_data)

    def clear_events(self):
        """Limpiar todos los eventos"""
        for widget in self.event_widgets.values():
            self.layout.removeWidget(widget)
            widget.deleteLater()
        self.event_widgets.clear()

    def set_events(self, events_list):
        """Establecer la lista completa de eventos"""
        self.clear_events()
        for event_data in events_list:
            self.add_event(event_data)

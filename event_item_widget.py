#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget personalizado para items de eventos con checkbox de completado
"""

from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel,
                            QCheckBox, QFrame, QPushButton)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QCursor


class EventItemWidget(QWidget):
    """Widget personalizado para mostrar un evento con checkbox de completado"""

    # Señales
    completed_changed = pyqtSignal(int, bool)  # event_id, completed
    event_clicked = pyqtSignal(int)  # event_id para editar
    event_delete_requested = pyqtSignal(int)  # event_id para eliminar

    def __init__(self, event_data, parent=None):
        super().__init__(parent)
        self.event_data = event_data
        self.event_id = event_data['id']

        self.setup_ui()
        self.update_appearance()

    def setup_ui(self):
        """Configurar la interfaz del widget"""
        # Layout principal horizontal para una sola línea
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(5, 2, 5, 2)  # Márgenes mínimos arriba/abajo
        main_layout.setSpacing(8)

        # Checkbox de completado (más grande)
        self.completed_checkbox = QCheckBox("✓")  # Usar texto como marca
        self.completed_checkbox.setChecked(bool(self.event_data.get('completed', 0)))
        self.completed_checkbox.stateChanged.connect(self.on_completed_changed)
        self.completed_checkbox.setToolTip("Marcar como completado")

        # Hacer el checkbox más grande (el doble) y personalizar
        self.completed_checkbox.setStyleSheet("""
            QCheckBox {
                spacing: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 4px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #BDC3C7;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #27AE60;
                background-color: #27AE60;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #3498DB;
            }
            QCheckBox::indicator:unchecked:hover {
                background-color: #F8F9FA;
            }
            QCheckBox:checked {
                color: #27AE60;
            }
            QCheckBox:unchecked {
                color: transparent;
            }
        """)

        main_layout.addWidget(self.completed_checkbox, 0, Qt.AlignTop)

        # Indicador de prioridad
        self.priority_label = QLabel()
        self.priority_label.setFixedSize(16, 16)
        self.update_priority_indicator()
        main_layout.addWidget(self.priority_label, 0, Qt.AlignCenter)

        # Contenido del evento en una sola línea
        self.event_label = QLabel()
        self.event_label.setFont(QFont("Arial", 10))  # Tamaño estándar para una línea
        self.update_event_text()
        main_layout.addWidget(self.event_label)
        main_layout.addStretch()

        # Botón de eliminar (aspa blanca en fondo negro, mismo tamaño que checkbox)
        self.delete_button = QPushButton("✕")
        self.delete_button.setFixedSize(20, 20)  # Mismo tamaño que checkbox
        self.delete_button.setToolTip("Eliminar evento definitivamente")
        self.delete_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #2C3E50;
                color: white;
                border: 1px solid #34495E;
                border-radius: 3px;
                font-size: 11px;
                font-weight: bold;
                margin-right: 5px;
            }
            QPushButton:hover {
                background-color: #34495E;
                border: 1px solid #5D6D7E;
            }
            QPushButton:pressed {
                background-color: #1B2631;
                border: 1px solid #2C3E50;
            }
        """)
        self.delete_button.clicked.connect(self.on_delete_requested)

        # Agregar el botón con margen derecho más amplio para separarlo del borde
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 15, 0)  # Margen derecho de 15px (más alejado)
        button_layout.addWidget(self.delete_button)

        main_layout.addWidget(button_container, 0, Qt.AlignCenter)

        # Hacer el área de contenido clickeable para editar
        self.setStyleSheet("""
            EventItemWidget {
                border-radius: 3px;
                padding: 2px;
            }
            EventItemWidget:hover {
                background-color: #ECF0F1;
            }
        """)

    def update_priority_indicator(self):
        """Actualizar el indicador visual de prioridad"""
        priority = self.event_data.get('priority', 1)
        priority_config = {
            4: {"icon": "🔴", "tooltip": "Muy Importante"},
            3: {"icon": "🟠", "tooltip": "Importante"},
            2: {"icon": "🟡", "tooltip": "Poco Importante"},
            1: {"icon": "🟢", "tooltip": "Normal"}
        }

        config = priority_config.get(priority, priority_config[1])
        self.priority_label.setText(config["icon"])
        self.priority_label.setToolTip(f"Prioridad: {config['tooltip']}")

    def update_event_text(self):
        """Actualizar el texto del evento en una sola línea"""
        # Formatear hora
        time_str = ""
        if self.event_data.get('all_day', 0):
            time_str = "Todo el día"
        else:
            start_time = self.event_data.get('time_start', '')
            end_time = self.event_data.get('time_end', '')
            if start_time and end_time:
                # Mostrar solo HH:MM
                start_formatted = start_time[:5] if len(start_time) >= 5 else start_time
                end_formatted = end_time[:5] if len(end_time) >= 5 else end_time
                time_str = f"{start_formatted}-{end_formatted}"

        # Título del evento
        title = self.event_data.get('title', 'Sin título')

        # Descripción (opcional, limitada)
        description = self.event_data.get('description', '').strip()
        if description:
            # Limitar descripción a 30 caracteres para que quepa en una línea
            if len(description) > 30:
                description = description[:27] + "..."
            event_text = f"{time_str}: {title} - {description}"
        else:
            event_text = f"{time_str}: {title}"

        self.event_label.setText(event_text)

    def update_appearance(self):
        """Actualizar la apariencia según el estado de completado"""
        completed = bool(self.event_data.get('completed', 0))

        if completed:
            # Estilo para evento completado
            self.setStyleSheet("""
                EventItemWidget {
                    background-color: #F8F9FA;
                    border-radius: 3px;
                    padding: 2px;
                    border-left: 3px solid #95A5A6;
                    min-height: 24px;
                    max-height: 24px;
                }
                EventItemWidget:hover {
                    background-color: #E9ECEF;
                }
            """)

            # Texto tachado y más tenue
            self.event_label.setStyleSheet("color: #7F8C8D; text-decoration: line-through;")

        else:
            # Estilo para evento activo
            category_color = self.event_data.get('category_color', '#2C3E50')

            self.setStyleSheet(f"""
                EventItemWidget {{
                    background-color: white;
                    border-radius: 3px;
                    padding: 2px;
                    border-left: 3px solid {category_color};
                    min-height: 24px;
                    max-height: 24px;
                }}
                EventItemWidget:hover {{
                    background-color: #ECF0F1;
                }}
            """)

            # Texto normal
            text_color = category_color if category_color else '#2C3E50'
            self.event_label.setStyleSheet(f"color: {text_color}; text-decoration: none;")

    def on_completed_changed(self, state):
        """Manejar cambio en el estado de completado"""
        completed = state == Qt.Checked

        # Actualizar datos del evento
        self.event_data['completed'] = 1 if completed else 0

        # Actualizar apariencia
        self.update_appearance()

        # Emitir señal
        self.completed_changed.emit(self.event_id, completed)

        print(f"Evento {self.event_id} marcado como {'completado' if completed else 'activo'}")

    def on_delete_requested(self):
        """Manejar solicitud de eliminación del evento"""
        from PyQt5.QtWidgets import QMessageBox

        # Confirmar eliminación
        reply = QMessageBox.question(
            self,
            "Confirmar Eliminación",
            f"¿Está seguro de que desea eliminar definitivamente el evento?\n\n"
            f"Título: {self.event_data.get('title', 'Sin título')}\n"
            f"Fecha: {self.event_data.get('date', '')}\n\n"
            f"⚠️ Esta acción no se puede deshacer.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Emitir señal de eliminación
            self.event_delete_requested.emit(self.event_id)
            print(f"Solicitud de eliminación para evento {self.event_id}")

    def mousePressEvent(self, event):
        """Manejar clic en el widget para editar evento"""
        if event.button() == Qt.LeftButton:
            # Solo emitir señal de edición si no se hizo clic en el checkbox o área del botón de eliminar
            checkbox_area = self.completed_checkbox.geometry()
            delete_button_area = self.delete_button.geometry()

            # Expandir el área del botón de eliminar para incluir su contenedor (más amplio)
            delete_area_expanded = delete_button_area.adjusted(-15, -5, 15, 5)

            if (not checkbox_area.contains(event.pos()) and
                not delete_area_expanded.contains(event.pos())):
                self.event_clicked.emit(self.event_id)
        super().mousePressEvent(event)

    def update_event_data(self, event_data):
        """Actualizar los datos del evento"""
        self.event_data = event_data

        # Actualizar checkbox sin emitir señal
        self.completed_checkbox.blockSignals(True)
        self.completed_checkbox.setChecked(bool(event_data.get('completed', 0)))
        self.completed_checkbox.blockSignals(False)

        # Actualizar contenido
        self.update_priority_indicator()
        self.update_event_text()
        self.update_appearance()

    def set_completed(self, completed):
        """Establecer el estado de completado sin emitir señal"""
        self.completed_checkbox.blockSignals(True)
        self.completed_checkbox.setChecked(completed)
        self.completed_checkbox.blockSignals(False)

        self.event_data['completed'] = 1 if completed else 0
        self.update_appearance()


class EventListWidget(QWidget):
    """Widget contenedor para una lista de eventos con checkboxes"""

    # Señales
    event_completed_changed = pyqtSignal(int, bool)  # event_id, completed
    event_edit_requested = pyqtSignal(int)  # event_id
    event_delete_requested = pyqtSignal(int)  # event_id

    def __init__(self, parent=None):
        super().__init__(parent)
        self.event_widgets = {}  # Diccionario de event_id -> EventItemWidget

        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(2)

    def add_event(self, event_data):
        """Agregar un evento a la lista"""
        event_widget = EventItemWidget(event_data)
        event_widget.completed_changed.connect(self.event_completed_changed.emit)
        event_widget.event_clicked.connect(self.event_edit_requested.emit)
        event_widget.event_delete_requested.connect(self.event_delete_requested.emit)

        self.layout.addWidget(event_widget)
        self.event_widgets[event_data['id']] = event_widget

    def remove_event(self, event_id):
        """Remover un evento de la lista"""
        if event_id in self.event_widgets:
            widget = self.event_widgets[event_id]
            self.layout.removeWidget(widget)
            widget.deleteLater()
            del self.event_widgets[event_id]

    def update_event(self, event_data):
        """Actualizar un evento en la lista"""
        event_id = event_data['id']
        if event_id in self.event_widgets:
            self.event_widgets[event_id].update_event_data(event_data)

    def clear_events(self):
        """Limpiar todos los eventos"""
        for widget in self.event_widgets.values():
            self.layout.removeWidget(widget)
            widget.deleteLater()
        self.event_widgets.clear()

    def set_events(self, events_list):
        """Establecer la lista completa de eventos"""
        self.clear_events()
        for event_data in events_list:
            self.add_event(event_data)

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget personalizado para items de eventos con checkbox de completado
"""

from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel,
                            QCheckBox, QFrame, QPushButton, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QCursor


class EventItemWidget(QWidget):
    """Widget personalizado para mostrar un evento con checkbox de completado"""

    # Señales
    completed_changed = pyqtSignal(int, bool)  # event_id, completed
    event_clicked = pyqtSignal(int)  # event_id para editar
    event_delete_requested = pyqtSignal(int)  # event_id para eliminar

    def __init__(self, event_data, parent=None):
        super().__init__(parent)
        self.event_data = event_data
        self.event_id = event_data['id']

        self.setup_ui()
        self.update_appearance()

    def setup_ui(self):
        """Configurar la interfaz del widget"""
        # Layout principal horizontal para una sola línea
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(5, 2, 5, 2)  # Márgenes mínimos arriba/abajo
        main_layout.setSpacing(8)

        # Checkbox de completado (más grande)
        self.completed_checkbox = QCheckBox("✓")  # Usar texto como marca
        self.completed_checkbox.setChecked(bool(self.event_data.get('completed', 0)))
        self.completed_checkbox.stateChanged.connect(self.on_completed_changed)
        self.completed_checkbox.setToolTip("Marcar como completado")

        # Hacer el checkbox más grande (el doble) y personalizar
        self.completed_checkbox.setStyleSheet("""
            QCheckBox {
                spacing: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 4px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #BDC3C7;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #27AE60;
                background-color: #27AE60;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #3498DB;
            }
            QCheckBox::indicator:unchecked:hover {
                background-color: #F8F9FA;
            }
            QCheckBox:checked {
                color: #27AE60;
            }
            QCheckBox:unchecked {
                color: transparent;
            }
        """)

        main_layout.addWidget(self.completed_checkbox, 0, Qt.AlignTop)

        # Indicador de prioridad
        self.priority_label = QLabel()
        self.priority_label.setFixedSize(16, 16)
        self.update_priority_indicator()
        main_layout.addWidget(self.priority_label, 0, Qt.AlignCenter)

        # Contenido del evento en una sola línea
        self.event_label = QLabel()
        self.event_label.setFont(QFont("Arial", 10))  # Tamaño estándar para una línea
        self.update_event_text()
        main_layout.addWidget(self.event_label)

        # Espaciador entre el texto y los botones
        main_layout.addStretch()

        # Botón de editar (cuadrado perfecto más grande)
        self.edit_button = QPushButton("✏️")
        self.edit_button.setFixedSize(32, 32)  # Cuadrado perfecto más grande
        self.edit_button.setToolTip("Editar evento")
        self.edit_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.edit_button.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #21618C;
            }
        """)
        self.edit_button.clicked.connect(self.on_edit_requested)
        main_layout.addWidget(self.edit_button, 0, Qt.AlignCenter)

        # Espaciador entre botones
        spacer_between_buttons = QWidget()
        spacer_between_buttons.setFixedWidth(8)
        main_layout.addWidget(spacer_between_buttons)

        # Botón de eliminar (cuadrado perfecto más grande)
        self.delete_button = QPushButton("🗑️")
        self.delete_button.setFixedSize(32, 32)  # Cuadrado perfecto más grande
        self.delete_button.setToolTip("Eliminar evento definitivamente")
        self.delete_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
        """)
        self.delete_button.clicked.connect(self.on_delete_requested)
        main_layout.addWidget(self.delete_button, 0, Qt.AlignCenter)

        # Hacer el área de contenido clickeable para editar
        self.setStyleSheet("""
            EventItemWidget {
                border-radius: 3px;
                padding: 2px;
            }
            EventItemWidget:hover {
                background-color: #ECF0F1;
            }
        """)

    def update_priority_indicator(self):
        """Actualizar el indicador visual de prioridad"""
        priority = self.event_data.get('priority', 1)
        priority_config = {
            4: {"icon": "🔴", "tooltip": "Muy Importante"},
            3: {"icon": "🟠", "tooltip": "Importante"},
            2: {"icon": "🟡", "tooltip": "Poco Importante"},
            1: {"icon": "🟢", "tooltip": "Normal"}
        }

        config = priority_config.get(priority, priority_config[1])
        self.priority_label.setText(config["icon"])
        self.priority_label.setToolTip(f"Prioridad: {config['tooltip']}")

    def update_event_text(self):
        """Actualizar el texto del evento en una sola línea"""
        # Formatear hora
        time_str = ""
        if self.event_data.get('all_day', 0):
            time_str = "Todo el día"
        else:
            start_time = self.event_data.get('time_start', '')
            end_time = self.event_data.get('time_end', '')
            if start_time and end_time:
                # Mostrar solo HH:MM
                start_formatted = start_time[:5] if len(start_time) >= 5 else start_time
                end_formatted = end_time[:5] if len(end_time) >= 5 else end_time
                time_str = f"{start_formatted}-{end_formatted}"

        # Título del evento
        title = self.event_data.get('title', 'Sin título')

        # Descripción (opcional, limitada)
        description = self.event_data.get('description', '').strip()
        if description:
            # Limitar descripción a 30 caracteres para que quepa en una línea
            if len(description) > 30:
                description = description[:27] + "..."
            event_text = f"{time_str}: {title} - {description}"
        else:
            event_text = f"{time_str}: {title}"

        self.event_label.setText(event_text)

    def update_appearance(self):
        """Actualizar la apariencia según el estado de completado"""
        completed = bool(self.event_data.get('completed', 0))

        if completed:
            # Estilo para evento completado (tipo Excel)
            self.setStyleSheet("""
                EventItemWidget {
                    background-color: #F8F9FA;
                    border: 1px solid #D0D0D0;
                    border-left: 3px solid #95A5A6;
                    padding: 2px;
                    margin: 0px;
                    min-height: 24px;
                    max-height: 24px;
                }
                EventItemWidget:hover {
                    background-color: #E9ECEF;
                    border: 1px solid #3498DB;
                    border-left: 3px solid #95A5A6;
                }
            """)

            # Texto tachado y más tenue
            self.event_label.setStyleSheet("color: #7F8C8D; text-decoration: line-through; font-weight: normal;")

        else:
            # Estilo para evento activo (tipo Excel)
            category_color = self.event_data.get('category_color', '#2C3E50')

            self.setStyleSheet(f"""
                EventItemWidget {{
                    background-color: white;
                    border: 1px solid #D0D0D0;
                    border-left: 3px solid {category_color};
                    padding: 2px;
                    margin: 0px;
                    min-height: 24px;
                    max-height: 24px;
                }}
                EventItemWidget:hover {{
                    background-color: #ECF0F1;
                    border: 1px solid #3498DB;
                    border-left: 3px solid {category_color};
                }}
            """)

            # Texto normal
            text_color = category_color if category_color else '#2C3E50'
            self.event_label.setStyleSheet(f"color: {text_color}; text-decoration: none;")

    def on_completed_changed(self, state):
        """Manejar cambio en el estado de completado"""
        completed = state == Qt.Checked

        # Actualizar datos del evento
        self.event_data['completed'] = 1 if completed else 0

        # Actualizar apariencia
        self.update_appearance()

        # Emitir señal
        self.completed_changed.emit(self.event_id, completed)

        print(f"Evento {self.event_id} marcado como {'completado' if completed else 'activo'}")

    def on_edit_requested(self):
        """Manejar solicitud de edición del evento"""
        self.event_clicked.emit(self.event_id)
        print(f"Solicitud de edición para evento {self.event_id}")

    def on_delete_requested(self):
        """Manejar solicitud de eliminación del evento"""
        from PyQt5.QtWidgets import QMessageBox

        # Crear diálogo de confirmación mejorado
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("⚠️ Confirmar Eliminación")
        msg_box.setIcon(QMessageBox.Warning)

        # Mensaje de advertencia
        event_title = self.event_data.get('title', 'Sin título')
        event_date = self.event_data.get('date', '')

        msg_box.setText(f"¿Está seguro de que desea eliminar el evento?")
        msg_box.setInformativeText(f"📅 Evento: {event_title}\n"
                                  f"📆 Fecha: {event_date}\n\n"
                                  f"⚠️ ADVERTENCIA: Se perderán todos los datos del evento.\n"
                                  f"Esta acción no se puede deshacer.")

        # Botones personalizados
        delete_button = msg_box.addButton("🗑️ ELIMINAR", QMessageBox.DestructiveRole)
        cancel_button = msg_box.addButton("❌ CANCELAR", QMessageBox.RejectRole)

        # Configurar el diálogo
        msg_box.setDefaultButton(cancel_button)  # Cancelar por defecto para seguridad

        # Mostrar diálogo y procesar respuesta
        msg_box.exec_()

        if msg_box.clickedButton() == delete_button:
            # Usuario confirmó la eliminación
            self.event_delete_requested.emit(self.event_id)
            print(f"Solicitud de eliminación para evento {self.event_id}")

    def mousePressEvent(self, event):
        """Manejar clic en el widget para editar evento"""
        if event.button() == Qt.LeftButton:
            # Solo emitir señal de edición si no se hizo clic en el checkbox o botones
            checkbox_area = self.completed_checkbox.geometry()
            edit_button_area = self.edit_button.geometry()
            delete_button_area = self.delete_button.geometry()

            # Expandir ligeramente las áreas de los botones
            edit_area_expanded = edit_button_area.adjusted(-5, -5, 5, 5)
            delete_area_expanded = delete_button_area.adjusted(-5, -5, 5, 5)

            if (not checkbox_area.contains(event.pos()) and
                not edit_area_expanded.contains(event.pos()) and
                not delete_area_expanded.contains(event.pos())):
                self.event_clicked.emit(self.event_id)
        super().mousePressEvent(event)

    def update_event_data(self, event_data):
        """Actualizar los datos del evento"""
        self.event_data = event_data

        # Actualizar checkbox sin emitir señal
        self.completed_checkbox.blockSignals(True)
        self.completed_checkbox.setChecked(bool(event_data.get('completed', 0)))
        self.completed_checkbox.blockSignals(False)

        # Actualizar contenido
        self.update_priority_indicator()
        self.update_event_text()
        self.update_appearance()

    def set_completed(self, completed):
        """Establecer el estado de completado sin emitir señal"""
        self.completed_checkbox.blockSignals(True)
        self.completed_checkbox.setChecked(completed)
        self.completed_checkbox.blockSignals(False)

        self.event_data['completed'] = 1 if completed else 0
        self.update_appearance()


class EventListWidget(QWidget):
    """Widget contenedor para una lista de eventos en formato tabla Excel"""

    # Señales
    event_completed_changed = pyqtSignal(int, bool)  # event_id, completed
    event_edit_requested = pyqtSignal(int)  # event_id
    event_delete_requested = pyqtSignal(int)  # event_id

    def __init__(self, parent=None):
        super().__init__(parent)
        self.event_widgets = {}  # Diccionario de event_id -> fila
        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz tipo Excel con tabla"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Crear tabla tipo Excel
        self.events_table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.events_table)

    def setup_table(self):
        """Configurar la tabla tipo Excel"""
        # Configurar columnas
        columns = ["✓", "🔥", "Hora", "Título", "Descripción", "Acciones"]
        self.events_table.setColumnCount(len(columns))
        self.events_table.setHorizontalHeaderLabels(columns)

        # Estilo tipo Excel
        self.events_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #D0D0D0;
                background-color: white;
                alternate-background-color: #F8F9FA;
                selection-background-color: #E3F2FD;
                font-size: 10pt;
                border: 1px solid #BDC3C7;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #E0E0E0;
                border-right: 1px solid #E0E0E0;
            }
            QHeaderView::section {
                background-color: #F0F0F0;
                padding: 8px;
                border: 1px solid #D0D0D0;
                font-weight: bold;
                font-size: 10pt;
            }
            QHeaderView::section:hover {
                background-color: #E8F4FD;
            }
        """)

        # Configuración de la tabla
        self.events_table.setAlternatingRowColors(True)
        self.events_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.events_table.verticalHeader().setVisible(False)
        self.events_table.setSortingEnabled(True)

        # Configurar redimensionamiento de columnas (tipo Excel)
        header = self.events_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)        # Checkbox
        header.setSectionResizeMode(1, QHeaderView.Fixed)        # Prioridad
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # Hora
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # Título
        header.setSectionResizeMode(4, QHeaderView.Stretch)     # Descripción
        header.setSectionResizeMode(5, QHeaderView.Fixed)       # Acciones

        # Anchos iniciales de columnas
        self.events_table.setColumnWidth(0, 40)   # Checkbox
        self.events_table.setColumnWidth(1, 40)   # Prioridad
        self.events_table.setColumnWidth(2, 80)   # Hora
        self.events_table.setColumnWidth(3, 150)  # Título
        self.events_table.setColumnWidth(5, 120)  # Acciones (más amplia)

    def add_event(self, event_data):
        """Agregar un evento a la tabla"""
        row = self.events_table.rowCount()
        self.events_table.insertRow(row)

        # Columna 0: Checkbox de completado
        checkbox = QCheckBox()
        checkbox.setChecked(event_data.get('completed', False))
        checkbox.stateChanged.connect(lambda state, eid=event_data['id'], r=row:
            self.on_checkbox_changed(eid, state == Qt.Checked, r))
        checkbox.setStyleSheet("QCheckBox { margin: 5px; }")
        self.events_table.setCellWidget(row, 0, checkbox)

        # Columna 1: Indicador de prioridad
        priority = event_data.get('priority', 1)
        priority_icons = {4: "🔴", 3: "🟠", 2: "🟡", 1: "🟢"}
        priority_item = QTableWidgetItem(priority_icons.get(priority, "🟢"))
        priority_item.setTextAlignment(Qt.AlignCenter)
        self.events_table.setItem(row, 1, priority_item)

        # Columna 2: Hora
        if event_data.get('all_day', 0):
            time_str = "Todo el día"
        else:
            start_time = event_data.get('time_start', '')
            end_time = event_data.get('time_end', '')
            if start_time and end_time:
                start_formatted = start_time[:5] if len(start_time) >= 5 else start_time
                end_formatted = end_time[:5] if len(end_time) >= 5 else end_time
                time_str = f"{start_formatted}-{end_formatted}"
            else:
                time_str = start_time[:5] if start_time else ""

        time_item = QTableWidgetItem(time_str)
        if event_data.get('completed', False):
            time_item.setBackground(Qt.lightGray)
            # Aplicar texto tachado
            font = time_item.font()
            font.setStrikeOut(True)
            time_item.setFont(font)
            time_item.setForeground(Qt.gray)
        self.events_table.setItem(row, 2, time_item)

        # Columna 3: Título
        title_item = QTableWidgetItem(event_data.get('title', 'Sin título'))
        if event_data.get('completed', False):
            title_item.setBackground(Qt.lightGray)
            # Aplicar texto tachado
            font = title_item.font()
            font.setStrikeOut(True)
            title_item.setFont(font)
            title_item.setForeground(Qt.gray)
        self.events_table.setItem(row, 3, title_item)

        # Columna 4: Descripción
        description = event_data.get('description', '').strip()
        if len(description) > 50:
            description = description[:47] + "..."
        desc_item = QTableWidgetItem(description)
        if event_data.get('completed', False):
            desc_item.setBackground(Qt.lightGray)
            # Aplicar texto tachado
            font = desc_item.font()
            font.setStrikeOut(True)
            desc_item.setFont(font)
            desc_item.setForeground(Qt.gray)
        self.events_table.setItem(row, 4, desc_item)

        # Columna 5: Botones de acción
        actions_widget = self.create_actions_widget(event_data['id'])
        self.events_table.setCellWidget(row, 5, actions_widget)

        # Guardar referencia
        self.event_widgets[event_data['id']] = row

    def create_actions_widget(self, event_id):
        """Crear widget con botones de acción para cada fila"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(4)

        # Botón editar (cuadrado perfecto más grande)
        edit_button = QPushButton("✏️")
        edit_button.setFixedSize(32, 32)
        edit_button.setToolTip("Editar evento")
        edit_button.clicked.connect(lambda: self.event_edit_requested.emit(event_id))
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #21618C;
            }
        """)
        layout.addWidget(edit_button)

        # Botón eliminar (cuadrado perfecto más grande)
        delete_button = QPushButton("🗑️")
        delete_button.setFixedSize(32, 32)
        delete_button.setToolTip("Eliminar evento")
        delete_button.clicked.connect(lambda: self.event_delete_requested.emit(event_id))
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
        """)
        layout.addWidget(delete_button)

        return widget

    def on_checkbox_changed(self, event_id, completed, row):
        """Manejar cambio en checkbox y actualizar aspecto visual"""
        # Emitir señal para actualizar base de datos
        self.event_completed_changed.emit(event_id, completed)

        # Actualizar aspecto visual de la fila
        self.update_row_appearance(row, completed)

    def update_row_appearance(self, row, completed):
        """Actualizar el aspecto visual de una fila según su estado de completado"""
        # Actualizar columnas 2, 3, 4 (Hora, Título, Descripción)
        for col in [2, 3, 4]:
            item = self.events_table.item(row, col)
            if item:
                if completed:
                    # Aplicar estilo de completado
                    item.setBackground(Qt.lightGray)
                    font = item.font()
                    font.setStrikeOut(True)
                    item.setFont(font)
                    item.setForeground(Qt.gray)
                else:
                    # Remover estilo de completado
                    item.setBackground(Qt.white)
                    font = item.font()
                    font.setStrikeOut(False)
                    item.setFont(font)
                    item.setForeground(Qt.black)

    def remove_event(self, event_id):
        """Remover un evento de la tabla"""
        if event_id in self.event_widgets:
            row = self.event_widgets[event_id]
            self.events_table.removeRow(row)
            del self.event_widgets[event_id]
            # Actualizar índices de filas
            self.update_row_indices()

    def update_row_indices(self):
        """Actualizar los índices de filas después de eliminar"""
        new_widgets = {}
        for event_id, old_row in self.event_widgets.items():
            # Encontrar la nueva fila buscando por event_id en los widgets
            for row in range(self.events_table.rowCount()):
                actions_widget = self.events_table.cellWidget(row, 5)
                if actions_widget:
                    # Verificar si este widget corresponde al event_id
                    # (esto es una simplificación, en una implementación real
                    # necesitarías una forma más robusta de identificar)
                    new_widgets[event_id] = row
                    break
        self.event_widgets = new_widgets

    def update_event(self, event_data):
        """Actualizar un evento en la tabla"""
        event_id = event_data['id']
        if event_id in self.event_widgets:
            # Remover y volver a agregar (simplificado)
            self.remove_event(event_id)
            self.add_event(event_data)

    def update_event_completed_status(self, event_id, completed):
        """Actualizar solo el estado de completado de un evento específico"""
        if event_id in self.event_widgets:
            row = self.event_widgets[event_id]

            # Actualizar checkbox sin emitir señal
            checkbox = self.events_table.cellWidget(row, 0)
            if checkbox:
                checkbox.blockSignals(True)
                checkbox.setChecked(completed)
                checkbox.blockSignals(False)

            # Actualizar aspecto visual
            self.update_row_appearance(row, completed)

    def clear_events(self):
        """Limpiar todos los eventos"""
        self.events_table.setRowCount(0)
        self.event_widgets.clear()

    def set_events(self, events_list):
        """Establecer la lista completa de eventos ordenados por prioridad y hora"""
        self.clear_events()

        # Ordenar eventos por prioridad (más importantes arriba) y hora de ejecución
        sorted_events = sorted(events_list, key=lambda x: (
            -x.get('priority', 1),  # Prioridad descendente
            x.get('time_start', '00:00:00') if not x.get('all_day', 0) else '00:00:00'
        ))

        for event_data in sorted_events:
            self.add_event(event_data)

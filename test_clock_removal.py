#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar la funcionalidad de eliminación de relojes
con botón X y solo relojes digitales
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout)
from PyQt5.QtCore import Qt

from world_clocks_widget import WorldClocksWidget
from clock_widget import ClockWidget
from data_manager import DataManager
from styles import apply_styles


class TestClockRemovalWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Eliminación de Relojes - Solo Digitales")
        self.setGeometry(100, 100, 900, 600)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Eliminación de Relojes")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "• Cada reloj tiene un botón ✕ rojo en la esquina superior derecha\n"
            "• Haz clic en ✕ para eliminar un reloj (mínimo 2 relojes)\n"
            "• Solo se muestran relojes digitales\n"
            "• Usa '+ Agregar Reloj' para añadir más relojes (máximo 6)"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #666; font-size: 10pt;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Widget de relojes mundiales
        self.world_clocks = WorldClocksWidget(self.data_manager)
        layout.addWidget(self.world_clocks)
        
        # Botones de prueba
        buttons_layout = QHBoxLayout()
        
        btn_add_tokyo = QPushButton("Agregar Tokio")
        btn_add_tokyo.clicked.connect(self.add_tokyo_clock)
        buttons_layout.addWidget(btn_add_tokyo)
        
        btn_add_ny = QPushButton("Agregar Nueva York")
        btn_add_ny.clicked.connect(self.add_ny_clock)
        buttons_layout.addWidget(btn_add_ny)
        
        btn_add_sydney = QPushButton("Agregar Sydney")
        btn_add_sydney.clicked.connect(self.add_sydney_clock)
        buttons_layout.addWidget(btn_add_sydney)
        
        btn_count = QPushButton("Contar Relojes")
        btn_count.clicked.connect(self.count_clocks)
        buttons_layout.addWidget(btn_count)
        
        layout.addLayout(buttons_layout)
        
        # Información adicional
        info = QLabel(
            "💡 Características implementadas:\n"
            "✅ Botón X rojo para eliminar relojes\n"
            "✅ Solo relojes digitales (sin opción analógica)\n"
            "✅ Confirmación antes de eliminar\n"
            "✅ Mínimo 2 relojes, máximo 6 relojes\n"
            "✅ Configuración guardada automáticamente"
        )
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("margin: 10px; color: #27AE60; font-size: 9pt;")
        info.setWordWrap(True)
        layout.addWidget(info)
    
    def add_tokyo_clock(self):
        """Agregar reloj de Tokio"""
        if len(self.world_clocks.clocks) < 6:
            tokyo_clock = ClockWidget(
                timezone="Asia/Tokyo",
                city_name="Tokio"
            )
            self.world_clocks.add_clock_widget(tokyo_clock)
            self.world_clocks.save_clocks_config()
            print("Reloj de Tokio agregado")
        else:
            print("Máximo de relojes alcanzado")
    
    def add_ny_clock(self):
        """Agregar reloj de Nueva York"""
        if len(self.world_clocks.clocks) < 6:
            ny_clock = ClockWidget(
                timezone="America/New_York",
                city_name="Nueva York"
            )
            self.world_clocks.add_clock_widget(ny_clock)
            self.world_clocks.save_clocks_config()
            print("Reloj de Nueva York agregado")
        else:
            print("Máximo de relojes alcanzado")
    
    def add_sydney_clock(self):
        """Agregar reloj de Sydney"""
        if len(self.world_clocks.clocks) < 6:
            sydney_clock = ClockWidget(
                timezone="Australia/Sydney",
                city_name="Sydney"
            )
            self.world_clocks.add_clock_widget(sydney_clock)
            self.world_clocks.save_clocks_config()
            print("Reloj de Sydney agregado")
        else:
            print("Máximo de relojes alcanzado")
    
    def count_clocks(self):
        """Contar relojes actuales"""
        count = len(self.world_clocks.clocks)
        cities = [clock.city_name for clock in self.world_clocks.clocks]
        print(f"Relojes actuales: {count}")
        print(f"Ciudades: {', '.join(cities)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestClockRemovalWindow()
    window.show()
    
    sys.exit(app.exec_())

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget de carrusel de banners con rotación automática
"""

import os
import webbrowser
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QLineEdit, QFileDialog, QMessageBox,
                            QDialog, QDialogButtonBox, QFormLayout, QCheckBox,
                            QSpinBox, QListWidget, QListWidgetItem, QFrame,
                            QScrollArea, QGroupBox, QComboBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QPixmap, QFont, QCursor, QPainter, QColor


class BannerItem:
    """Clase para representar un banner individual"""

    def __init__(self, banner_id=None, enabled=True, title="", image_path="",
                 url="", alt_text="", duration=5000):
        self.id = banner_id or self.generate_id()
        self.enabled = enabled
        self.title = title
        self.image_path = image_path
        self.url = url
        self.alt_text = alt_text
        self.duration = duration  # Duración en milisegundos

    def generate_id(self):
        """Generar ID único para el banner"""
        import time
        return f"banner_{int(time.time() * 1000)}"

    def to_dict(self):
        """Convertir a diccionario para guardar"""
        return {
            'id': self.id,
            'enabled': self.enabled,
            'title': self.title,
            'image_path': self.image_path,
            'url': self.url,
            'alt_text': self.alt_text,
            'duration': self.duration
        }

    @classmethod
    def from_dict(cls, data):
        """Crear desde diccionario"""
        return cls(
            banner_id=data.get('id'),
            enabled=data.get('enabled', True),
            title=data.get('title', ''),
            image_path=data.get('image_path', ''),
            url=data.get('url', ''),
            alt_text=data.get('alt_text', ''),
            duration=data.get('duration', 5000)
        )


class BannerEditDialog(QDialog):
    """Diálogo para editar un banner individual"""

    def __init__(self, data_manager, banner_item=None, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.banner_item = banner_item or BannerItem()

        self.setWindowTitle("Editar Banner" if banner_item else "Nuevo Banner")
        self.setModal(True)
        self.setFixedSize(500, 450)

        self.setup_ui()
        self.load_banner_data()

    def setup_ui(self):
        """Configurar interfaz del diálogo"""
        layout = QVBoxLayout(self)

        # Título
        title = QLabel("📝 " + ("Editar Banner" if self.banner_item.id else "Nuevo Banner"))
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 10px;")
        layout.addWidget(title)

        # Formulario
        form_layout = QFormLayout()

        # Habilitar banner
        self.enabled_checkbox = QCheckBox("Banner activo")
        form_layout.addRow("Estado:", self.enabled_checkbox)

        # Título del banner
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("Título descriptivo del banner...")
        form_layout.addRow("Título:", self.title_edit)

        # Imagen
        image_layout = QHBoxLayout()
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setPlaceholderText("Ruta de la imagen...")
        self.browse_button = QPushButton("📁 Examinar")
        self.browse_button.clicked.connect(self.browse_image)
        image_layout.addWidget(self.image_path_edit)
        image_layout.addWidget(self.browse_button)
        form_layout.addRow("Imagen:", image_layout)

        # URL
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("https://ejemplo.com")
        form_layout.addRow("URL:", self.url_edit)

        # Texto alternativo
        self.alt_text_edit = QLineEdit()
        self.alt_text_edit.setPlaceholderText("Texto cuando no hay imagen")
        form_layout.addRow("Texto alternativo:", self.alt_text_edit)

        # Duración
        self.duration_spinbox = QSpinBox()
        self.duration_spinbox.setRange(1000, 30000)  # 1-30 segundos
        self.duration_spinbox.setValue(5000)
        self.duration_spinbox.setSuffix(" ms")
        self.duration_spinbox.setToolTip("Tiempo que se muestra este banner (milisegundos)")
        form_layout.addRow("Duración:", self.duration_spinbox)

        layout.addLayout(form_layout)

        # Vista previa
        preview_group = QGroupBox("Vista Previa")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_label = QLabel("Vista previa del banner")
        self.preview_label.setFixedHeight(60)
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setStyleSheet("""
            border: 2px solid #BDC3C7;
            background-color: #F8F9FA;
            border-radius: 3px;
            color: #7F8C8D;
            font-style: italic;
        """)
        preview_layout.addWidget(self.preview_label)

        layout.addWidget(preview_group)

        # Conectar señales para vista previa
        self.title_edit.textChanged.connect(self.update_preview)
        self.image_path_edit.textChanged.connect(self.update_preview)
        self.alt_text_edit.textChanged.connect(self.update_preview)
        self.enabled_checkbox.stateChanged.connect(self.update_preview)

        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def browse_image(self):
        """Examinar archivo de imagen"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar imagen para el banner",
            "",
            "Imágenes (*.png *.jpg *.jpeg *.gif *.bmp);;Todos los archivos (*)"
        )

        if file_path:
            self.image_path_edit.setText(file_path)

    def update_preview(self):
        """Actualizar vista previa"""
        if not self.enabled_checkbox.isChecked():
            self.preview_label.setText("Banner deshabilitado")
            self.preview_label.setStyleSheet("""
                border: 2px solid #E74C3C;
                background-color: #FADBD8;
                border-radius: 3px;
                color: #E74C3C;
                font-style: italic;
            """)
            return

        image_path = self.image_path_edit.text().strip()
        title = self.title_edit.text().strip()
        alt_text = self.alt_text_edit.text().strip()

        if image_path and os.path.exists(image_path):
            try:
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(200, 50, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.preview_label.setPixmap(scaled_pixmap)
                    self.preview_label.setText("")
                    self.preview_label.setStyleSheet("""
                        border: 2px solid #27AE60;
                        background-color: #E8F8F5;
                        border-radius: 3px;
                    """)
                else:
                    self.preview_label.setText(alt_text or title or "Imagen inválida")
                    self.preview_label.setStyleSheet("""
                        border: 2px solid #F39C12;
                        background-color: #FEF9E7;
                        border-radius: 3px;
                        color: #F39C12;
                    """)
            except Exception:
                self.preview_label.setText(alt_text or title or "Error cargando imagen")
                self.preview_label.setStyleSheet("""
                    border: 2px solid #E74C3C;
                    background-color: #FADBD8;
                    border-radius: 3px;
                    color: #E74C3C;
                """)
        else:
            display_text = title or alt_text or "Banner sin imagen"
            self.preview_label.setText(display_text)
            self.preview_label.setStyleSheet("""
                border: 2px solid #3498DB;
                background-color: #EBF5FB;
                border-radius: 3px;
                color: #2C3E50;
            """)

    def load_banner_data(self):
        """Cargar datos del banner en el formulario"""
        self.enabled_checkbox.setChecked(self.banner_item.enabled)
        self.title_edit.setText(self.banner_item.title)
        self.image_path_edit.setText(self.banner_item.image_path)
        self.url_edit.setText(self.banner_item.url)
        self.alt_text_edit.setText(self.banner_item.alt_text)
        self.duration_spinbox.setValue(self.banner_item.duration)

        self.update_preview()

    def get_banner_item(self):
        """Obtener banner item con los datos del formulario"""
        self.banner_item.enabled = self.enabled_checkbox.isChecked()
        self.banner_item.title = self.title_edit.text().strip()
        self.banner_item.image_path = self.image_path_edit.text().strip()
        self.banner_item.url = self.url_edit.text().strip()
        self.banner_item.alt_text = self.alt_text_edit.text().strip()
        self.banner_item.duration = self.duration_spinbox.value()

        return self.banner_item


class BannerManagementDialog(QDialog):
    """Diálogo para gestionar múltiples banners"""

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.banners = []

        self.setWindowTitle("🎠 Gestión de Banners")
        self.setModal(True)
        self.setFixedSize(700, 500)

        self.setup_ui()
        self.load_banners()

    def setup_ui(self):
        """Configurar interfaz del diálogo"""
        layout = QVBoxLayout(self)

        # Título
        title = QLabel("🎠 Gestión del Carrusel de Banners")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 15px;")
        layout.addWidget(title)

        # Información
        info = QLabel("Gestione múltiples banners que rotarán automáticamente en el carrusel")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("color: #7F8C8D; margin-bottom: 10px;")
        layout.addWidget(info)

        # Layout principal horizontal
        main_layout = QHBoxLayout()

        # Panel izquierdo - Lista de banners
        left_panel = QVBoxLayout()

        # Título de la lista
        list_title = QLabel("📋 Banners Configurados:")
        list_title.setFont(QFont("Arial", 12, QFont.Bold))
        list_title.setStyleSheet("color: #2C3E50; margin: 5px;")
        left_panel.addWidget(list_title)

        # Lista de banners
        self.banners_list = QListWidget()
        self.banners_list.setMinimumWidth(300)
        self.banners_list.itemSelectionChanged.connect(self.on_banner_selected)
        left_panel.addWidget(self.banners_list)

        # Botones de gestión
        buttons_layout = QHBoxLayout()

        self.btn_add = QPushButton("➕ Nuevo")
        self.btn_add.setToolTip("Agregar nuevo banner")
        self.btn_add.clicked.connect(self.add_banner)
        buttons_layout.addWidget(self.btn_add)

        self.btn_edit = QPushButton("✏️ Editar")
        self.btn_edit.setToolTip("Editar banner seleccionado")
        self.btn_edit.setEnabled(False)
        self.btn_edit.clicked.connect(self.edit_banner)
        buttons_layout.addWidget(self.btn_edit)

        self.btn_delete = QPushButton("🗑️ Eliminar")
        self.btn_delete.setToolTip("Eliminar banner seleccionado")
        self.btn_delete.setEnabled(False)
        self.btn_delete.clicked.connect(self.delete_banner)
        buttons_layout.addWidget(self.btn_delete)

        self.btn_duplicate = QPushButton("📋 Duplicar")
        self.btn_duplicate.setToolTip("Duplicar banner seleccionado")
        self.btn_duplicate.setEnabled(False)
        self.btn_duplicate.clicked.connect(self.duplicate_banner)
        buttons_layout.addWidget(self.btn_duplicate)

        left_panel.addLayout(buttons_layout)

        # Panel derecho - Configuración del carrusel
        right_panel = QVBoxLayout()

        # Configuración global
        config_group = QGroupBox("⚙️ Configuración del Carrusel")
        config_layout = QFormLayout(config_group)

        # Habilitar carrusel
        self.carousel_enabled = QCheckBox("Carrusel activo")
        config_layout.addRow("Estado:", self.carousel_enabled)

        # Altura del carrusel
        self.carousel_height = QSpinBox()
        self.carousel_height.setRange(40, 200)
        self.carousel_height.setValue(67)
        self.carousel_height.setSuffix(" px")
        config_layout.addRow("Altura:", self.carousel_height)

        # Transición
        self.transition_type = QComboBox()
        self.transition_type.addItems(["Deslizar", "Desvanecer", "Instantáneo"])
        config_layout.addRow("Transición:", self.transition_type)

        # Velocidad de transición
        self.transition_speed = QSpinBox()
        self.transition_speed.setRange(100, 2000)
        self.transition_speed.setValue(500)
        self.transition_speed.setSuffix(" ms")
        config_layout.addRow("Velocidad:", self.transition_speed)

        # Pausar al hover
        self.pause_on_hover = QCheckBox("Pausar al pasar el mouse")
        config_layout.addRow("Interacción:", self.pause_on_hover)

        right_panel.addWidget(config_group)

        # Vista previa del carrusel
        preview_group = QGroupBox("👁️ Vista Previa")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_widget = QLabel("Vista previa del carrusel")
        self.preview_widget.setFixedHeight(80)
        self.preview_widget.setAlignment(Qt.AlignCenter)
        self.preview_widget.setStyleSheet("""
            border: 2px solid #BDC3C7;
            background-color: #F8F9FA;
            border-radius: 5px;
            color: #7F8C8D;
            font-style: italic;
        """)
        preview_layout.addWidget(self.preview_widget)

        # Información de estado
        self.status_label = QLabel("📊 Estado: Sin banners configurados")
        self.status_label.setStyleSheet("color: #7F8C8D; font-size: 9pt; margin: 5px;")
        preview_layout.addWidget(self.status_label)

        right_panel.addWidget(preview_group)

        # Agregar paneles al layout principal
        main_layout.addLayout(left_panel, 2)
        main_layout.addLayout(right_panel, 1)
        layout.addLayout(main_layout)

        # Botones del diálogo
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.save_and_accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Conectar señales
        self.carousel_enabled.stateChanged.connect(self.update_preview)
        self.carousel_height.valueChanged.connect(self.update_preview)

    def load_banners(self):
        """Cargar banners desde la configuración"""
        config = self.data_manager.get_config('banner_carousel_config', {})

        # Cargar configuración global
        self.carousel_enabled.setChecked(config.get('enabled', True))
        self.carousel_height.setValue(config.get('height', 67))
        self.transition_type.setCurrentText(config.get('transition_type', 'Deslizar'))
        self.transition_speed.setValue(config.get('transition_speed', 500))
        self.pause_on_hover.setChecked(config.get('pause_on_hover', True))

        # Cargar banners
        banners_data = config.get('banners', [])
        self.banners = [BannerItem.from_dict(data) for data in banners_data]

        self.refresh_banners_list()
        self.update_preview()

    def refresh_banners_list(self):
        """Actualizar lista de banners"""
        self.banners_list.clear()

        for i, banner in enumerate(self.banners):
            status_icon = "✅" if banner.enabled else "❌"
            duration_text = f"{banner.duration/1000:.1f}s"

            item_text = f"{status_icon} {banner.title or f'Banner {i+1}'} ({duration_text})"

            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, i)  # Guardar índice

            # Color según estado
            if banner.enabled:
                item.setForeground(QColor("#27AE60"))
            else:
                item.setForeground(QColor("#E74C3C"))

            self.banners_list.addItem(item)

        self.update_status()

    def update_status(self):
        """Actualizar información de estado"""
        total = len(self.banners)
        enabled = sum(1 for b in self.banners if b.enabled)

        if total == 0:
            status = "📊 Estado: Sin banners configurados"
        else:
            status = f"📊 Estado: {enabled}/{total} banners activos"

        self.status_label.setText(status)

    def update_preview(self):
        """Actualizar vista previa"""
        if not self.carousel_enabled.isChecked():
            self.preview_widget.setText("Carrusel deshabilitado")
            self.preview_widget.setStyleSheet("""
                border: 2px solid #E74C3C;
                background-color: #FADBD8;
                border-radius: 5px;
                color: #E74C3C;
                font-style: italic;
            """)
            return

        enabled_banners = [b for b in self.banners if b.enabled]

        if not enabled_banners:
            self.preview_widget.setText("Sin banners activos para mostrar")
            self.preview_widget.setStyleSheet("""
                border: 2px solid #F39C12;
                background-color: #FEF9E7;
                border-radius: 5px;
                color: #F39C12;
                font-style: italic;
            """)
        else:
            total_duration = sum(b.duration for b in enabled_banners) / 1000
            self.preview_widget.setText(
                f"🎠 Carrusel activo\n"
                f"{len(enabled_banners)} banners • Ciclo: {total_duration:.1f}s"
            )
            self.preview_widget.setStyleSheet("""
                border: 2px solid #27AE60;
                background-color: #E8F8F5;
                border-radius: 5px;
                color: #27AE60;
                font-weight: bold;
            """)

    def on_banner_selected(self):
        """Manejar selección de banner"""
        selected = bool(self.banners_list.selectedItems())
        self.btn_edit.setEnabled(selected)
        self.btn_delete.setEnabled(selected)
        self.btn_duplicate.setEnabled(selected)

    def add_banner(self):
        """Agregar nuevo banner"""
        dialog = BannerEditDialog(self.data_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            banner = dialog.get_banner_item()
            self.banners.append(banner)
            self.refresh_banners_list()
            self.update_preview()

    def edit_banner(self):
        """Editar banner seleccionado"""
        selected_items = self.banners_list.selectedItems()
        if not selected_items:
            return

        index = selected_items[0].data(Qt.UserRole)
        banner = self.banners[index]

        dialog = BannerEditDialog(self.data_manager, banner, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.banners[index] = dialog.get_banner_item()
            self.refresh_banners_list()
            self.update_preview()

    def delete_banner(self):
        """Eliminar banner seleccionado"""
        selected_items = self.banners_list.selectedItems()
        if not selected_items:
            return

        index = selected_items[0].data(Qt.UserRole)
        banner = self.banners[index]

        reply = QMessageBox.question(
            self,
            "Confirmar Eliminación",
            f"¿Está seguro de eliminar el banner '{banner.title or f'Banner {index+1}'}'?",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            del self.banners[index]
            self.refresh_banners_list()
            self.update_preview()

    def duplicate_banner(self):
        """Duplicar banner seleccionado"""
        selected_items = self.banners_list.selectedItems()
        if not selected_items:
            return

        index = selected_items[0].data(Qt.UserRole)
        original_banner = self.banners[index]

        # Crear copia
        new_banner = BannerItem(
            enabled=original_banner.enabled,
            title=f"{original_banner.title} (Copia)",
            image_path=original_banner.image_path,
            url=original_banner.url,
            alt_text=original_banner.alt_text,
            duration=original_banner.duration
        )

        self.banners.append(new_banner)
        self.refresh_banners_list()
        self.update_preview()

    def save_and_accept(self):
        """Guardar configuración y cerrar"""
        config = {
            'enabled': self.carousel_enabled.isChecked(),
            'height': self.carousel_height.value(),
            'transition_type': self.transition_type.currentText(),
            'transition_speed': self.transition_speed.value(),
            'pause_on_hover': self.pause_on_hover.isChecked(),
            'banners': [banner.to_dict() for banner in self.banners]
        }

        self.data_manager.set_config('banner_carousel_config', config)
        self.accept()


class BannerCarouselWidget(QWidget):
    """Widget principal del carrusel de banners"""

    banner_clicked = pyqtSignal(str)  # Señal cuando se hace clic en un banner

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.banners = []
        self.current_banner_index = 0
        self.is_paused = False

        # Timer para rotación automática
        self.rotation_timer = QTimer()
        self.rotation_timer.timeout.connect(self.next_banner)

        # Timer para transiciones
        self.transition_timer = QTimer()
        self.transition_timer.setSingleShot(True)

        self.setup_ui()
        self.load_carousel_config()

    def setup_ui(self):
        """Configurar interfaz del carrusel"""
        # Layout principal
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 10, 40, 10)  # Margen derecho para botón

        # Altura por defecto
        self.carousel_height = 67
        self.setFixedHeight(self.carousel_height)

        # Estilo del carrusel
        self.setStyleSheet("""
            BannerCarouselWidget {
                background-color: #FFFFFF;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
            }
            BannerCarouselWidget:hover {
                border: 2px solid #3498DB;
                background-color: #F8F9FA;
            }
        """)

        # Widget de contenido del banner actual
        self.content_widget = QWidget()
        self.content_layout = QHBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)

        layout.addWidget(self.content_widget)

        # Botón de configuración
        self.config_button = QPushButton("🎠")
        self.config_button.setFixedSize(28, 28)
        self.config_button.setToolTip("Configurar carrusel de banners")
        self.config_button.setStyleSheet("""
            QPushButton {
                background-color: #E8F6F3;
                border: 2px solid #1ABC9C;
                border-radius: 14px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #D5F4E6;
                border: 2px solid #16A085;
            }
            QPushButton:pressed {
                background-color: #A3E4D7;
            }
        """)
        self.config_button.clicked.connect(self.configure_carousel)

        layout.addWidget(self.config_button, 0, Qt.AlignTop | Qt.AlignRight)

        # Configurar eventos del mouse
        self.setCursor(QCursor(Qt.PointingHandCursor))

        # Eventos para pausar/reanudar
        self.enterEvent = self.on_mouse_enter
        self.leaveEvent = self.on_mouse_leave

    def load_carousel_config(self):
        """Cargar configuración del carrusel"""
        config = self.data_manager.get_config('banner_carousel_config', {})

        # Configuración global
        self.carousel_enabled = config.get('enabled', True)
        self.carousel_height = config.get('height', 67)
        self.transition_type = config.get('transition_type', 'Deslizar')
        self.transition_speed = config.get('transition_speed', 500)
        self.pause_on_hover = config.get('pause_on_hover', True)

        # Actualizar altura
        self.setFixedHeight(self.carousel_height)

        # Cargar banners
        banners_data = config.get('banners', [])
        self.banners = [BannerItem.from_dict(data) for data in banners_data if data.get('enabled', True)]

        # Reiniciar carrusel
        self.current_banner_index = 0
        self.update_display()
        self.start_rotation()

    def update_display(self):
        """Actualizar visualización del banner actual"""
        # Limpiar contenido anterior
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not self.carousel_enabled:
            # Carrusel deshabilitado
            label = QLabel("🎠 Carrusel deshabilitado - Haga clic para configurar")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("color: #7F8C8D; font-style: italic; font-size: 11px;")
            self.content_layout.addWidget(label)
            return

        if not self.banners:
            # Sin banners
            label = QLabel("🎠 Sin banners configurados - Haga clic para agregar")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("color: #E67E22; font-style: italic; font-size: 11px;")
            self.content_layout.addWidget(label)
            return

        # Mostrar banner actual
        current_banner = self.banners[self.current_banner_index]
        self.display_banner(current_banner)

    def display_banner(self, banner):
        """Mostrar un banner específico"""
        if banner.image_path and os.path.exists(banner.image_path):
            # Mostrar imagen
            try:
                pixmap = QPixmap(banner.image_path)
                if not pixmap.isNull():
                    # Escalar imagen
                    available_width = self.width() - 70  # Margen para botón
                    available_height = self.carousel_height - 20

                    scaled_pixmap = pixmap.scaled(
                        available_width, available_height,
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )

                    image_label = QLabel()
                    image_label.setPixmap(scaled_pixmap)
                    image_label.setAlignment(Qt.AlignCenter)
                    self.content_layout.addWidget(image_label)
                else:
                    # Imagen inválida
                    self.display_text_banner(banner)
            except Exception as e:
                print(f"Error cargando imagen del banner: {str(e)}")
                self.display_text_banner(banner)
        else:
            # Mostrar texto
            self.display_text_banner(banner)

    def display_text_banner(self, banner):
        """Mostrar banner de texto"""
        text = banner.title or banner.alt_text or "Banner sin contenido"

        text_label = QLabel(text)
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setStyleSheet("""
            color: #2C3E50;
            font-weight: bold;
            font-size: 12pt;
            padding: 5px;
        """)
        text_label.setWordWrap(True)
        self.content_layout.addWidget(text_label)

    def start_rotation(self):
        """Iniciar rotación automática"""
        self.rotation_timer.stop()

        if self.carousel_enabled and len(self.banners) > 1:
            current_banner = self.banners[self.current_banner_index]
            self.rotation_timer.start(current_banner.duration)

    def stop_rotation(self):
        """Detener rotación automática"""
        self.rotation_timer.stop()

    def next_banner(self):
        """Cambiar al siguiente banner"""
        if not self.banners:
            return

        self.current_banner_index = (self.current_banner_index + 1) % len(self.banners)

        # Aplicar transición
        if self.transition_type == "Deslizar":
            self.slide_transition()
        elif self.transition_type == "Desvanecer":
            self.fade_transition()
        else:  # Instantáneo
            self.update_display()
            self.start_rotation()

    def slide_transition(self):
        """Transición deslizante"""
        # Por simplicidad, usar transición instantánea por ahora
        # En una implementación completa, se usarían QPropertyAnimation
        self.update_display()
        self.start_rotation()

    def fade_transition(self):
        """Transición de desvanecimiento"""
        # Por simplicidad, usar transición instantánea por ahora
        self.update_display()
        self.start_rotation()

    def on_mouse_enter(self, event):
        """Manejar entrada del mouse"""
        if self.pause_on_hover and not self.is_paused:
            self.is_paused = True
            self.stop_rotation()

    def on_mouse_leave(self, event):
        """Manejar salida del mouse"""
        if self.pause_on_hover and self.is_paused:
            self.is_paused = False
            self.start_rotation()

    def mousePressEvent(self, event):
        """Manejar clic en el carrusel"""
        if event.button() == Qt.LeftButton and self.banners:
            current_banner = self.banners[self.current_banner_index]
            if current_banner.url:
                try:
                    webbrowser.open(current_banner.url)
                    self.banner_clicked.emit(current_banner.url)
                except Exception as e:
                    QMessageBox.warning(self, "Error", f"No se pudo abrir la URL: {str(e)}")

        super().mousePressEvent(event)

    def configure_carousel(self):
        """Configurar el carrusel"""
        try:
            dialog = BannerManagementDialog(self.data_manager, self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_carousel_config()
                QMessageBox.information(
                    self,
                    "Configuración Guardada",
                    "El carrusel de banners ha sido configurado correctamente."
                )
        except Exception as e:
            print(f"Error configurando carrusel: {str(e)}")
            QMessageBox.warning(self, "Error", f"Error configurando carrusel: {str(e)}")

    def resizeEvent(self, event):
        """Manejar redimensionamiento"""
        super().resizeEvent(event)
        if hasattr(self, 'content_layout'):
            self.update_display()

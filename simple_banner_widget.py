#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget de banner simplificado y funcional
"""

import os
import webbrowser
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QFileDialog, QMessageBox,
                            QDialog, QDialogButtonBox, QFormLayout, QCheckBox,
                            QSpinBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QFont, QCursor


class SimpleBannerConfigDialog(QDialog):
    """Diálogo simplificado para configurar el banner"""
    
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setWindowTitle("Configurar Banner")
        self.setModal(True)
        self.setFixedSize(500, 400)
        
        self.setup_ui()
        self.load_current_config()
    
    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)
        
        # Título
        title = QLabel("Configuración del Banner")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 10px;")
        layout.addWidget(title)
        
        # Formulario
        form_layout = QFormLayout()
        
        # Habilitar banner
        self.enabled_checkbox = QCheckBox("Habilitar banner")
        form_layout.addRow("Estado:", self.enabled_checkbox)
        
        # Imagen
        image_layout = QHBoxLayout()
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setPlaceholderText("Ruta de la imagen...")
        self.browse_button = QPushButton("Examinar")
        self.browse_button.clicked.connect(self.browse_image)
        image_layout.addWidget(self.image_path_edit)
        image_layout.addWidget(self.browse_button)
        form_layout.addRow("Imagen:", image_layout)
        
        # URL
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("https://ejemplo.com")
        form_layout.addRow("URL:", self.url_edit)
        
        # Texto alternativo
        self.alt_text_edit = QLineEdit()
        self.alt_text_edit.setPlaceholderText("Texto cuando no hay imagen")
        form_layout.addRow("Texto alternativo:", self.alt_text_edit)
        
        # Altura del banner
        self.height_spinbox = QSpinBox()
        self.height_spinbox.setRange(40, 200)
        self.height_spinbox.setValue(67)
        self.height_spinbox.setSuffix(" px")
        form_layout.addRow("Altura del banner:", self.height_spinbox)
        
        layout.addLayout(form_layout)
        
        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def browse_image(self):
        """Examinar archivo de imagen"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar imagen",
            "",
            "Imágenes (*.png *.jpg *.jpeg *.gif *.bmp);;Todos los archivos (*)"
        )
        
        if file_path:
            self.image_path_edit.setText(file_path)
    
    def load_current_config(self):
        """Cargar configuración actual"""
        config = self.data_manager.get_config('banner_config', {})
        
        self.enabled_checkbox.setChecked(config.get('enabled', False))
        self.image_path_edit.setText(config.get('image_path', ''))
        self.url_edit.setText(config.get('url', ''))
        self.alt_text_edit.setText(config.get('alt_text', ''))
        self.height_spinbox.setValue(config.get('height', 67))
    
    def get_config(self):
        """Obtener configuración del diálogo"""
        return {
            'enabled': self.enabled_checkbox.isChecked(),
            'image_path': self.image_path_edit.text().strip(),
            'url': self.url_edit.text().strip(),
            'alt_text': self.alt_text_edit.text().strip(),
            'height': self.height_spinbox.value()
        }


class SimpleBannerWidget(QWidget):
    """Widget de banner simplificado"""
    
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.banner_height = 67
        self.url = None
        
        self.setup_ui()
        self.load_banner_config()
    
    def setup_ui(self):
        """Configurar la interfaz del banner"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 10, 40, 10)
        
        # Altura configurable
        self.setFixedHeight(self.banner_height)
        self.setStyleSheet("""
            SimpleBannerWidget {
                background-color: #FFFFFF;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
            }
            SimpleBannerWidget:hover {
                border: 2px solid #3498DB;
                background-color: #F8F9FA;
            }
        """)
        
        # Widget de contenido
        self.content_widget = QWidget()
        self.content_layout = QHBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        
        layout.addWidget(self.content_widget)
        
        # Botón de configuración
        self.config_button = QPushButton("⚙")
        self.config_button.setFixedSize(24, 24)
        self.config_button.setToolTip("Configurar banner")
        self.config_button.setStyleSheet("""
            QPushButton {
                background-color: #ECF0F1;
                border: 1px solid #BDC3C7;
                border-radius: 12px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #D5DBDB;
                border: 2px solid #3498DB;
            }
        """)
        self.config_button.clicked.connect(self.configure_banner)
        
        layout.addWidget(self.config_button, 0, Qt.AlignTop | Qt.AlignRight)
        
        # Cursor clickeable
        self.setCursor(QCursor(Qt.PointingHandCursor))
    
    def load_banner_config(self):
        """Cargar y aplicar configuración del banner"""
        config = self.data_manager.get_config('banner_config', {})
        
        # Actualizar altura
        self.banner_height = config.get('height', 67)
        self.setFixedHeight(self.banner_height)
        
        # Limpiar contenido anterior
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        if not config.get('enabled', False):
            # Banner deshabilitado
            label = QLabel("Banner deshabilitado - Haga clic en ⚙ para configurar")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("color: #7F8C8D; font-style: italic; font-size: 10px;")
            self.content_layout.addWidget(label)
            self.url = None
            return
        
        # Banner habilitado
        image_path = config.get('image_path', '')
        self.url = config.get('url', '')
        alt_text = config.get('alt_text', '')
        
        if image_path and os.path.exists(image_path):
            # Mostrar imagen
            try:
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # Escalar imagen
                    available_width = self.width() - 60
                    available_height = self.banner_height - 20
                    
                    scaled_pixmap = pixmap.scaled(
                        available_width, available_height, 
                        Qt.KeepAspectRatio, 
                        Qt.SmoothTransformation
                    )
                    
                    image_label = QLabel()
                    image_label.setPixmap(scaled_pixmap)
                    image_label.setAlignment(Qt.AlignCenter)
                    self.content_layout.addWidget(image_label)
                else:
                    # Imagen inválida
                    text_label = QLabel(alt_text or "Imagen no válida")
                    text_label.setAlignment(Qt.AlignCenter)
                    text_label.setStyleSheet("color: #E74C3C; font-weight: bold;")
                    self.content_layout.addWidget(text_label)
            except Exception as e:
                print(f"Error cargando imagen: {str(e)}")
                text_label = QLabel(alt_text or "Error cargando imagen")
                text_label.setAlignment(Qt.AlignCenter)
                text_label.setStyleSheet("color: #E74C3C; font-weight: bold;")
                self.content_layout.addWidget(text_label)
        else:
            # Mostrar texto alternativo
            text_label = QLabel(alt_text or "Banner sin imagen")
            text_label.setAlignment(Qt.AlignCenter)
            text_label.setStyleSheet("color: #2C3E50; font-weight: bold;")
            self.content_layout.addWidget(text_label)
    
    def mousePressEvent(self, event):
        """Manejar clic en el banner"""
        if event.button() == Qt.LeftButton and self.url:
            try:
                webbrowser.open(self.url)
            except Exception as e:
                QMessageBox.warning(self, "Error", f"No se pudo abrir la URL: {str(e)}")
        
        super().mousePressEvent(event)
    
    def configure_banner(self):
        """Configurar el banner"""
        try:
            config_dialog = SimpleBannerConfigDialog(self.data_manager, self)
            if config_dialog.exec_() == QDialog.Accepted:
                config = config_dialog.get_config()
                self.data_manager.set_config('banner_config', config)
                self.load_banner_config()
                QMessageBox.information(self, "Configuración Guardada", "El banner ha sido configurado correctamente.")
        except Exception as e:
            print(f"Error configurando banner: {str(e)}")
            QMessageBox.warning(self, "Error", f"Error configurando banner: {str(e)}")
    
    def resizeEvent(self, event):
        """Manejar redimensionamiento del widget"""
        super().resizeEvent(event)
        if hasattr(self, 'content_layout'):
            self.load_banner_config()

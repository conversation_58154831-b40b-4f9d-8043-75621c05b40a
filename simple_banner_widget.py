#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget de banner simplificado y funcional
"""

import os
import webbrowser
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QLineEdit, QFileDialog, QMessageBox,
                            QDialog, QDialogButtonBox, QFormLayout, QCheckBox,
                            QSpinBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QFont, QCursor


class MasterPasswordDialog(QDialog):
    """Diálogo para contraseña maestra simplificado"""

    def __init__(self, data_manager, is_first_time=False, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.is_first_time = is_first_time

        if is_first_time:
            self.setWindowTitle("🔐 Configurar Contraseña Maestra")
            self.setFixedSize(450, 300)
        else:
            self.setWindowTitle("🔐 Acceso Requerido")
            self.setFixedSize(400, 250)

        self.setModal(True)
        self.setup_ui()

    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)

        if self.is_first_time:
            # Primera vez - configurar contraseña maestra
            self.setup_first_time_ui(layout)
        else:
            # Acceso - solicitar contraseña maestra
            self.setup_access_ui(layout)

        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.validate_and_accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def setup_first_time_ui(self, layout):
        """Configurar UI para primera vez"""
        # Título
        title = QLabel("🔐 Configurar Contraseña Maestra")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 15px;")
        layout.addWidget(title)

        # Mensaje
        message = QLabel(
            "Es la primera vez que configura el banner.\n"
            "Establezca una contraseña maestra para proteger la configuración:"
        )
        message.setWordWrap(True)
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("color: #2980B9; margin: 15px; font-size: 11pt;")
        layout.addWidget(message)

        # Formulario
        form_layout = QFormLayout()

        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("Mínimo 6 caracteres...")
        form_layout.addRow("Nueva contraseña:", self.password_edit)

        self.confirm_edit = QLineEdit()
        self.confirm_edit.setEchoMode(QLineEdit.Password)
        self.confirm_edit.setPlaceholderText("Repetir contraseña...")
        form_layout.addRow("Confirmar:", self.confirm_edit)

        layout.addLayout(form_layout)

        # Información de seguridad
        security_info = QLabel(
            "🔒 La contraseña maestra protegerá la configuración del banner\n"
            "y será requerida para cualquier modificación futura."
        )
        security_info.setWordWrap(True)
        security_info.setAlignment(Qt.AlignCenter)
        security_info.setStyleSheet("color: #27AE60; font-size: 10pt; margin: 15px;")
        layout.addWidget(security_info)

        self.password_edit.setFocus()

    def setup_access_ui(self, layout):
        """Configurar UI para acceso"""
        # Título
        title = QLabel("🔐 Acceso Requerido")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 15px;")
        layout.addWidget(title)

        # Mensaje
        message = QLabel("Ingrese la contraseña maestra para acceder a la configuración del banner:")
        message.setWordWrap(True)
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("color: #2980B9; margin: 15px; font-size: 11pt;")
        layout.addWidget(message)

        # Campo de contraseña
        form_layout = QFormLayout()
        self.access_password_edit = QLineEdit()
        self.access_password_edit.setEchoMode(QLineEdit.Password)
        self.access_password_edit.setPlaceholderText("Contraseña maestra...")
        form_layout.addRow("Contraseña:", self.access_password_edit)
        layout.addLayout(form_layout)

        # Información adicional
        info = QLabel("🔒 Esta contraseña protege la configuración del banner")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("color: #7F8C8D; font-size: 10pt; margin: 15px;")
        layout.addWidget(info)

        self.access_password_edit.setFocus()

    def validate_and_accept(self):
        """Validar según el contexto y aceptar"""
        if self.is_first_time:
            self.validate_first_time()
        else:
            self.validate_access()

    def validate_first_time(self):
        """Validar configuración de primera vez"""
        password = self.password_edit.text()
        confirm = self.confirm_edit.text()

        if len(password) < 6:
            QMessageBox.warning(self, "Error", "La contraseña debe tener al menos 6 caracteres.")
            return

        if password != confirm:
            QMessageBox.warning(self, "Error", "Las contraseñas no coinciden.")
            return

        # Guardar contraseña maestra
        self.data_manager.set_config('banner_master_password', password)
        self.accept()

    def validate_access(self):
        """Validar acceso con contraseña maestra"""
        password = self.access_password_edit.text()

        if not password:
            QMessageBox.warning(self, "Error", "Debe ingresar la contraseña maestra.")
            return

        # Verificar contraseña
        stored_password = self.data_manager.get_config('banner_master_password', None)
        if password != stored_password:
            QMessageBox.warning(self, "Acceso Denegado", "Contraseña maestra incorrecta.")
            return

        self.accept()

    def get_password(self):
        """Obtener la contraseña ingresada"""
        if self.is_first_time:
            return self.password_edit.text()
        else:
            return self.access_password_edit.text()


class SimpleBannerConfigDialog(QDialog):
    """Diálogo simplificado para configurar el banner"""

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setWindowTitle("Configurar Banner")
        self.setModal(True)
        self.setFixedSize(500, 400)

        self.setup_ui()
        self.load_current_config()

    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        layout = QVBoxLayout(self)

        # Título
        title = QLabel("Configuración del Banner")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; margin: 10px;")
        layout.addWidget(title)

        # Formulario
        form_layout = QFormLayout()

        # Habilitar banner
        self.enabled_checkbox = QCheckBox("Habilitar banner")
        form_layout.addRow("Estado:", self.enabled_checkbox)

        # Imagen
        image_layout = QHBoxLayout()
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setPlaceholderText("Ruta de la imagen...")
        self.browse_button = QPushButton("Examinar")
        self.browse_button.clicked.connect(self.browse_image)
        image_layout.addWidget(self.image_path_edit)
        image_layout.addWidget(self.browse_button)
        form_layout.addRow("Imagen:", image_layout)

        # URL
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("https://ejemplo.com")
        form_layout.addRow("URL:", self.url_edit)

        # Texto alternativo
        self.alt_text_edit = QLineEdit()
        self.alt_text_edit.setPlaceholderText("Texto cuando no hay imagen")
        form_layout.addRow("Texto alternativo:", self.alt_text_edit)

        # Altura del banner
        self.height_spinbox = QSpinBox()
        self.height_spinbox.setRange(40, 200)
        self.height_spinbox.setValue(67)
        self.height_spinbox.setSuffix(" px")
        form_layout.addRow("Altura del banner:", self.height_spinbox)

        layout.addLayout(form_layout)

        # Información de dimensiones
        dimensions_info = QLabel("📐 Banner: 600x200 píxeles (tamaño fijo centrado)")
        dimensions_info.setAlignment(Qt.AlignCenter)
        dimensions_info.setStyleSheet("color: #7F8C8D; font-size: 10pt; font-style: italic; margin: 10px;")
        layout.addWidget(dimensions_info)

        # Botones
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def browse_image(self):
        """Examinar archivo de imagen"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar imagen",
            "",
            "Imágenes (*.png *.jpg *.jpeg *.gif *.bmp);;Todos los archivos (*)"
        )

        if file_path:
            self.image_path_edit.setText(file_path)

    def load_current_config(self):
        """Cargar configuración actual"""
        config = self.data_manager.get_config('banner_config', {})

        self.enabled_checkbox.setChecked(config.get('enabled', False))
        self.image_path_edit.setText(config.get('image_path', ''))
        self.url_edit.setText(config.get('url', ''))
        self.alt_text_edit.setText(config.get('alt_text', ''))
        self.height_spinbox.setValue(config.get('height', 67))

    def get_config(self):
        """Obtener configuración del diálogo"""
        return {
            'enabled': self.enabled_checkbox.isChecked(),
            'image_path': self.image_path_edit.text().strip(),
            'url': self.url_edit.text().strip(),
            'alt_text': self.alt_text_edit.text().strip(),
            'height': self.height_spinbox.value()
        }


class SimpleBannerWidget(QWidget):
    """Widget de banner simplificado"""

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.banner_height = 67
        self.url = None

        self.setup_ui()
        self.load_banner_config()

    def setup_ui(self):
        """Configurar la interfaz del banner"""
        # Layout principal para centrar el banner
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # Espaciador izquierdo
        main_layout.addStretch()

        # Widget del banner con tamaño fijo 600x200px
        self.banner_widget = QWidget()
        self.banner_widget.setFixedSize(600, 200)  # Tamaño fijo 600x200px
        self.banner_widget.setStyleSheet("""
            QWidget {
                background-color: #FFFFFF;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
            }
            QWidget:hover {
                border: 2px solid #3498DB;
                background-color: #F8F9FA;
            }
        """)

        # Layout interno del banner
        banner_layout = QHBoxLayout(self.banner_widget)
        banner_layout.setContentsMargins(10, 10, 40, 10)  # Margen derecho para botón

        # Widget de contenido
        self.content_widget = QWidget()
        self.content_layout = QHBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)

        banner_layout.addWidget(self.content_widget)

        # Botón de configuración
        self.config_button = QPushButton("⚙")
        self.config_button.setFixedSize(24, 24)
        self.config_button.setToolTip("Configurar banner")
        self.config_button.setStyleSheet("""
            QPushButton {
                background-color: #ECF0F1;
                border: 1px solid #BDC3C7;
                border-radius: 12px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #D5DBDB;
                border: 2px solid #3498DB;
            }
        """)
        self.config_button.clicked.connect(self.configure_banner)

        banner_layout.addWidget(self.config_button, 0, Qt.AlignTop | Qt.AlignRight)

        # Agregar banner widget al layout principal
        main_layout.addWidget(self.banner_widget)

        # Espaciador derecho
        main_layout.addStretch()

        # Cursor clickeable
        self.setCursor(QCursor(Qt.PointingHandCursor))

    def load_banner_config(self):
        """Cargar y aplicar configuración del banner"""
        config = self.data_manager.get_config('banner_config', {})

        # Actualizar altura
        self.banner_height = config.get('height', 67)
        self.setFixedHeight(self.banner_height)

        # Limpiar contenido anterior
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not config.get('enabled', False):
            # Banner deshabilitado
            label = QLabel("Banner deshabilitado - Haga clic en ⚙ para configurar")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("color: #7F8C8D; font-style: italic; font-size: 10px;")
            self.content_layout.addWidget(label)
            self.url = None
            return

        # Banner habilitado
        image_path = config.get('image_path', '')
        self.url = config.get('url', '')
        alt_text = config.get('alt_text', '')

        if image_path and os.path.exists(image_path):
            # Mostrar imagen
            try:
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # Escalar imagen para el banner fijo (580x180 máximo)
                    available_width = 580  # 600 - 20 margen
                    available_height = 180  # 200 - 20 margen

                    scaled_pixmap = pixmap.scaled(
                        available_width, available_height,
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )

                    image_label = QLabel()
                    image_label.setPixmap(scaled_pixmap)
                    image_label.setAlignment(Qt.AlignCenter)
                    self.content_layout.addWidget(image_label)
                else:
                    # Imagen inválida
                    text_label = QLabel(alt_text or "Imagen no válida")
                    text_label.setAlignment(Qt.AlignCenter)
                    text_label.setStyleSheet("color: #E74C3C; font-weight: bold;")
                    self.content_layout.addWidget(text_label)
            except Exception as e:
                print(f"Error cargando imagen: {str(e)}")
                text_label = QLabel(alt_text or "Error cargando imagen")
                text_label.setAlignment(Qt.AlignCenter)
                text_label.setStyleSheet("color: #E74C3C; font-weight: bold;")
                self.content_layout.addWidget(text_label)
        else:
            # Mostrar texto alternativo
            text_label = QLabel(alt_text or "Banner sin imagen")
            text_label.setAlignment(Qt.AlignCenter)
            text_label.setStyleSheet("color: #2C3E50; font-weight: bold;")
            self.content_layout.addWidget(text_label)

    def mousePressEvent(self, event):
        """Manejar clic en el banner"""
        if event.button() == Qt.LeftButton and self.url:
            try:
                webbrowser.open(self.url)
            except Exception as e:
                QMessageBox.warning(self, "Error", f"No se pudo abrir la URL: {str(e)}")

        super().mousePressEvent(event)

    def configure_banner(self):
        """Configurar el banner (requiere contraseña maestra)"""
        try:
            # Verificar si existe contraseña maestra
            master_password = self.data_manager.get_config('banner_master_password', None)

            if master_password is None:
                # Primera vez - configurar contraseña maestra
                password_dialog = MasterPasswordDialog(self.data_manager, is_first_time=True, parent=self)
                if password_dialog.exec_() != QDialog.Accepted:
                    return

                QMessageBox.information(
                    self,
                    "Contraseña Configurada",
                    "Contraseña maestra configurada correctamente.\n"
                    "Recuerde esta contraseña para futuras configuraciones."
                )
            else:
                # Solicitar contraseña maestra existente
                password_dialog = MasterPasswordDialog(self.data_manager, is_first_time=False, parent=self)
                if password_dialog.exec_() != QDialog.Accepted:
                    return

            # Mostrar diálogo de configuración del banner
            config_dialog = SimpleBannerConfigDialog(self.data_manager, self)
            if config_dialog.exec_() == QDialog.Accepted:
                config = config_dialog.get_config()
                self.data_manager.set_config('banner_config', config)
                self.load_banner_config()
                QMessageBox.information(self, "Configuración Guardada", "El banner ha sido configurado correctamente.")

        except Exception as e:
            print(f"Error configurando banner: {str(e)}")
            QMessageBox.warning(self, "Error", f"Error configurando banner: {str(e)}")

    def resizeEvent(self, event):
        """Manejar redimensionamiento del widget"""
        super().resizeEvent(event)
        if hasattr(self, 'content_layout'):
            self.load_banner_config()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar que los relojes mundiales funcionan correctamente
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

from world_clocks_widget import WorldClocksWidget
from data_manager import DataManager
from styles import apply_styles


class TestClocksWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Relojes Mundiales")
        self.setGeometry(100, 100, 800, 400)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Agregar widget de relojes
        self.world_clocks = WorldClocksWidget(self.data_manager)
        layout.addWidget(self.world_clocks)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestClocksWindow()
    window.show()
    
    sys.exit(app.exec_())

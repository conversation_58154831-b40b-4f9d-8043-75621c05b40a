#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os
import json
import datetime
import shutil
import zipfile
import tempfile
from PyQt5.QtCore import QDate, QTime, QDateTime


class DataManager:
    def __init__(self, app_data_path=None):
        # Definir la ruta de los datos de la aplicación
        if app_data_path is None:
            app_data_path = os.path.join(os.path.expanduser('~'), '.cypher_calendar')
            if not os.path.exists(app_data_path):
                os.makedirs(app_data_path)

        self.app_data_path = app_data_path
        self.db_path = os.path.join(app_data_path, 'calendar_data.db')
        self.config_path = os.path.join(app_data_path, 'config.json')

        # Inicializar la base de datos
        self.init_database()

        # Cargar configuración
        self.config = self.load_config()

    def init_database(self):
        """Inicializar las tablas de la base de datos"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Tabla de eventos
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS events (
            id INTEGER PRIMARY KEY,
            title TEXT NOT NULL,
            date TEXT NOT NULL,
            time_start TEXT NOT NULL,
            time_end TEXT NOT NULL,
            description TEXT,
            category_id INTEGER,
            all_day INTEGER DEFAULT 0,
            reminder TEXT,
            reminder_sent INTEGER DEFAULT 0,
            color TEXT DEFAULT '#1E90FF',
            priority INTEGER DEFAULT 1,
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
        ''')

        # Agregar columna de prioridad si no existe (para bases de datos existentes)
        try:
            cursor.execute('ALTER TABLE events ADD COLUMN priority INTEGER DEFAULT 1')
        except sqlite3.OperationalError:
            # La columna ya existe
            pass

        # Tabla de notas
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS notes (
            id INTEGER PRIMARY KEY,
            title TEXT NOT NULL,
            content TEXT,
            created_date TEXT NOT NULL,
            modified_date TEXT NOT NULL,
            category_id INTEGER,
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
        ''')

        # Tabla de categorías
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            color TEXT DEFAULT '#1E90FF'
        )
        ''')

        conn.commit()
        conn.close()

    def load_config(self):
        """Cargar la configuración de la aplicación"""
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r') as f:
                return json.load(f)
        else:
            # Configuración por defecto
            default_config = {
                "timezone": "local",
                "theme": "light",
                "reminder_sound": "default",
                "reminder_advance_time": 15,  # minutos
                "minimize_to_tray": True,
                "start_with_system": False,
                "world_clocks": [
                    {
                        "timezone": "Europe/Madrid",
                        "city_name": "Madrid",
                        "clock_type": "digital",
                        "alarms": []
                    },
                    {
                        "timezone": "America/Sao_Paulo",
                        "city_name": "São Paulo",
                        "clock_type": "digital",
                        "alarms": []
                    }
                ]
            }
            self.save_config(default_config)
            return default_config

    def save_config(self, config=None):
        """Guardar la configuración de la aplicación"""
        if config is None:
            config = self.config

        with open(self.config_path, 'w') as f:
            json.dump(config, f, indent=4)

    def get_config(self, key, default=None):
        """Obtener un valor de configuración"""
        return self.config.get(key, default)

    def set_config(self, key, value):
        """Establecer un valor de configuración"""
        self.config[key] = value
        self.save_config()

    # ----- Métodos para eventos -----

    def add_event(self, event_data):
        """Añadir un nuevo evento"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
        INSERT INTO events (title, date, time_start, time_end, description,
                           category_id, all_day, reminder, color, priority)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            event_data['title'],
            event_data['date'],
            event_data['time_start'],
            event_data['time_end'],
            event_data['description'],
            event_data.get('category_id'),
            event_data.get('all_day', 0),
            event_data.get('reminder'),
            event_data.get('color', '#1E90FF'),
            event_data.get('priority', 1)
        ))

        event_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return event_id

    def update_event(self, event_id, event_data):
        """Actualizar un evento existente"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
        UPDATE events
        SET title = ?, date = ?, time_start = ?, time_end = ?, description = ?,
            category_id = ?, all_day = ?, reminder = ?, color = ?, priority = ?
        WHERE id = ?
        ''', (
            event_data['title'],
            event_data['date'],
            event_data['time_start'],
            event_data['time_end'],
            event_data['description'],
            event_data.get('category_id'),
            event_data.get('all_day', 0),
            event_data.get('reminder'),
            event_data.get('color', '#1E90FF'),
            event_data.get('priority', 1),
            event_id
        ))

        conn.commit()
        conn.close()

        return cursor.rowcount > 0

    def update_event_priority(self, event_id, priority):
        """Actualizar solo la prioridad de un evento"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('UPDATE events SET priority = ? WHERE id = ?', (priority, event_id))

        conn.commit()
        conn.close()

        return cursor.rowcount > 0

    def delete_event(self, event_id):
        """Eliminar un evento"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('DELETE FROM events WHERE id = ?', (event_id,))

        conn.commit()
        conn.close()

        return cursor.rowcount > 0

    def get_event(self, event_id):
        """Obtener un evento por su ID"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
        SELECT e.*, c.name as category_name, c.color as category_color
        FROM events e
        LEFT JOIN categories c ON e.category_id = c.id
        WHERE e.id = ?
        ''', (event_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return dict(row)
        return None

    def get_events_by_date(self, date):
        """Obtener eventos para una fecha específica"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
        SELECT e.*, c.name as category_name, c.color as category_color
        FROM events e
        LEFT JOIN categories c ON e.category_id = c.id
        WHERE e.date = ?
        ORDER BY e.priority DESC, e.time_start
        ''', (date,))

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]

    def get_events_with_reminders(self):
        """Obtener eventos con recordatorios pendientes"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
        SELECT e.*, c.name as category_name, c.color as category_color
        FROM events e
        LEFT JOIN categories c ON e.category_id = c.id
        WHERE e.reminder IS NOT NULL AND e.reminder_sent = 0
        ORDER BY e.date, e.time_start
        ''')

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]

    def mark_reminder_sent(self, event_id):
        """Marcar un recordatorio como enviado"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('UPDATE events SET reminder_sent = 1 WHERE id = ?', (event_id,))

        conn.commit()
        conn.close()

    def search_events(self, query):
        """Buscar eventos por título o descripción"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        search_query = f"%{query}%"

        cursor.execute('''
        SELECT e.*, c.name as category_name, c.color as category_color
        FROM events e
        LEFT JOIN categories c ON e.category_id = c.id
        WHERE e.title LIKE ? OR e.description LIKE ?
        ORDER BY e.date, e.time_start
        ''', (search_query, search_query))

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]

    # ----- Métodos para notas -----

    def add_note(self, note_data):
        """Añadir una nueva nota"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        cursor.execute('''
        INSERT INTO notes (title, content, created_date, modified_date, category_id)
        VALUES (?, ?, ?, ?, ?)
        ''', (
            note_data['title'],
            note_data.get('content', ''),
            now,
            now,
            note_data.get('category_id')
        ))

        note_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return note_id

    def update_note(self, note_id, note_data):
        """Actualizar una nota existente"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        cursor.execute('''
        UPDATE notes
        SET title = ?, content = ?, modified_date = ?, category_id = ?
        WHERE id = ?
        ''', (
            note_data['title'],
            note_data.get('content', ''),
            now,
            note_data.get('category_id'),
            note_id
        ))

        conn.commit()
        conn.close()

        return cursor.rowcount > 0

    def delete_note(self, note_id):
        """Eliminar una nota"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('DELETE FROM notes WHERE id = ?', (note_id,))

        conn.commit()
        conn.close()

        return cursor.rowcount > 0

    def get_note(self, note_id):
        """Obtener una nota por su ID"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
        SELECT n.*, c.name as category_name, c.color as category_color
        FROM notes n
        LEFT JOIN categories c ON n.category_id = c.id
        WHERE n.id = ?
        ''', (note_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return dict(row)
        return None

    def get_all_notes(self):
        """Obtener todas las notas"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
        SELECT n.*, c.name as category_name, c.color as category_color
        FROM notes n
        LEFT JOIN categories c ON n.category_id = c.id
        ORDER BY n.modified_date DESC
        ''')

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]

    def search_notes(self, query):
        """Buscar notas por título o contenido"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        search_query = f"%{query}%"

        cursor.execute('''
        SELECT n.*, c.name as category_name, c.color as category_color
        FROM notes n
        LEFT JOIN categories c ON n.category_id = c.id
        WHERE n.title LIKE ? OR n.content LIKE ?
        ORDER BY n.modified_date DESC
        ''', (search_query, search_query))

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]

    # ----- Métodos para categorías -----

    def add_category(self, name, color='#1E90FF'):
        """Añadir una nueva categoría"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('INSERT INTO categories (name, color) VALUES (?, ?)', (name, color))

        category_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return category_id

    def update_category(self, category_id, name, color=None):
        """Actualizar una categoría existente"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        if color:
            cursor.execute('UPDATE categories SET name = ?, color = ? WHERE id = ?',
                         (name, color, category_id))
        else:
            cursor.execute('UPDATE categories SET name = ? WHERE id = ?',
                         (name, category_id))

        conn.commit()
        conn.close()

        return cursor.rowcount > 0

    def delete_category(self, category_id):
        """Eliminar una categoría"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Primero actualizar los eventos y notas que usan esta categoría
        cursor.execute('UPDATE events SET category_id = NULL WHERE category_id = ?',
                     (category_id,))
        cursor.execute('UPDATE notes SET category_id = NULL WHERE category_id = ?',
                     (category_id,))

        # Luego eliminar la categoría
        cursor.execute('DELETE FROM categories WHERE id = ?', (category_id,))

        conn.commit()
        conn.close()

        return cursor.rowcount > 0

    def get_category(self, category_id):
        """Obtener una categoría por su ID"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM categories WHERE id = ?', (category_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return dict(row)
        return None

    def get_all_categories(self):
        """Obtener todas las categorías"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM categories ORDER BY name')

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]

    # ----- Métodos para exportación/importación -----

    def export_data(self, file_path):
        """Exportar todos los datos a un archivo zip"""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                # Crear una copia de la base de datos
                db_copy = os.path.join(temp_dir, 'calendar_data.db')
                shutil.copy2(self.db_path, db_copy)

                # Crear una copia del archivo de configuración
                config_copy = os.path.join(temp_dir, 'config.json')
                with open(self.config_path, 'r') as src, open(config_copy, 'w') as dst:
                    dst.write(src.read())

                # Crear el archivo zip
                with zipfile.ZipFile(file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    zipf.write(db_copy, 'calendar_data.db')
                    zipf.write(config_copy, 'config.json')

            return True
        except Exception as e:
            print(f"Error al exportar datos: {e}")
            return False

    def import_data(self, file_path):
        """Importar datos desde un archivo zip"""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extraer el archivo zip
                with zipfile.ZipFile(file_path, 'r') as zipf:
                    zipf.extractall(temp_dir)

                # Verificar que los archivos existan
                db_path = os.path.join(temp_dir, 'calendar_data.db')
                config_path = os.path.join(temp_dir, 'config.json')

                if not os.path.exists(db_path) or not os.path.exists(config_path):
                    return False

                # Cerrar cualquier conexión existente
                conn = sqlite3.connect(self.db_path)
                conn.close()

                # Reemplazar los archivos
                shutil.copy2(db_path, self.db_path)
                shutil.copy2(config_path, self.config_path)

                # Recargar la configuración
                self.config = self.load_config()

                return True
        except Exception as e:
            print(f"Error al importar datos: {e}")
            return False

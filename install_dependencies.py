#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para instalar las dependencias necesarias para el sistema de encriptación
"""

import subprocess
import sys
import os


def install_package(package_name):
    """Instalar un paquete usando pip"""
    try:
        print(f"📦 Instalando {package_name}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package_name], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package_name} instalado correctamente")
            return True
        else:
            print(f"❌ Error instalando {package_name}: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error instalando {package_name}: {str(e)}")
        return False


def check_package(package_name):
    """Verificar si un paquete está instalado"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False


def main():
    """Función principal"""
    print("🔐 Instalador de Dependencias para Sistema de Encriptación")
    print("=" * 60)
    
    # Lista de dependencias necesarias
    dependencies = [
        ("cryptography", "cryptography"),  # (nombre_import, nombre_pip)
        ("PIL", "Pillow"),
    ]
    
    # Verificar dependencias existentes
    print("\n📋 Verificando dependencias existentes...")
    missing_packages = []
    
    for import_name, pip_name in dependencies:
        if check_package(import_name):
            print(f"✅ {pip_name} ya está instalado")
        else:
            print(f"❌ {pip_name} no está instalado")
            missing_packages.append(pip_name)
    
    # Instalar paquetes faltantes
    if missing_packages:
        print(f"\n🔧 Instalando {len(missing_packages)} paquetes faltantes...")
        
        for package in missing_packages:
            success = install_package(package)
            if not success:
                print(f"\n⚠️ No se pudo instalar {package}")
                print("💡 Intente instalar manualmente con:")
                print(f"   pip install {package}")
    else:
        print("\n🎉 Todas las dependencias ya están instaladas!")
    
    # Verificación final
    print("\n🔍 Verificación final...")
    all_installed = True
    
    for import_name, pip_name in dependencies:
        if check_package(import_name):
            print(f"✅ {pip_name} - OK")
        else:
            print(f"❌ {pip_name} - FALTA")
            all_installed = False
    
    if all_installed:
        print("\n🎉 ¡Todas las dependencias están instaladas correctamente!")
        print("\n🚀 Ahora puede usar todas las funciones de encriptación:")
        print("   • Encriptación de archivos del programa")
        print("   • Almacenamiento protegido de imágenes")
        print("   • Sistema de contraseña maestra")
        print("   • Protección automática de carpetas")
    else:
        print("\n⚠️ Algunas dependencias no se pudieron instalar.")
        print("💡 El programa funcionará con funcionalidad limitada.")
    
    print(f"\n📁 Directorio actual: {os.getcwd()}")
    print("🔧 Para probar las mejoras, ejecute:")
    print("   python test_banner_improvements.py")


if __name__ == "__main__":
    main()

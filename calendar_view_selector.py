#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Selector de vistas del calendario: DÍA, SEMANA, MES, AÑO
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFrame, QButtonGroup)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont


class CalendarViewSelector(QFrame):
    """Widget selector de vistas del calendario"""
    
    # Señales
    view_changed = pyqtSignal(str)  # Emite: 'day', 'week', 'month', 'year'
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_view = 'month'  # Vista por defecto
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurar la interfaz del selector"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setLineWidth(1)
        self.setStyleSheet("""
            CalendarViewSelector {
                background-color: #F8F9FA;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        
        # Título
        title_label = QLabel("📅 VISTA")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 11, QFont.Bold))
        title_label.setStyleSheet("color: #2C3E50; margin-bottom: 5px;")
        layout.addWidget(title_label)
        
        # Grupo de botones para selección exclusiva
        self.button_group = QButtonGroup(self)
        
        # Botón DÍA
        self.day_button = QPushButton("📆\nDÍA")
        self.day_button.setCheckable(True)
        self.day_button.setMinimumHeight(60)
        self.day_button.clicked.connect(lambda: self.set_view('day'))
        self.button_group.addButton(self.day_button)
        layout.addWidget(self.day_button)
        
        # Botón SEMANA
        self.week_button = QPushButton("📅\nSEMANA")
        self.week_button.setCheckable(True)
        self.week_button.setMinimumHeight(60)
        self.week_button.clicked.connect(lambda: self.set_view('week'))
        self.button_group.addButton(self.week_button)
        layout.addWidget(self.week_button)
        
        # Botón MES
        self.month_button = QPushButton("🗓️\nMES")
        self.month_button.setCheckable(True)
        self.month_button.setMinimumHeight(60)
        self.month_button.clicked.connect(lambda: self.set_view('month'))
        self.button_group.addButton(self.month_button)
        layout.addWidget(self.month_button)
        
        # Botón AÑO
        self.year_button = QPushButton("📊\nAÑO")
        self.year_button.setCheckable(True)
        self.year_button.setMinimumHeight(60)
        self.year_button.clicked.connect(lambda: self.set_view('year'))
        self.button_group.addButton(self.year_button)
        layout.addWidget(self.year_button)
        
        # Espaciador
        layout.addStretch()
        
        # Aplicar estilos a los botones
        self.apply_button_styles()
        
        # Seleccionar vista por defecto
        self.month_button.setChecked(True)
    
    def apply_button_styles(self):
        """Aplicar estilos a los botones"""
        button_style = """
            QPushButton {
                background-color: #FFFFFF;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
                color: #2C3E50;
                font-weight: bold;
                font-size: 10pt;
                text-align: center;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #E8F4FD;
                border: 2px solid #3498DB;
            }
            QPushButton:checked {
                background-color: #3498DB;
                border: 2px solid #2980B9;
                color: white;
            }
            QPushButton:pressed {
                background-color: #2980B9;
            }
        """
        
        self.day_button.setStyleSheet(button_style)
        self.week_button.setStyleSheet(button_style)
        self.month_button.setStyleSheet(button_style)
        self.year_button.setStyleSheet(button_style)
    
    def set_view(self, view_type):
        """Establecer la vista del calendario"""
        if view_type != self.current_view:
            self.current_view = view_type
            
            # Actualizar estado de los botones
            self.day_button.setChecked(view_type == 'day')
            self.week_button.setChecked(view_type == 'week')
            self.month_button.setChecked(view_type == 'month')
            self.year_button.setChecked(view_type == 'year')
            
            # Emitir señal de cambio
            self.view_changed.emit(view_type)
    
    def get_current_view(self):
        """Obtener la vista actual"""
        return self.current_view

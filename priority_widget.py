#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Widget para mostrar y cambiar la prioridad de eventos
"""

from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel, 
                            QPushButton, QMenu, QAction)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont


class PriorityWidget(QWidget):
    """Widget para mostrar y cambiar la prioridad de un evento"""
    
    priority_changed = pyqtSignal(int, int)  # event_id, new_priority
    
    def __init__(self, event_id, priority=1, parent=None):
        super().__init__(parent)
        self.event_id = event_id
        self.priority = priority
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurar la interfaz del widget"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(5)
        
        # Botón de prioridad con color
        self.priority_btn = QPushButton()
        self.priority_btn.setFixedSize(20, 20)
        self.priority_btn.clicked.connect(self.show_priority_menu)
        self.update_priority_display()
        
        layout.addWidget(self.priority_btn)
        layout.addStretch()
    
    def update_priority_display(self):
        """Actualizar la visualización de la prioridad"""
        priority_styles = {
            1: {"color": "#27AE60", "text": "🟢", "tooltip": "Normal"},
            2: {"color": "#F39C12", "text": "🟡", "tooltip": "Poco Importante"},
            3: {"color": "#E67E22", "text": "🟠", "tooltip": "Importante"},
            4: {"color": "#E74C3C", "text": "🔴", "tooltip": "Muy Importante"}
        }
        
        style_info = priority_styles.get(self.priority, priority_styles[1])
        
        self.priority_btn.setText(style_info["text"])
        self.priority_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {style_info["color"]};
                border: 1px solid #BDC3C7;
                border-radius: 10px;
                font-size: 12px;
            }}
            QPushButton:hover {{
                border: 2px solid #34495E;
            }}
        """)
        self.priority_btn.setToolTip(f"Prioridad: {style_info['tooltip']}\nHaz clic para cambiar")
    
    def show_priority_menu(self):
        """Mostrar menú para cambiar la prioridad"""
        menu = QMenu(self)
        
        priorities = [
            (1, "🟢 Normal", "#27AE60"),
            (2, "🟡 Poco Importante", "#F39C12"),
            (3, "🟠 Importante", "#E67E22"),
            (4, "🔴 Muy Importante", "#E74C3C")
        ]
        
        for priority_value, text, color in priorities:
            action = QAction(text, self)
            action.setData(priority_value)
            
            # Marcar la prioridad actual
            if priority_value == self.priority:
                action.setCheckable(True)
                action.setChecked(True)
            
            action.triggered.connect(lambda checked, p=priority_value: self.change_priority(p))
            menu.addAction(action)
        
        # Mostrar el menú en la posición del botón
        menu.exec_(self.priority_btn.mapToGlobal(self.priority_btn.rect().bottomLeft()))
    
    def change_priority(self, new_priority):
        """Cambiar la prioridad del evento"""
        if new_priority != self.priority:
            old_priority = self.priority
            self.priority = new_priority
            self.update_priority_display()
            
            # Emitir señal de cambio
            self.priority_changed.emit(self.event_id, new_priority)
            
            print(f"Prioridad del evento {self.event_id} cambiada de {old_priority} a {new_priority}")
    
    def set_priority(self, priority):
        """Establecer la prioridad sin emitir señal"""
        self.priority = priority
        self.update_priority_display()


class EventListItemWidget(QWidget):
    """Widget personalizado para items de eventos con prioridad"""
    
    priority_changed = pyqtSignal(int, int)  # event_id, new_priority
    event_clicked = pyqtSignal(int)  # event_id
    
    def __init__(self, event_data, parent=None):
        super().__init__(parent)
        self.event_data = event_data
        self.event_id = event_data['id']
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurar la interfaz del widget"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(8)
        
        # Widget de prioridad
        self.priority_widget = PriorityWidget(self.event_id, self.event_data.get('priority', 1))
        self.priority_widget.priority_changed.connect(self.priority_changed.emit)
        layout.addWidget(self.priority_widget)
        
        # Información del evento
        event_info_layout = QVBoxLayout()
        event_info_layout.setSpacing(2)
        
        # Título y hora
        time_str = ""
        if self.event_data.get('all_day', 0):
            time_str = "Todo el día"
        else:
            start_time = self.event_data.get('time_start', '')
            end_time = self.event_data.get('time_end', '')
            if start_time and end_time:
                time_str = f"{start_time} - {end_time}"
        
        title_text = f"{time_str}: {self.event_data['title']}"
        self.title_label = QLabel(title_text)
        self.title_label.setFont(QFont("Arial", 10, QFont.Bold))
        
        # Aplicar color de categoría si existe
        if self.event_data.get('category_color'):
            self.title_label.setStyleSheet(f"color: {self.event_data['category_color']};")
        
        event_info_layout.addWidget(self.title_label)
        
        # Descripción (si existe y no está vacía)
        description = self.event_data.get('description', '').strip()
        if description:
            desc_preview = description[:50] + "..." if len(description) > 50 else description
            self.desc_label = QLabel(desc_preview)
            self.desc_label.setFont(QFont("Arial", 8))
            self.desc_label.setStyleSheet("color: #7F8C8D; font-style: italic;")
            event_info_layout.addWidget(self.desc_label)
        
        layout.addLayout(event_info_layout)
        layout.addStretch()
        
        # Hacer el widget clickeable
        self.setStyleSheet("""
            EventListItemWidget:hover {
                background-color: #ECF0F1;
                border-radius: 3px;
            }
        """)
    
    def mousePressEvent(self, event):
        """Manejar clic en el widget"""
        if event.button() == Qt.LeftButton:
            self.event_clicked.emit(self.event_id)
        super().mousePressEvent(event)
    
    def update_event_data(self, event_data):
        """Actualizar los datos del evento"""
        self.event_data = event_data
        self.priority_widget.set_priority(event_data.get('priority', 1))
        
        # Actualizar título
        time_str = ""
        if event_data.get('all_day', 0):
            time_str = "Todo el día"
        else:
            start_time = event_data.get('time_start', '')
            end_time = event_data.get('time_end', '')
            if start_time and end_time:
                time_str = f"{start_time} - {end_time}"
        
        title_text = f"{time_str}: {event_data['title']}"
        self.title_label.setText(title_text)
        
        # Actualizar color de categoría
        if event_data.get('category_color'):
            self.title_label.setStyleSheet(f"color: {event_data['category_color']};")
        else:
            self.title_label.setStyleSheet("")

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para crear imágenes de prueba para banners (600x200)
"""

import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QPixmap, QPainter, QColor, QFont, QLinearGradient, QBrush
from PyQt5.QtCore import QRect, Qt


def create_banner_image(width, height, title, subtitle, gradient_colors, filename):
    """Crear imagen de banner con gradiente y texto"""
    
    # Crear pixmap
    pixmap = QPixmap(width, height)
    
    # Crear painter
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.Antialiasing)
    
    # Crear gradiente
    gradient = QLinearGradient(0, 0, width, 0)
    gradient.setColorAt(0, QColor(gradient_colors[0]))
    gradient.setColorAt(0.5, QColor(gradient_colors[1]))
    gradient.setColorAt(1, QColor(gradient_colors[2]))
    
    # Llenar fondo con gradiente
    painter.fillRect(0, 0, width, height, QBrush(gradient))
    
    # Configurar texto
    painter.setPen(QColor("white"))
    
    # Título principal
    title_font = QFont("Arial", 24, QFont.Bold)
    painter.setFont(title_font)
    title_rect = QRect(0, height//4, width, height//3)
    painter.drawText(title_rect, Qt.AlignCenter, title)
    
    # Subtítulo
    subtitle_font = QFont("Arial", 14)
    painter.setFont(subtitle_font)
    subtitle_rect = QRect(0, height//2 + 20, width, height//3)
    painter.drawText(subtitle_rect, Qt.AlignCenter, subtitle)
    
    # Finalizar
    painter.end()
    
    # Guardar imagen
    success = pixmap.save(filename, "PNG")
    return success


def main():
    """Crear imágenes de prueba para banners"""
    
    # Crear aplicación Qt (necesaria para QPixmap)
    app = QApplication([])
    
    print("🎨 Creando imágenes de prueba para banners (600x200)...")
    print("=" * 50)
    
    # Definir banners de prueba
    banners = [
        {
            "title": "🎯 BANNER PROMOCIONAL",
            "subtitle": "Haz clic para visitar nuestro sitio web",
            "colors": ["#3498DB", "#9B59B6", "#E74C3C"],
            "filename": "banner_promocional_600x200.png"
        },
        {
            "title": "🚀 NUEVAS FUNCIONES",
            "subtitle": "Descubre las últimas actualizaciones",
            "colors": ["#1ABC9C", "#16A085", "#27AE60"],
            "filename": "banner_funciones_600x200.png"
        },
        {
            "title": "💡 CONSEJOS Y TRUCOS",
            "subtitle": "Aprende a usar mejor la aplicación",
            "colors": ["#F39C12", "#E67E22", "#D35400"],
            "filename": "banner_consejos_600x200.png"
        },
        {
            "title": "📢 ANUNCIO IMPORTANTE",
            "subtitle": "Información relevante para usuarios",
            "colors": ["#E74C3C", "#C0392B", "#A93226"],
            "filename": "banner_anuncio_600x200.png"
        },
        {
            "title": "🎉 CELEBRACIÓN",
            "subtitle": "¡Únete a nuestra celebración especial!",
            "colors": ["#9B59B6", "#8E44AD", "#7D3C98"],
            "filename": "banner_celebracion_600x200.png"
        }
    ]
    
    # Crear cada banner
    created_count = 0
    for banner in banners:
        try:
            success = create_banner_image(
                600, 200,  # Tamaño fijo 600x200
                banner["title"],
                banner["subtitle"],
                banner["colors"],
                banner["filename"]
            )
            
            if success:
                print(f"✅ {banner['filename']} - {banner['title']}")
                created_count += 1
            else:
                print(f"❌ Error creando {banner['filename']}")
                
        except Exception as e:
            print(f"❌ Error creando {banner['filename']}: {str(e)}")
    
    print(f"\n🎉 Proceso completado: {created_count}/{len(banners)} imágenes creadas")
    
    if created_count > 0:
        print("\n💡 INSTRUCCIONES DE USO:")
        print("1. Ejecute: python test_banner_carousel.py")
        print("2. Haga clic en el botón 🎠 para configurar")
        print("3. Agregue banners con ➕ Nuevo")
        print("4. Seleccione las imágenes creadas")
        print("5. Configure URLs y duración")
        print("6. Observe el comportamiento:")
        print("   • 1-3 banners: Fijos y centrados")
        print("   • 4+ banners: Carrusel rotativo")
        
        print(f"\n📁 Archivos creados en: {os.getcwd()}")
        for banner in banners:
            if os.path.exists(banner["filename"]):
                print(f"   📄 {banner['filename']}")
    
    # No ejecutar app.exec_() ya que solo creamos imágenes
    print("\n✅ Script completado exitosamente!")


if __name__ == "__main__":
    main()

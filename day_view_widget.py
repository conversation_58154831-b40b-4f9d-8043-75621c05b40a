#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Vista diaria del calendario con intervalos de tiempo
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QScrollArea, QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, QDate, QTime
from PyQt5.QtGui import QFont, QPainter, QColor, QPen


class TimeSlotWidget(QFrame):
    """Widget para un intervalo de tiempo específico"""
    
    def __init__(self, time_str, events=None, parent=None):
        super().__init__(parent)
        self.time_str = time_str
        self.events = events or []
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurar la interfaz del intervalo"""
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(1)
        self.setMinimumHeight(40)
        self.setStyleSheet("""
            TimeSlotWidget {
                background-color: #FFFFFF;
                border: 1px solid #E0E0E0;
                margin: 0px;
            }
            TimeSlotWidget:hover {
                background-color: #F0F8FF;
                border: 1px solid #3498DB;
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(10)
        
        # Hora
        time_label = QLabel(self.time_str)
        time_label.setFont(QFont("Arial", 9, QFont.Bold))
        time_label.setStyleSheet("color: #7F8C8D; min-width: 50px;")
        time_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        layout.addWidget(time_label)
        
        # Eventos
        events_container = QWidget()
        events_layout = QVBoxLayout(events_container)
        events_layout.setContentsMargins(0, 0, 0, 0)
        events_layout.setSpacing(2)
        
        if self.events:
            for event in self.events:
                event_label = QLabel(f"• {event.get('title', 'Sin título')}")
                event_label.setFont(QFont("Arial", 9))
                event_label.setStyleSheet(f"""
                    color: {event.get('category_color', '#2C3E50')};
                    background-color: {event.get('category_color', '#E8F4FD')}20;
                    padding: 2px 5px;
                    border-radius: 3px;
                    border-left: 3px solid {event.get('category_color', '#3498DB')};
                """)
                events_layout.addWidget(event_label)
        else:
            # Espacio vacío
            events_layout.addStretch()
        
        layout.addWidget(events_container, 1)


class DayViewWidget(QWidget):
    """Vista diaria del calendario"""
    
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.current_date = QDate.currentDate()
        self.time_interval = 30  # Intervalos de 30 minutos
        
        self.setup_ui()
        self.load_day_events()
    
    def setup_ui(self):
        """Configurar la interfaz de la vista diaria"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Cabecera con la fecha
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #3498DB;
                color: white;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 10, 10, 10)
        
        self.date_label = QLabel()
        self.date_label.setAlignment(Qt.AlignCenter)
        self.date_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.date_label.setStyleSheet("color: white;")
        header_layout.addWidget(self.date_label)
        
        layout.addWidget(header_frame)
        
        # Área de scroll para los intervalos de tiempo
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Widget contenedor de intervalos
        self.time_slots_container = QWidget()
        self.time_slots_layout = QVBoxLayout(self.time_slots_container)
        self.time_slots_layout.setContentsMargins(5, 5, 5, 5)
        self.time_slots_layout.setSpacing(0)
        
        scroll_area.setWidget(self.time_slots_container)
        layout.addWidget(scroll_area)
        
        # Crear intervalos de tiempo
        self.create_time_slots()
        
        # Actualizar fecha
        self.update_date_label()
    
    def create_time_slots(self):
        """Crear intervalos de tiempo para el día"""
        # Limpiar intervalos existentes
        while self.time_slots_layout.count():
            child = self.time_slots_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        # Crear intervalos desde 00:00 hasta 23:30
        current_time = QTime(0, 0)
        end_time = QTime(23, 59)
        
        while current_time <= end_time:
            time_str = current_time.toString("hh:mm")
            
            # Obtener eventos para este intervalo
            events_in_slot = self.get_events_for_time_slot(current_time)
            
            # Crear widget del intervalo
            time_slot = TimeSlotWidget(time_str, events_in_slot)
            self.time_slots_layout.addWidget(time_slot)
            
            # Avanzar al siguiente intervalo
            current_time = current_time.addSecs(self.time_interval * 60)
    
    def get_events_for_time_slot(self, slot_time):
        """Obtener eventos que ocurren en un intervalo de tiempo específico"""
        date_str = self.current_date.toString("yyyy-MM-dd")
        all_events = self.data_manager.get_events_by_date(date_str)
        
        events_in_slot = []
        
        for event in all_events:
            if event.get('all_day', 0):
                # Eventos de todo el día aparecen en el primer slot (00:00)
                if slot_time == QTime(0, 0):
                    events_in_slot.append(event)
            else:
                # Verificar si el evento ocurre en este intervalo
                start_time_str = event.get('time_start', '')
                end_time_str = event.get('time_end', '')
                
                if start_time_str and end_time_str:
                    try:
                        start_time = QTime.fromString(start_time_str, "hh:mm:ss")
                        end_time = QTime.fromString(end_time_str, "hh:mm:ss")
                        
                        # Calcular el final del slot actual
                        slot_end = slot_time.addSecs(self.time_interval * 60)
                        
                        # Verificar si hay superposición
                        if (start_time <= slot_end and end_time >= slot_time):
                            events_in_slot.append(event)
                    except:
                        pass
        
        return events_in_slot
    
    def set_date(self, date):
        """Establecer la fecha a mostrar"""
        self.current_date = date
        self.update_date_label()
        self.load_day_events()
    
    def update_date_label(self):
        """Actualizar la etiqueta de fecha"""
        date_text = self.current_date.toString("dddd, d 'de' MMMM 'de' yyyy")
        self.date_label.setText(date_text)
    
    def load_day_events(self):
        """Cargar eventos del día y actualizar intervalos"""
        self.create_time_slots()
    
    def set_time_interval(self, minutes):
        """Establecer el intervalo de tiempo en minutos"""
        self.time_interval = minutes
        self.create_time_slots()

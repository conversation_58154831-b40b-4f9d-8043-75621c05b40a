#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para crear imágenes de muestra para el banner
"""

import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QPixmap, QPainter, QFont, QColor, QBrush, QPen
from PyQt5.QtCore import Qt, QRect


def create_sample_banner_qt(filename="sample_banner_qt.png", text="Banner de Prueba", width=300, height=60):
    """Crear imagen de banner usando Qt"""
    
    # Crear pixmap
    pixmap = QPixmap(width, height)
    pixmap.fill(QColor("#3498DB"))  # Fondo azul
    
    # Crear painter
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.Antialiasing)
    
    # Configurar fuente
    font = QFont("Arial", 14, QFont.Bold)
    painter.setFont(font)
    
    # Configurar color del texto
    painter.setPen(QPen(QColor("white")))
    
    # Dibujar texto centrado
    rect = QRect(0, 0, width, height)
    painter.drawText(rect, Qt.AlignCenter, text)
    
    # Agregar borde
    painter.setPen(QPen(QColor("#2980B9"), 2))
    painter.drawRect(1, 1, width-2, height-2)
    
    painter.end()
    
    # Guardar imagen
    success = pixmap.save(filename, "PNG")
    return success, filename


def create_multiple_samples():
    """Crear múltiples imágenes de muestra"""
    samples = [
        ("banner_azul.png", "Haz Clic para Visitar", "#3498DB", "#2980B9"),
        ("banner_verde.png", "Sitio Web Oficial", "#27AE60", "#229954"),
        ("banner_naranja.png", "Más Información", "#E67E22", "#D35400"),
        ("banner_morado.png", "Portal Principal", "#8E44AD", "#7D3C98"),
        ("banner_rojo.png", "Enlace Importante", "#E74C3C", "#C0392B")
    ]
    
    created_files = []
    
    for filename, text, bg_color, border_color in samples:
        # Crear pixmap
        pixmap = QPixmap(300, 60)
        pixmap.fill(QColor(bg_color))
        
        # Crear painter
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Configurar fuente
        font = QFont("Arial", 12, QFont.Bold)
        painter.setFont(font)
        
        # Configurar color del texto
        painter.setPen(QPen(QColor("white")))
        
        # Dibujar texto centrado
        rect = QRect(0, 0, 300, 60)
        painter.drawText(rect, Qt.AlignCenter, text)
        
        # Agregar borde
        painter.setPen(QPen(QColor(border_color), 2))
        painter.drawRect(1, 1, 298, 58)
        
        # Agregar icono decorativo
        painter.setPen(QPen(QColor("white")))
        painter.setFont(QFont("Arial", 16))
        painter.drawText(QRect(10, 0, 30, 60), Qt.AlignCenter, "🔗")
        
        painter.end()
        
        # Guardar imagen
        if pixmap.save(filename, "PNG"):
            created_files.append(filename)
    
    return created_files


def create_gradient_banner(filename="banner_gradient.png"):
    """Crear banner con gradiente"""
    pixmap = QPixmap(300, 60)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.Antialiasing)
    
    # Crear gradiente
    from PyQt5.QtGui import QLinearGradient
    gradient = QLinearGradient(0, 0, 300, 0)
    gradient.setColorAt(0, QColor("#3498DB"))
    gradient.setColorAt(1, QColor("#9B59B6"))
    
    # Llenar con gradiente
    painter.fillRect(0, 0, 300, 60, QBrush(gradient))
    
    # Agregar texto
    painter.setPen(QPen(QColor("white")))
    painter.setFont(QFont("Arial", 14, QFont.Bold))
    painter.drawText(QRect(0, 0, 300, 60), Qt.AlignCenter, "Banner con Gradiente")
    
    # Agregar sombra al texto
    painter.setPen(QPen(QColor("#2C3E50")))
    painter.drawText(QRect(1, 1, 300, 60), Qt.AlignCenter, "Banner con Gradiente")
    
    painter.end()
    
    success = pixmap.save(filename, "PNG")
    return success, filename


if __name__ == "__main__":
    app = QApplication([])
    
    print("🎨 Creando imágenes de muestra para el banner...")
    
    # Crear banner básico
    success, filename = create_sample_banner_qt()
    if success:
        print(f"✅ Creado: {filename}")
    else:
        print(f"❌ Error creando: {filename}")
    
    # Crear múltiples muestras
    created_files = create_multiple_samples()
    print(f"✅ Creados {len(created_files)} banners temáticos:")
    for file in created_files:
        print(f"   📄 {file}")
    
    # Crear banner con gradiente
    success, filename = create_gradient_banner()
    if success:
        print(f"✅ Creado: {filename}")
    else:
        print(f"❌ Error creando: {filename}")
    
    print("\n🔧 INSTRUCCIONES DE USO:")
    print("1. Ejecuta: python test_banner_widget.py")
    print("2. Haz clic en ⚙ en el banner")
    print("3. Ingresa contraseña: admin123")
    print("4. Selecciona una de las imágenes creadas")
    print("5. Agrega una URL (ej: https://www.google.com)")
    print("6. Guarda y prueba haciendo clic en el banner")
    
    print(f"\n📁 Archivos creados en: {os.getcwd()}")
    
    app.quit()

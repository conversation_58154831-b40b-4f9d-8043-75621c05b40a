# 🌍 Relojes Mundiales - CYPHER Calendar

## Descripción
Se ha agregado una nueva funcionalidad de **Relojes Mundiales** al calendario CYPHER que permite:

- Mostrar múltiples relojes con diferentes zonas horarias
- Alternar entre vista digital y analógica
- Configurar alarmas personalizadas con sonido
- Seleccionar entre las principales ciudades del mundo

## 📍 Ubicación
Los relojes mundiales se encuentran en la **parte inferior derecha** de la pestaña del calendario, debajo de la lista de eventos.

### 📏 Dimensiones Optimizadas: Relojes Fijos + Contenedor Flexible
- **Altura inicial**: Ocupa aproximadamente **3 cuadrículas del calendario** de altura
- **Redimensionamiento del contenedor**: Barra separadora completamente responsive
- **Colapso completo**: Permite colapsar cualquier sección hasta 0 píxeles
- **Relojes individuales**: <PERSON>año FIJO (220x180 a 280x250 píxeles) para consistencia visual
- **Contenedor de relojes**: REDIMENSIONABLE (mínimo 250x200) que se adapta al espacio
- **Scroll automático**: Horizontal cuando hay muchos relojes
- **Fuente de hora**: 22pt para excelente visibilidad
- **Persistencia**: Las proporciones se guardan automáticamente sin restricciones

## ⚙️ Funcionalidades

### 🕐 Tipo de Reloj
- **Solo Digital**: Muestra hora en formato HH:MM:SS y fecha con fuente grande (22pt)
- **Interfaz simplificada**: Sin opciones de cambio de tipo para mayor claridad
- **Optimizado**: Diseño enfocado en la mejor experiencia digital

### 🌎 Zonas Horarias Disponibles
Por defecto se incluyen:
- **Madrid** (Europe/Madrid)
- **São Paulo** (America/Sao_Paulo)

Zonas horarias adicionales disponibles:
- Tokio (Asia/Tokyo)
- Londres (Europe/London)
- Sydney (Australia/Sydney)
- Nueva York (America/New_York)
- Los Ángeles (America/Los_Angeles)
- Singapur (Asia/Singapore)
- París (Europe/Paris)
- Berlín (Europe/Berlin)
- Moscú (Europe/Moscow)
- Dubai (Asia/Dubai)
- Hong Kong (Asia/Hong_Kong)
- Mumbai (Asia/Kolkata)
- Ciudad de México (America/Mexico_City)
- Buenos Aires (America/Argentina/Buenos_Aires)
- Cairo (Africa/Cairo)
- Johannesburgo (Africa/Johannesburg)

### 🔔 Sistema de Alarmas

#### Configuración de Alarmas
- **Hora específica**: Configurar hora exacta para la alarma
- **Repetición**: Opción de repetir diariamente
- **Días de la semana**: Seleccionar días específicos para repetir
- **Sonidos disponibles**:
  - Sonido por defecto
  - Campana
  - Beep
  - Chime
- **Volumen**: Ajustable del 1% al 100%
- **Duración**: De 5 a 300 segundos
- **Mensaje personalizado**: Texto opcional para mostrar

#### Notificación de Alarma Mejorada
- **Sonido simultáneo**: Inicia automáticamente con la ventana popup
- **Ventana emergente prominente**: Siempre visible encima de otras ventanas
- **Opciones de posposición**: 1, 5, 10, 15, 30 minutos (configurable)
- **Control de sonido**: Se detiene al posponer o finalizar
- **Posposición inteligente**: Programa automáticamente nueva alarma
- **Auto-cierre**: Después de la duración configurada si no hay interacción

## 🎨 Interfaz

### Controles por Reloj
- **Etiqueta de ciudad**: Muestra el nombre de la ciudad
- **Botón de cierre (✕)**: Eliminar este reloj (esquina superior derecha)
- **Selector de zona horaria**: Dropdown para cambiar la ciudad
- **Botón de alarma (🔔)**: Configurar nuevas alarmas

### Gestión de Relojes
- **Agregar reloj**: Botón "+ Agregar Reloj" (máximo 6 relojes)
- **Eliminar reloj**: Botón ✕ rojo en cada reloj con confirmación (mínimo 2 relojes)
- **Redimensionar contenedor**: Arrastrar barra separadora para ajustar altura del contenedor de relojes vs eventos

## 📐 Comportamiento de Redimensionamiento

### 🔒 Relojes Individuales (Tamaño Fijo)
- **Dimensiones fijas**: 220x180 píxeles (mínimo) a 280x250 píxeles (máximo)
- **Consistencia visual**: Todos los relojes mantienen el mismo tamaño
- **No se redimensionan**: Al cambiar el tamaño del contenedor

### 📏 Contenedor de Relojes (Flexible)
- **Redimensionamiento libre**: Sin límites artificiales mediante barra separadora
- **Tamaño mínimo**: 250x200 píxeles (para mostrar al menos un reloj)
- **Scroll automático**: Aparece horizontalmente cuando hay muchos relojes
- **Colapso completo**: Puede reducirse hasta 0 píxeles si se desea

### 🎯 Ventajas de este Diseño
- **Consistencia**: Los relojes siempre se ven igual, independientemente del espacio
- **Flexibilidad**: El usuario controla cuánto espacio dedicar a los relojes
- **Escalabilidad**: Funciona bien con 2 o 6 relojes
- **Usabilidad**: Scroll automático evita que los relojes se compriman

## 🔔 Lógica de Alarmas Corregida

### 🎵 Sincronización Sonido-Visual
- **Inicio simultáneo**: El sonido comienza exactamente cuando aparece la ventana popup
- **Control integrado**: El diálogo controla directamente el sonido
- **Detención inmediata**: El sonido se detiene al interactuar con la alarma

### ⏰ Sistema de Posposición Inteligente
- **Opciones flexibles**: 1, 5, 10, 15, 30 minutos
- **Programación automática**: Crea nueva alarma temporal para el tiempo seleccionado
- **No repetitiva**: Las alarmas pospuestas son de una sola vez
- **Persistencia**: Se guardan en la configuración hasta que se activen

### 🔇 Control de Sonido Avanzado
- **Gestión de hilos**: Control completo de hilos de sonido activos
- **Detención garantizada**: Flag de parada para terminar sonidos inmediatamente
- **Limpieza automática**: Los hilos se limpian al detener sonidos
- **Multiplataforma**: Funciona en Windows, macOS y Linux

### 🎯 Flujo de Alarma Completo
1. **Activación**: Alarma se activa a la hora programada
2. **Sonido + Popup**: Inician simultáneamente
3. **Interacción del usuario**:
   - **Posponer**: Detiene sonido, programa nueva alarma
   - **Detener**: Detiene sonido, elimina alarma (si no es repetitiva)
   - **Auto-cierre**: Detiene sonido después de la duración configurada
4. **Limpieza**: Recursos de sonido se liberan automáticamente

## 📅 Diálogo de Eventos Mejorado

### 🖼️ Mejoras Visuales
- **Ventana amplia**: 700x600 píxeles (ocupa espacio de zona de eventos)
- **Redimensionamiento libre**: Usuario puede ajustar por esquinas y laterales
- **Organización por secciones**: Información básica, fecha/hora, categoría, descripción
- **Scroll automático**: Si el contenido excede el tamaño de ventana
- **Campos espaciosos**: 35px mínimo de altura, fuentes mejoradas

### 🏷️ Sistema de Categorías Avanzado
- **8 categorías por defecto**: Importante, Negocios, Familia, Médico, Personal, Trabajo, Educación, Viajes
- **Colores distintivos**: Cada categoría tiene su color único predefinido
- **Botón "+ Nueva"**: Permite agregar categorías personalizadas
- **Validación inteligente**: Evita duplicados y nombres vacíos
- **Selector de color**: Para categorías personalizadas

### 📝 Área de Descripción Mejorada
- **Altura mínima**: 120px para texto extenso
- **Fuente optimizada**: Arial 11pt para mejor legibilidad
- **Placeholder descriptivo**: Guía al usuario sobre qué escribir
- **Redimensionamiento**: Se adapta al tamaño de la ventana

### 🎯 Beneficios
- **No interfiere**: Con visualización de calendario y relojes
- **Experiencia mejorada**: Interfaz más profesional y organizada
- **Gestión completa**: Categorías predefinidas y personalizables
- **Flexibilidad**: Redimensionable según preferencias del usuario

## ⚡ Sistema de Prioridades de Eventos

### 🎯 Niveles de Prioridad
- **🔴 Muy Importante** (Prioridad 4): Eventos críticos y urgentes
- **🟠 Importante** (Prioridad 3): Eventos importantes pero no críticos
- **🟡 Poco Importante** (Prioridad 2): Eventos de baja prioridad
- **🟢 Normal** (Prioridad 1): Eventos regulares (por defecto)

### 📊 Ordenamiento Inteligente
- **Criterio principal**: Prioridad (4 → 3 → 2 → 1)
- **Criterio secundario**: Hora de inicio (dentro de la misma prioridad)
- **Actualización automática**: Los eventos se reordenan al cambiar prioridad

### 🎨 Interfaz Mejorada
- **Indicadores visuales**: Emojis de colores en la lista de eventos
- **Botón de categoría**: Redimensionado para mostrar texto completo
- **Edición por clic**: Los eventos se editan al hacer clic en ellos
- **Sección de prioridad**: Radio buttons con colores distintivos en el diálogo

### 🗄️ Persistencia
- **Campo en BD**: `priority INTEGER DEFAULT 1` agregado a tabla `events`
- **Migración automática**: Compatible con bases de datos existentes
- **Método específico**: `update_event_priority()` para cambios rápidos

## 📅 Calendario Mejorado con Indicadores Visuales

### 🎨 Días de la Semana Mejorados
- **Texto más grande**: 14pt en negrita para mejor legibilidad
- **Contraste mejorado**: Fondo oscuro (#34495E) con texto blanco
- **Encabezados prominentes**: Padding aumentado (8px) para mayor presencia visual

### 🔲 Cuadrados de Prioridad en Días
- **Ubicación**: Tercio inferior de cada casilla del día
- **Formato**: Cuadrados perfectos (ancho = alto)
- **Espaciado**: 2px entre cuadrados para separación clara
- **Centrado**: Alineación horizontal automática
- **Tamaño adaptativo**: 8-20px según espacio disponible

### 🔢 Numeración Siempre Visible
- **Número "1"**: Para un solo evento (no se omite)
- **Número "2"**: Para dos eventos de la misma prioridad
- **Número "3+"**: Para múltiples eventos consecutivos
- **Fuente proporcional**: 6-10pt según tamaño del cuadrado
- **Color**: Blanco con borde para máximo contraste

### 🎯 Colores por Prioridad
- **🔴 Muy Importante**: Rojo (#E74C3C) - Aparece primero
- **🟠 Importante**: Naranja (#E67E22) - Segunda posición
- **🟡 Poco Importante**: Amarillo (#F39C12) - Tercera posición
- **🟢 Normal**: Verde (#27AE60) - Última posición

### 🔄 Actualización Automática
- **Sincronización**: Los indicadores se actualizan al crear/editar/eliminar eventos
- **Cache inteligente**: Solo recalcula cuando cambia el mes
- **Rendimiento**: Optimizado para calendarios con muchos eventos

## 📢 Banner Configurable con Protección por Contraseña

### 🎯 Ubicación y Dimensiones
- **Posición**: Parte inferior de los relojes mundiales
- **Altura**: 60px (igual a una cuadrícula del calendario)
- **Ancho**: Adaptable al contenedor de relojes
- **Integración**: Seamless con el diseño existente

### 🔐 Sistema de Protección
- **Contraseña**: `admin123` (configurable en el código)
- **Acceso restringido**: Solo usuarios autorizados pueden modificar
- **Validación**: Verificación antes de mostrar diálogo de configuración
- **Seguridad**: Previene modificaciones no autorizadas

### 🎨 Configuración de Contenido
- **Imagen personalizable**: Soporte para PNG, JPG, JPEG, GIF, BMP
- **Escalado automático**: Imagen se ajusta al tamaño del banner
- **Texto alternativo**: Mostrado cuando no hay imagen
- **URL externa**: Enlace que se abre en el navegador
- **Estado**: Habilitado/deshabilitado según necesidad

### 🖱️ Interacción del Usuario
- **Botón configuración**: ⚙ en esquina superior derecha
- **Clic en banner**: Abre URL en navegador predeterminado
- **Hover effect**: Indicación visual de interactividad
- **Cursor pointer**: Indica que el banner es clickeable

### 🔧 Diálogo de Configuración
- **Vista previa**: Actualización en tiempo real
- **Explorador de archivos**: Selección fácil de imágenes
- **Validación**: Verificación de archivos de imagen válidos
- **Formulario completo**: Todos los campos organizados

### 💾 Persistencia de Datos
- **Configuración guardada**: En base de datos SQLite
- **Campos almacenados**: enabled, image_path, url, alt_text
- **Carga automática**: Al iniciar la aplicación
- **Sincronización**: Cambios reflejados inmediatamente

## 🔧 Archivos Nuevos Creados

1. **`clock_widget.py`**: Widget individual para cada reloj
2. **`world_clocks_widget.py`**: Contenedor principal de relojes
3. **`alarm_dialog.py`**: Diálogos para configurar y mostrar alarmas
4. **`sound_manager.py`**: Gestor de sonidos multiplataforma
5. **`README_EVENT_DIALOG.md`**: Documentación completa del diálogo de eventos mejorado
6. **`test_clocks.py`**: Script de prueba independiente
7. **`test_clock_switch.py`**: Prueba específica para cambio digital/analógico (obsoleto)
8. **`test_adaptive_splitter.py`**: Prueba del splitter adaptativo
9. **`test_clock_removal.py`**: Prueba de eliminación de relojes con botón X
10. **`test_fully_responsive_splitter.py`**: Prueba de splitter sin límites
11. **`test_fixed_clocks_flexible_container.py`**: Prueba de relojes fijos + contenedor flexible
12. **`test_alarm_logic.py`**: Prueba de lógica corregida de alarmas
13. **`test_improved_event_dialog.py`**: Prueba del diálogo de eventos mejorado
14. **`test_event_improvements.py`**: Prueba del sistema de prioridades y mejoras de eventos
15. **`test_calendar_improvements.py`**: Prueba de mejoras del calendario con indicadores
16. **`test_priority_squares.py`**: Prueba específica de cuadrados de prioridad
17. **`test_event_checkboxes.py`**: Prueba de checkboxes para marcar eventos completados
18. **`test_checkbox_size.py`**: Prueba específica de tamaño y alineación de checkboxes
19. **`test_banner_widget.py`**: Prueba del banner configurable con protección
20. **`priority_widget.py`**: Widget para gestión de prioridades de eventos
21. **`custom_calendar.py`**: Calendario personalizado con indicadores de prioridad
22. **`event_item_widget.py`**: Widget personalizado para eventos con checkboxes
23. **`banner_widget.py`**: Widget de banner configurable con protección por contraseña
24. **`create_sample_banner.py`**: Script para crear imágenes de muestra para el banner

## 🔄 Archivos Modificados

1. **`calendar_tab.py`**: Integración de relojes en el layout + edición de eventos por clic
2. **`event_dialog.py`**: Diálogo de eventos completamente rediseñado + sistema de prioridades
3. **`data_manager.py`**: Configuración persistente de relojes + campo de prioridad + ordenamiento
4. **`styles.py`**: Estilos CSS para los relojes
5. **`main.py`**: Aplicación de estilos

## 💾 Persistencia de Datos
- La configuración de relojes se guarda automáticamente
- Las alarmas se mantienen entre sesiones
- Exportación/importación incluye configuración de relojes

## 🎵 Compatibilidad de Sonidos
- **Windows**: Usa `winsound` para generar tonos
- **macOS**: Utiliza sonidos del sistema
- **Linux**: Intenta usar `speaker-test` o `beep`
- **Fallback**: Carácter de bell del sistema

## 🚀 Uso Rápido

1. **Ver relojes**: Los relojes aparecen automáticamente en la parte inferior derecha
2. **Cambiar zona horaria**: Usar el dropdown en cada reloj
3. **Configurar alarma**: Hacer clic en "🔔 Alarma"
4. **Eliminar reloj**: Hacer clic en "✕" rojo (confirmar eliminación)
5. **Agregar más relojes**: Hacer clic en "+ Agregar Reloj"
6. **Ajustar tamaño del contenedor**: Arrastrar la barra separadora horizontal entre eventos y relojes

## 🔍 Pruebas
Ejecutar los scripts de prueba:

**Prueba general de relojes:**
```bash
python test_clocks.py
```

**Prueba específica de cambio digital/analógico:**
```bash
python test_clock_switch.py
```

**Prueba del splitter adaptativo:**
```bash
python test_adaptive_splitter.py
```

**Prueba de eliminación de relojes:**
```bash
python test_clock_removal.py
```

**Prueba de splitter completamente responsive:**
```bash
python test_fully_responsive_splitter.py
```

**Prueba de lógica corregida de alarmas:**
```bash
python test_alarm_logic.py
```

**Prueba del diálogo de eventos mejorado:**
```bash
python test_improved_event_dialog.py
```

**Prueba del sistema de prioridades y mejoras de eventos:**
```bash
python test_event_improvements.py
```

**Prueba de mejoras del calendario con indicadores:**
```bash
python test_calendar_improvements.py
```

**Prueba específica de cuadrados de prioridad:**
```bash
python test_priority_squares.py
```

**Prueba de checkboxes para eventos completados:**
```bash
python test_event_checkboxes.py
```

**Prueba de tamaño y alineación de checkboxes:**
```bash
python test_checkbox_size.py
```

**Prueba del banner configurable:**
```bash
python test_banner_widget.py
```

**Crear imágenes de muestra para el banner:**
```bash
python create_sample_banner.py
```

## 📝 Notas Técnicas
- Los relojes se actualizan cada segundo
- Las alarmas se verifican en cada actualización
- La configuración se guarda en `config.json`
- Los estilos están integrados en el sistema de temas existente

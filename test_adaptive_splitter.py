#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar la funcionalidad del splitter adaptativo
entre la zona de eventos y la zona de relojes mundiales
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QTextEdit, QSplitter)
from PyQt5.QtCore import Qt

from world_clocks_widget import WorldClocksWidget
from data_manager import DataManager
from styles import apply_styles


class TestAdaptiveSplitterWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Splitter Adaptativo - Eventos/Relojes")
        self.setGeometry(100, 100, 800, 600)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Splitter Adaptativo")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "Arrastra la barra separadora horizontal entre las dos secciones para ajustar el tamaño.\n"
            "La configuración se guarda automáticamente y se restaura al reiniciar."
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #666; font-size: 10pt;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Crear splitter vertical (como en el calendario real)
        splitter = QSplitter(Qt.Vertical)
        splitter.setHandleWidth(8)
        splitter.setChildrenCollapsible(False)
        
        # Sección superior: Simulación de eventos
        events_section = QWidget()
        events_section.setMinimumHeight(150)
        events_layout = QVBoxLayout(events_section)
        
        events_title = QLabel("📅 Sección de Eventos")
        events_title.setStyleSheet("font-size: 14pt; font-weight: bold; color: #2C3E50; padding: 10px;")
        events_layout.addWidget(events_title)
        
        events_content = QTextEdit()
        events_content.setPlainText(
            "Esta es la sección de eventos del día.\n\n"
            "Aquí se mostrarían:\n"
            "• Lista de eventos del día seleccionado\n"
            "• Botones para agregar, editar y eliminar eventos\n"
            "• Información detallada de cada evento\n\n"
            "Puedes redimensionar esta sección arrastrando la barra separadora hacia arriba o abajo."
        )
        events_content.setMaximumHeight(200)
        events_layout.addWidget(events_content)
        
        # Sección inferior: Relojes mundiales
        self.world_clocks = WorldClocksWidget(self.data_manager)
        self.world_clocks.setMinimumHeight(200)
        
        # Agregar secciones al splitter
        splitter.addWidget(events_section)
        splitter.addWidget(self.world_clocks)
        
        # Configurar comportamiento del splitter
        splitter.setStretchFactor(0, 1)  # Eventos pueden estirarse
        splitter.setStretchFactor(1, 1)  # Relojes también pueden estirarse
        
        # Tamaños iniciales
        splitter.setSizes([250, 300])
        
        # Conectar señal para guardar estado
        splitter.splitterMoved.connect(lambda: self.save_splitter_state(splitter))
        
        # Guardar referencia y cargar estado
        self.test_splitter = splitter
        self.load_splitter_state()
        
        layout.addWidget(splitter)
        
        # Información adicional
        info = QLabel(
            "💡 Tip: La barra separadora tiene 8 píxeles de grosor y cambia de color al pasar el mouse.\n"
            "Las proporciones se guardan automáticamente en la configuración."
        )
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("margin: 10px; color: #7F8C8D; font-size: 9pt;")
        info.setWordWrap(True)
        layout.addWidget(info)
    
    def save_splitter_state(self, splitter):
        """Guardar el estado del splitter de prueba"""
        sizes = splitter.sizes()
        self.data_manager.set_config('test_splitter_sizes', sizes)
        print(f"Estado del splitter guardado: {sizes}")
    
    def load_splitter_state(self):
        """Cargar el estado guardado del splitter de prueba"""
        saved_sizes = self.data_manager.get_config('test_splitter_sizes', None)
        if saved_sizes and len(saved_sizes) == 2:
            if all(size > 50 for size in saved_sizes):
                self.test_splitter.setSizes(saved_sizes)
                print(f"Estado del splitter restaurado: {saved_sizes}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestAdaptiveSplitterWindow()
    window.show()
    
    sys.exit(app.exec_())

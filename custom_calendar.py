#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Calendario personalizado que muestra indicadores de prioridad de eventos
"""

from PyQt5.QtWidgets import QCalendarWidget, QWidget
from PyQt5.QtCore import Qt, QDate, QRect
from PyQt5.QtGui import QPainter, QColor, QFont, QPen, QBrush
from datetime import datetime


class CustomCalendarWidget(QCalendarWidget):
    """Calendario personalizado con indicadores de eventos por prioridad"""

    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.events_cache = {}  # Cache de eventos por fecha

        # Configurar el calendario
        self.setGridVisible(True)
        self.setVerticalHeaderFormat(QCalendarWidget.NoVerticalHeader)
        self.setMinimumWidth(400)

        # Actualizar cache de eventos
        self.update_events_cache()

    def update_events_cache(self):
        """Actualizar el cache de eventos para el mes actual"""
        current_date = self.selectedDate()
        year = current_date.year()
        month = current_date.month()

        # Limpiar cache anterior
        self.events_cache.clear()

        # Obtener eventos para todo el mes
        start_date = QDate(year, month, 1)
        end_date = QDate(year, month, start_date.daysInMonth())

        # Iterar por cada día del mes
        current = start_date
        while current <= end_date:
            date_str = current.toString("yyyy-MM-dd")
            events = self.data_manager.get_events_by_date(date_str)

            if events:
                # Agrupar eventos por prioridad
                priorities = {}
                for event in events:
                    priority = event.get('priority', 1)
                    if priority not in priorities:
                        priorities[priority] = 0
                    priorities[priority] += 1

                self.events_cache[date_str] = priorities

            current = current.addDays(1)

    def paintCell(self, painter, rect, date):
        """Pintar celda personalizada con indicadores de eventos"""
        # Llamar al método padre para pintar la celda base
        super().paintCell(painter, rect, date)

        # Obtener eventos para esta fecha
        date_str = date.toString("yyyy-MM-dd")
        priorities = self.events_cache.get(date_str, {})

        if not priorities:
            return

        # Configurar el painter
        painter.setRenderHint(QPainter.Antialiasing)

        # Definir colores de prioridad
        priority_colors = {
            4: QColor("#E74C3C"),  # Muy Importante - Rojo
            3: QColor("#E67E22"),  # Importante - Naranja
            2: QColor("#F39C12"),  # Poco Importante - Amarillo
            1: QColor("#27AE60")   # Normal - Verde
        }

        # Calcular el área del tercio inferior de la casilla
        cell_height = rect.height()
        cell_width = rect.width()
        third_height = cell_height // 3

        # Área disponible para indicadores (tercio inferior con márgenes)
        margin = 3
        indicators_area = QRect(
            rect.left() + margin,  # Margen izquierdo
            rect.bottom() - third_height + margin,  # Comenzar en el tercio inferior
            cell_width - (2 * margin),  # Ancho menos márgenes
            third_height - (2 * margin)  # Alto del tercio menos márgenes
        )

        # Calcular tamaño de cada indicador (cuadrados perfectos)
        num_priorities = len(priorities)
        if num_priorities > 0:
            # Calcular el tamaño del cuadrado basado en el espacio disponible
            available_height = indicators_area.height()
            spacing = 2  # Espacio entre cuadrados

            # El tamaño del cuadrado será el menor entre altura disponible y ancho por prioridad
            max_square_size = available_height
            available_width_per_square = (indicators_area.width() - (num_priorities - 1) * spacing) // num_priorities
            square_size = min(max_square_size, available_width_per_square, 20)  # Máximo 20px
            square_size = max(square_size, 8)  # Mínimo 8px

            # Calcular posición inicial para centrar los cuadrados
            total_width_needed = (num_priorities * square_size) + ((num_priorities - 1) * spacing)
            start_x = indicators_area.left() + (indicators_area.width() - total_width_needed) // 2
            start_y = indicators_area.top() + (indicators_area.height() - square_size) // 2

            # Dibujar indicadores de prioridad (ordenados de mayor a menor prioridad)
            x_offset = 0
            for priority in sorted(priorities.keys(), reverse=True):
                count = priorities[priority]
                color = priority_colors.get(priority, QColor("#95A5A6"))

                # Posición del cuadrado
                square_rect = QRect(
                    start_x + x_offset,
                    start_y,
                    square_size,
                    square_size
                )

                # Dibujar el cuadrado de prioridad
                painter.setBrush(QBrush(color))
                painter.setPen(QPen(QColor("#2C3E50"), 1))
                painter.drawRect(square_rect)

                # Siempre mostrar el número de eventos (incluso si es 1)
                painter.setPen(QPen(QColor("white")))
                # Ajustar tamaño de fuente según el tamaño del cuadrado
                font_size = max(6, min(10, square_size // 2))
                painter.setFont(QFont("Arial", font_size, QFont.Bold))
                painter.drawText(square_rect, Qt.AlignCenter, str(count))

                x_offset += square_size + spacing  # Siguiente posición

    def refresh_events(self):
        """Refrescar los eventos y repintar el calendario"""
        self.update_events_cache()
        self.update()  # Forzar repintado

    def setSelectedDate(self, date):
        """Override para actualizar cache cuando cambia el mes"""
        old_date = self.selectedDate()
        super().setSelectedDate(date)

        # Si cambió el mes, actualizar cache
        if (old_date.year() != date.year() or
            old_date.month() != date.month()):
            self.update_events_cache()

    def showNextMonth(self):
        """Override para actualizar cache al cambiar mes"""
        super().showNextMonth()
        self.update_events_cache()

    def showPreviousMonth(self):
        """Override para actualizar cache al cambiar mes"""
        super().showPreviousMonth()
        self.update_events_cache()

    def showNextYear(self):
        """Override para actualizar cache al cambiar año"""
        super().showNextYear()
        self.update_events_cache()

    def showPreviousYear(self):
        """Override para actualizar cache al cambiar año"""
        super().showPreviousYear()
        self.update_events_cache()


class CalendarDayWidget(QWidget):
    """Widget personalizado para mostrar un día del calendario con eventos"""

    def __init__(self, date, events=None, parent=None):
        super().__init__(parent)
        self.date = date
        self.events = events or []
        self.setMinimumSize(40, 40)

    def paintEvent(self, event):
        """Pintar el widget del día"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Fondo
        rect = self.rect()
        painter.fillRect(rect, QColor("#FFFFFF"))

        # Número del día
        painter.setPen(QPen(QColor("#2C3E50")))
        painter.setFont(QFont("Arial", 12, QFont.Bold))
        painter.drawText(rect.adjusted(2, 2, -2, -20), Qt.AlignTop | Qt.AlignLeft, str(self.date.day()))

        # Indicadores de eventos
        if self.events:
            self.draw_event_indicators(painter, rect)

    def draw_event_indicators(self, painter, rect):
        """Dibujar indicadores de eventos"""
        # Agrupar eventos por prioridad
        priorities = {}
        for event in self.events:
            priority = event.get('priority', 1)
            if priority not in priorities:
                priorities[priority] = 0
            priorities[priority] += 1

        # Colores de prioridad
        priority_colors = {
            4: QColor("#E74C3C"),  # Muy Importante - Rojo
            3: QColor("#E67E22"),  # Importante - Naranja
            2: QColor("#F39C12"),  # Poco Importante - Amarillo
            1: QColor("#27AE60")   # Normal - Verde
        }

        # Calcular área del tercio inferior para indicadores
        third_height = rect.height() // 3
        margin = 2
        indicators_area = QRect(
            rect.left() + margin,
            rect.bottom() - third_height + margin,
            rect.width() - (2 * margin),
            third_height - (2 * margin)
        )

        # Calcular tamaño de cuadrados
        num_priorities = len(priorities)
        spacing = 1
        available_height = indicators_area.height()
        available_width_per_square = (indicators_area.width() - (num_priorities - 1) * spacing) // num_priorities
        square_size = min(available_height, available_width_per_square, 12)  # Máximo 12px para widget pequeño
        square_size = max(square_size, 4)  # Mínimo 4px

        # Centrar cuadrados
        total_width_needed = (num_priorities * square_size) + ((num_priorities - 1) * spacing)
        start_x = indicators_area.left() + (indicators_area.width() - total_width_needed) // 2
        start_y = indicators_area.top() + (indicators_area.height() - square_size) // 2

        x_offset = 0
        for priority in sorted(priorities.keys(), reverse=True):
            count = priorities[priority]
            color = priority_colors.get(priority, QColor("#95A5A6"))

            # Dibujar cuadrado perfecto
            square_rect = QRect(
                start_x + x_offset,
                start_y,
                square_size,
                square_size
            )

            painter.setBrush(QBrush(color))
            painter.setPen(QPen(QColor("#2C3E50"), 1))
            painter.drawRect(square_rect)

            # Siempre mostrar número de eventos
            painter.setPen(QPen(QColor("white")))
            font_size = max(3, square_size // 2)
            painter.setFont(QFont("Arial", font_size, QFont.Bold))
            painter.drawText(square_rect, Qt.AlignCenter, str(count))

            x_offset += square_size + spacing

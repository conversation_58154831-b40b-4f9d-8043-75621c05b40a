#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Calendario personalizado que muestra indicadores de prioridad de eventos
"""

from PyQt5.QtWidgets import QCalendarWidget, QWidget
from PyQt5.QtCore import Qt, QDate, QRect
from PyQt5.QtGui import QPainter, QColor, QFont, QPen, QBrush
from datetime import datetime


class CustomCalendarWidget(QCalendarWidget):
    """Calendario personalizado con indicadores de eventos por prioridad"""
    
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.events_cache = {}  # Cache de eventos por fecha
        
        # Configurar el calendario
        self.setGridVisible(True)
        self.setVerticalHeaderFormat(QCalendarWidget.NoVerticalHeader)
        self.setMinimumWidth(400)
        
        # Actualizar cache de eventos
        self.update_events_cache()
    
    def update_events_cache(self):
        """Actualizar el cache de eventos para el mes actual"""
        current_date = self.selectedDate()
        year = current_date.year()
        month = current_date.month()
        
        # Limpiar cache anterior
        self.events_cache.clear()
        
        # Obtener eventos para todo el mes
        start_date = QDate(year, month, 1)
        end_date = QDate(year, month, start_date.daysInMonth())
        
        # Iterar por cada día del mes
        current = start_date
        while current <= end_date:
            date_str = current.toString("yyyy-MM-dd")
            events = self.data_manager.get_events_by_date(date_str)
            
            if events:
                # Agrupar eventos por prioridad
                priorities = {}
                for event in events:
                    priority = event.get('priority', 1)
                    if priority not in priorities:
                        priorities[priority] = 0
                    priorities[priority] += 1
                
                self.events_cache[date_str] = priorities
            
            current = current.addDays(1)
    
    def paintCell(self, painter, rect, date):
        """Pintar celda personalizada con indicadores de eventos"""
        # Llamar al método padre para pintar la celda base
        super().paintCell(painter, rect, date)
        
        # Obtener eventos para esta fecha
        date_str = date.toString("yyyy-MM-dd")
        priorities = self.events_cache.get(date_str, {})
        
        if not priorities:
            return
        
        # Configurar el painter
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Definir colores de prioridad
        priority_colors = {
            4: QColor("#E74C3C"),  # Muy Importante - Rojo
            3: QColor("#E67E22"),  # Importante - Naranja
            2: QColor("#F39C12"),  # Poco Importante - Amarillo
            1: QColor("#27AE60")   # Normal - Verde
        }
        
        # Calcular posición para los indicadores
        indicator_size = 8
        margin = 2
        start_x = rect.right() - (len(priorities) * (indicator_size + margin)) - margin
        start_y = rect.bottom() - indicator_size - margin
        
        # Dibujar indicadores de prioridad (ordenados de mayor a menor prioridad)
        x_offset = 0
        for priority in sorted(priorities.keys(), reverse=True):
            count = priorities[priority]
            color = priority_colors.get(priority, QColor("#95A5A6"))
            
            # Posición del indicador
            indicator_rect = QRect(
                start_x + x_offset,
                start_y,
                indicator_size,
                indicator_size
            )
            
            # Dibujar el cuadrado de prioridad
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(QColor("#2C3E50"), 1))
            painter.drawRect(indicator_rect)
            
            # Si hay más de un evento de esta prioridad, agregar un número
            if count > 1:
                painter.setPen(QPen(QColor("white")))
                painter.setFont(QFont("Arial", 6, QFont.Bold))
                painter.drawText(indicator_rect, Qt.AlignCenter, str(count))
            
            x_offset += indicator_size + margin
    
    def refresh_events(self):
        """Refrescar los eventos y repintar el calendario"""
        self.update_events_cache()
        self.update()  # Forzar repintado
    
    def setSelectedDate(self, date):
        """Override para actualizar cache cuando cambia el mes"""
        old_date = self.selectedDate()
        super().setSelectedDate(date)
        
        # Si cambió el mes, actualizar cache
        if (old_date.year() != date.year() or 
            old_date.month() != date.month()):
            self.update_events_cache()
    
    def showNextMonth(self):
        """Override para actualizar cache al cambiar mes"""
        super().showNextMonth()
        self.update_events_cache()
    
    def showPreviousMonth(self):
        """Override para actualizar cache al cambiar mes"""
        super().showPreviousMonth()
        self.update_events_cache()
    
    def showNextYear(self):
        """Override para actualizar cache al cambiar año"""
        super().showNextYear()
        self.update_events_cache()
    
    def showPreviousYear(self):
        """Override para actualizar cache al cambiar año"""
        super().showPreviousYear()
        self.update_events_cache()


class CalendarDayWidget(QWidget):
    """Widget personalizado para mostrar un día del calendario con eventos"""
    
    def __init__(self, date, events=None, parent=None):
        super().__init__(parent)
        self.date = date
        self.events = events or []
        self.setMinimumSize(40, 40)
    
    def paintEvent(self, event):
        """Pintar el widget del día"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Fondo
        rect = self.rect()
        painter.fillRect(rect, QColor("#FFFFFF"))
        
        # Número del día
        painter.setPen(QPen(QColor("#2C3E50")))
        painter.setFont(QFont("Arial", 12, QFont.Bold))
        painter.drawText(rect.adjusted(2, 2, -2, -20), Qt.AlignTop | Qt.AlignLeft, str(self.date.day()))
        
        # Indicadores de eventos
        if self.events:
            self.draw_event_indicators(painter, rect)
    
    def draw_event_indicators(self, painter, rect):
        """Dibujar indicadores de eventos"""
        # Agrupar eventos por prioridad
        priorities = {}
        for event in self.events:
            priority = event.get('priority', 1)
            if priority not in priorities:
                priorities[priority] = 0
            priorities[priority] += 1
        
        # Colores de prioridad
        priority_colors = {
            4: QColor("#E74C3C"),  # Muy Importante - Rojo
            3: QColor("#E67E22"),  # Importante - Naranja
            2: QColor("#F39C12"),  # Poco Importante - Amarillo
            1: QColor("#27AE60")   # Normal - Verde
        }
        
        # Dibujar indicadores
        indicator_size = 6
        margin = 1
        start_x = rect.left() + 2
        start_y = rect.bottom() - indicator_size - 2
        
        x_offset = 0
        for priority in sorted(priorities.keys(), reverse=True):
            count = priorities[priority]
            color = priority_colors.get(priority, QColor("#95A5A6"))
            
            # Dibujar cuadrado
            indicator_rect = QRect(
                start_x + x_offset,
                start_y,
                indicator_size,
                indicator_size
            )
            
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(QColor("#2C3E50"), 1))
            painter.drawRect(indicator_rect)
            
            x_offset += indicator_size + margin
            
            # Limitar número de indicadores para evitar overflow
            if x_offset > rect.width() - 10:
                break

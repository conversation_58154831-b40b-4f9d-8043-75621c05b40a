#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar el diálogo de eventos mejorado:
- Ventana más amplia y redimensionable
- Mejor organización visual
- Categorías por defecto y personalizables
- Área de descripción más espaciosa
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout)
from PyQt5.QtCore import Qt

from event_dialog import EventDialog
from data_manager import DataManager
from styles import apply_styles


class TestImprovedEventDialogWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba del Diálogo de Eventos Mejorado")
        self.setGeometry(100, 100, 800, 600)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba del Diálogo de Eventos Mejorado")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "🎯 MEJORAS IMPLEMENTADAS:\n"
            "✅ Ventana más amplia (700x600) que ocupa el espacio de la zona de eventos\n"
            "✅ Redimensionable libremente por el usuario (esquinas y laterales)\n"
            "✅ Mejor organización visual con secciones agrupadas\n"
            "✅ Área de descripción más espaciosa (120px mínimo)\n"
            "✅ 8 categorías por defecto: Importante, Negocios, Familia, Médico, etc.\n"
            "✅ Botón '+ Nueva' para agregar categorías personalizadas\n"
            "✅ Scroll automático si el contenido es muy largo\n"
            "✅ Campos más grandes y mejor espaciados\n\n"
            "🔧 PRUEBA: Haz clic en los botones para abrir el diálogo"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #27AE60; font-size: 10pt; background-color: #F0F8F0; padding: 15px; border-radius: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Botones de prueba
        buttons_layout = QHBoxLayout()
        
        btn_new_event = QPushButton("📅 Nuevo Evento")
        btn_new_event.setMinimumSize(150, 50)
        btn_new_event.setStyleSheet("font-size: 12pt; background-color: #3498DB; color: white; font-weight: bold;")
        btn_new_event.clicked.connect(self.test_new_event)
        buttons_layout.addWidget(btn_new_event)
        
        btn_edit_event = QPushButton("✏️ Editar Evento")
        btn_edit_event.setMinimumSize(150, 50)
        btn_edit_event.setStyleSheet("font-size: 12pt; background-color: #E67E22; color: white; font-weight: bold;")
        btn_edit_event.clicked.connect(self.test_edit_event)
        buttons_layout.addWidget(btn_edit_event)
        
        btn_show_categories = QPushButton("🏷️ Ver Categorías")
        btn_show_categories.setMinimumSize(150, 50)
        btn_show_categories.setStyleSheet("font-size: 12pt; background-color: #9B59B6; color: white; font-weight: bold;")
        btn_show_categories.clicked.connect(self.show_categories)
        buttons_layout.addWidget(btn_show_categories)
        
        layout.addLayout(buttons_layout)
        
        # Información de características
        features = QLabel(
            "📋 CARACTERÍSTICAS DESTACADAS:\n\n"
            "🖼️ DISEÑO VISUAL:\n"
            "• Secciones organizadas con iconos\n"
            "• Campos más grandes y espaciados\n"
            "• Scroll automático para contenido largo\n"
            "• Botones estilizados con colores\n\n"
            "🏷️ CATEGORÍAS:\n"
            "• 8 categorías por defecto con colores\n"
            "• Botón '+ Nueva' para categorías personalizadas\n"
            "• Validación de nombres duplicados\n"
            "• Selector de color para nuevas categorías\n\n"
            "📝 DESCRIPCIÓN:\n"
            "• Área más espaciosa (120px mínimo)\n"
            "• Fuente mejorada para mejor legibilidad\n"
            "• Placeholder descriptivo\n\n"
            "🔧 REDIMENSIONAMIENTO:\n"
            "• Ventana redimensionable por esquinas y laterales\n"
            "• Tamaño mínimo: 600x500\n"
            "• Tamaño inicial: 700x600"
        )
        features.setAlignment(Qt.AlignLeft)
        features.setStyleSheet("margin: 10px; color: #2C3E50; font-size: 9pt; background-color: #F8F9FA; padding: 15px; border-radius: 5px;")
        features.setWordWrap(True)
        layout.addWidget(features)
        
        layout.addStretch()
    
    def test_new_event(self):
        """Probar diálogo de nuevo evento"""
        dialog = EventDialog(self.data_manager, self)
        
        if dialog.exec_():
            event_data = dialog.get_event_data()
            print("Nuevo evento creado:")
            print(f"  Título: {event_data['title']}")
            print(f"  Fecha: {event_data['date']}")
            print(f"  Categoría ID: {event_data['category_id']}")
            print(f"  Descripción: {event_data['description'][:50]}..." if len(event_data['description']) > 50 else f"  Descripción: {event_data['description']}")
        else:
            print("Creación de evento cancelada")
    
    def test_edit_event(self):
        """Probar diálogo de edición con datos de ejemplo"""
        # Crear evento de ejemplo
        sample_event = {
            'id': 1,
            'title': 'Reunión de trabajo',
            'date': '2024-01-15',
            'time_start': '10:00:00',
            'time_end': '11:30:00',
            'all_day': 0,
            'category_id': None,
            'reminder': '15',
            'color': '#E74C3C',
            'description': 'Reunión semanal del equipo para revisar el progreso de los proyectos y planificar las tareas de la próxima semana.'
        }
        
        dialog = EventDialog(self.data_manager, self, event=sample_event)
        
        if dialog.exec_():
            event_data = dialog.get_event_data()
            print("Evento editado:")
            print(f"  Título: {event_data['title']}")
            print(f"  Fecha: {event_data['date']}")
            print(f"  Categoría ID: {event_data['category_id']}")
            print(f"  Descripción: {event_data['description'][:50]}..." if len(event_data['description']) > 50 else f"  Descripción: {event_data['description']}")
        else:
            print("Edición de evento cancelada")
    
    def show_categories(self):
        """Mostrar las categorías disponibles"""
        categories = self.data_manager.get_all_categories()
        
        print("\n=== CATEGORÍAS DISPONIBLES ===")
        if categories:
            for cat in categories:
                print(f"  • {cat['name']} (Color: {cat['color']})")
        else:
            print("  No hay categorías disponibles")
        print("===============================\n")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestImprovedEventDialogWindow()
    window.show()
    
    sys.exit(app.exec_())

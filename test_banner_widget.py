#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar la funcionalidad del banner configurable:
- Banner en la parte inferior de los relojes
- Altura igual a una cuadrícula del calendario
- Configuración protegida por contraseña
- Soporte para imagen y enlace externo
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QTextEdit, QSplitter)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from banner_widget import BannerWidget
from world_clocks_widget import WorldClocksWidget
from data_manager import DataManager
from styles import apply_styles


class TestBannerWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Banner Configurable")
        self.setGeometry(100, 100, 1200, 800)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Banner Configurable con Protección por Contraseña")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 10px; color: #2C3E50;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "🎯 FUNCIONALIDAD DEL BANNER:\n\n"
            "✅ UBICACIÓN: Parte inferior de los relojes mundiales\n"
            "✅ TAMAÑO: Altura igual a una cuadrícula del calendario (60px)\n"
            "✅ PROTECCIÓN: Contraseña requerida para configurar (admin123)\n"
            "✅ CONTENIDO: Imagen personalizable + enlace externo\n"
            "✅ INTERACCIÓN: Clic abre URL en navegador\n\n"
            "🔧 PRUEBA: Haz clic en ⚙ para configurar el banner (contraseña: admin123)"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #2980B9; font-size: 11pt; background-color: #EBF5FB; padding: 15px; border-radius: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Splitter para dividir la vista
        splitter = QSplitter(Qt.Horizontal)
        
        # Panel izquierdo: Relojes con banner
        left_panel = QWidget()
        left_panel.setMaximumWidth(600)
        left_layout = QVBoxLayout(left_panel)
        
        clocks_title = QLabel("🌍 Relojes Mundiales con Banner:")
        clocks_title.setFont(QFont("Arial", 14, QFont.Bold))
        clocks_title.setStyleSheet("color: #2C3E50; margin: 10px 0;")
        left_layout.addWidget(clocks_title)
        
        # Widget de relojes mundiales (incluye el banner)
        self.world_clocks = WorldClocksWidget(self.data_manager)
        left_layout.addWidget(self.world_clocks)
        
        splitter.addWidget(left_panel)
        
        # Panel derecho: Información y controles
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Información de configuración
        config_title = QLabel("🔧 Información de Configuración:")
        config_title.setFont(QFont("Arial", 12, QFont.Bold))
        config_title.setStyleSheet("color: #2C3E50; margin: 10px 0;")
        right_layout.addWidget(config_title)
        
        # Botones de prueba
        btn_test_config = QPushButton("🔑 Probar Configuración")
        btn_test_config.setMinimumHeight(40)
        btn_test_config.setStyleSheet("background-color: #3498DB; color: white; font-weight: bold;")
        btn_test_config.clicked.connect(self.test_configuration)
        right_layout.addWidget(btn_test_config)
        
        btn_reset_banner = QPushButton("🔄 Resetear Banner")
        btn_reset_banner.setMinimumHeight(35)
        btn_reset_banner.setStyleSheet("background-color: #E67E22; color: white; font-weight: bold;")
        btn_reset_banner.clicked.connect(self.reset_banner)
        right_layout.addWidget(btn_reset_banner)
        
        btn_create_sample = QPushButton("🖼️ Crear Imagen de Muestra")
        btn_create_sample.setMinimumHeight(35)
        btn_create_sample.setStyleSheet("background-color: #9B59B6; color: white; font-weight: bold;")
        btn_create_sample.clicked.connect(self.create_sample_image)
        right_layout.addWidget(btn_create_sample)
        
        right_layout.addWidget(QLabel(""))  # Espaciador
        
        # Información técnica
        tech_title = QLabel("📐 Especificaciones Técnicas:")
        tech_title.setFont(QFont("Arial", 11, QFont.Bold))
        tech_title.setStyleSheet("color: #2C3E50; margin: 10px 0;")
        right_layout.addWidget(tech_title)
        
        tech_info = QLabel(
            "🔐 SEGURIDAD:\n"
            "• Contraseña: admin123\n"
            "• Acceso protegido a configuración\n"
            "• Validación antes de cambios\n\n"
            "📏 DIMENSIONES:\n"
            "• Altura fija: 60px (cuadrícula calendario)\n"
            "• Ancho: Adaptable al contenedor\n"
            "• Imagen escalada automáticamente\n\n"
            "🎨 CONTENIDO:\n"
            "• Imagen personalizable (PNG, JPG, etc.)\n"
            "• Texto alternativo configurable\n"
            "• URL externa para navegador\n"
            "• Estado habilitado/deshabilitado\n\n"
            "⚡ FUNCIONALIDAD:\n"
            "• Clic abre URL en navegador\n"
            "• Hover visual para interacción\n"
            "• Configuración persistente\n"
            "• Vista previa en tiempo real\n\n"
            "🔧 CONFIGURACIÓN:\n"
            "• Botón ⚙ en esquina superior derecha\n"
            "• Diálogo modal con vista previa\n"
            "• Validación de archivos de imagen\n"
            "• Guardado automático en BD"
        )
        tech_info.setAlignment(Qt.AlignLeft)
        tech_info.setStyleSheet("color: #34495E; font-size: 9pt; background-color: #F8F9FA; padding: 10px; border-radius: 5px; border-left: 3px solid #3498DB;")
        tech_info.setWordWrap(True)
        right_layout.addWidget(tech_info)
        
        right_layout.addStretch()
        
        splitter.addWidget(right_panel)
        layout.addWidget(splitter)
        
        # Área de resultados
        self.results_area = QTextEdit()
        self.results_area.setMaximumHeight(120)
        self.results_area.setPlaceholderText("Los resultados de las pruebas aparecerán aquí...")
        self.results_area.setStyleSheet("background-color: #FAFAFA; border: 1px solid #BDC3C7; border-radius: 3px;")
        layout.addWidget(self.results_area)
        
        # Mostrar información inicial
        self.show_initial_info()
    
    def show_initial_info(self):
        """Mostrar información inicial del banner"""
        config = self.data_manager.get_config('banner_config', {})
        
        if config.get('enabled', False):
            self.results_area.append("✅ Banner habilitado")
            if config.get('image_path'):
                self.results_area.append(f"🖼️ Imagen: {os.path.basename(config['image_path'])}")
            if config.get('url'):
                self.results_area.append(f"🔗 URL: {config['url']}")
            if config.get('alt_text'):
                self.results_area.append(f"📝 Texto: {config['alt_text']}")
        else:
            self.results_area.append("⭕ Banner deshabilitado")
        
        self.results_area.append("🔑 Contraseña para configurar: admin123")
        self.results_area.append("⚙️ Haz clic en el botón ⚙ del banner para configurar")
    
    def test_configuration(self):
        """Probar la configuración del banner"""
        self.results_area.append("\n🔧 INSTRUCCIONES DE PRUEBA:")
        self.results_area.append("1. Haz clic en el botón ⚙ del banner")
        self.results_area.append("2. Ingresa la contraseña: admin123")
        self.results_area.append("3. Configura imagen, URL y texto")
        self.results_area.append("4. Observa la vista previa en tiempo real")
        self.results_area.append("5. Guarda y prueba el clic en el banner")
    
    def reset_banner(self):
        """Resetear configuración del banner"""
        # Resetear configuración
        self.data_manager.set_config('banner_config', {
            'enabled': False,
            'image_path': '',
            'url': '',
            'alt_text': ''
        })
        
        # Recargar banner
        self.world_clocks.banner.load_banner_config()
        
        self.results_area.append("🔄 Banner reseteado - configuración limpiada")
    
    def create_sample_image(self):
        """Crear una imagen de muestra para probar"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # Crear imagen de muestra
            width, height = 300, 60
            image = Image.new('RGB', (width, height), color='#3498DB')
            draw = ImageDraw.Draw(image)
            
            # Agregar texto
            try:
                # Intentar usar una fuente del sistema
                font = ImageFont.truetype("arial.ttf", 20)
            except:
                # Usar fuente por defecto si no encuentra arial
                font = ImageFont.load_default()
            
            text = "Banner de Prueba - Haz Clic Aquí"
            
            # Calcular posición centrada
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (width - text_width) // 2
            y = (height - text_height) // 2
            
            # Dibujar texto
            draw.text((x, y), text, fill='white', font=font)
            
            # Guardar imagen
            sample_path = "sample_banner.png"
            image.save(sample_path)
            
            self.results_area.append(f"🖼️ Imagen de muestra creada: {sample_path}")
            self.results_area.append("💡 Usa esta imagen para probar el banner")
            
        except ImportError:
            self.results_area.append("❌ PIL/Pillow no está instalado")
            self.results_area.append("💡 Instala con: pip install Pillow")
        except Exception as e:
            self.results_area.append(f"❌ Error creando imagen: {str(e)}")


class TestBannerStandaloneWindow(QMainWindow):
    """Ventana para probar solo el banner"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Banner Independiente")
        self.setGeometry(200, 200, 600, 200)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Banner Independiente")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 14pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Banner
        self.banner = BannerWidget(self.data_manager)
        layout.addWidget(self.banner)
        
        # Información
        info = QLabel("Contraseña: admin123 | Altura: 60px (cuadrícula calendario)")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("color: #7F8C8D; font-size: 10pt; margin: 10px;")
        layout.addWidget(info)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    # Mostrar ventana principal
    main_window = TestBannerWindow()
    main_window.show()
    
    # Mostrar ventana de banner independiente
    banner_window = TestBannerStandaloneWindow()
    banner_window.move(main_window.x() + main_window.width() + 20, main_window.y())
    banner_window.show()
    
    sys.exit(app.exec_())

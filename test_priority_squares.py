#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba específico para verificar el formato de cuadrados de prioridad:
- Cuadrados perfectos en el tercio inferior
- Espaciado entre cuadrados
- Números siempre visibles (incluso para 1 evento)
- Diferentes combinaciones de eventos
"""

import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QPushButton, QHBoxLayout, QTextEdit)
from PyQt5.QtCore import Qt

from custom_calendar import CustomCalendarWidget
from data_manager import DataManager
from styles import apply_styles


class TestPrioritySquaresWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba de Cuadrados de Prioridad")
        self.setGeometry(100, 100, 1200, 800)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Prueba de Cuadrados de Prioridad en Calendario")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "🎯 FORMATO DE CUADRADOS IMPLEMENTADO:\n\n"
            "✅ CUADRADOS PERFECTOS:\n"
            "• Formato cuadrado (ancho = alto)\n"
            "• Ubicados en el tercio inferior de cada día\n"
            "• Espaciado uniforme entre cuadrados\n"
            "• Centrados horizontalmente\n\n"
            "✅ NUMERACIÓN SIEMPRE VISIBLE:\n"
            "• Número '1' para un solo evento\n"
            "• Número '2' para dos eventos de la misma prioridad\n"
            "• Y así sucesivamente...\n\n"
            "✅ COLORES POR PRIORIDAD:\n"
            "• 🔴 Muy Importante | 🟠 Importante | 🟡 Poco Importante | 🟢 Normal\n\n"
            "🔧 PRUEBA: Usa los botones para crear diferentes combinaciones de eventos"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #2980B9; font-size: 10pt; background-color: #EBF5FB; padding: 15px; border-radius: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Layout horizontal para calendario y controles
        main_layout = QHBoxLayout()
        
        # Calendario personalizado
        self.calendar = CustomCalendarWidget(self.data_manager)
        main_layout.addWidget(self.calendar)
        
        # Panel de controles
        controls_widget = QWidget()
        controls_widget.setMaximumWidth(350)
        controls_layout = QVBoxLayout(controls_widget)
        
        # Sección: Eventos individuales
        controls_layout.addWidget(QLabel("📅 EVENTOS INDIVIDUALES:"))
        
        btn_single_critical = QPushButton("1 Evento Muy Importante")
        btn_single_critical.setMinimumHeight(35)
        btn_single_critical.setStyleSheet("background-color: #E74C3C; color: white; font-weight: bold;")
        btn_single_critical.clicked.connect(lambda: self.create_single_event(4))
        controls_layout.addWidget(btn_single_critical)
        
        btn_single_important = QPushButton("1 Evento Importante")
        btn_single_important.setMinimumHeight(35)
        btn_single_important.setStyleSheet("background-color: #E67E22; color: white; font-weight: bold;")
        btn_single_important.clicked.connect(lambda: self.create_single_event(3))
        controls_layout.addWidget(btn_single_important)
        
        btn_single_low = QPushButton("1 Evento Poco Importante")
        btn_single_low.setMinimumHeight(35)
        btn_single_low.setStyleSheet("background-color: #F39C12; color: white; font-weight: bold;")
        btn_single_low.clicked.connect(lambda: self.create_single_event(2))
        controls_layout.addWidget(btn_single_low)
        
        btn_single_normal = QPushButton("1 Evento Normal")
        btn_single_normal.setMinimumHeight(35)
        btn_single_normal.setStyleSheet("background-color: #27AE60; color: white; font-weight: bold;")
        btn_single_normal.clicked.connect(lambda: self.create_single_event(1))
        controls_layout.addWidget(btn_single_normal)
        
        controls_layout.addWidget(QLabel(""))  # Espaciador
        
        # Sección: Múltiples eventos
        controls_layout.addWidget(QLabel("📊 MÚLTIPLES EVENTOS:"))
        
        btn_multiple_same = QPushButton("3 Eventos Misma Prioridad")
        btn_multiple_same.setMinimumHeight(35)
        btn_multiple_same.setStyleSheet("background-color: #9B59B6; color: white; font-weight: bold;")
        btn_multiple_same.clicked.connect(self.create_multiple_same_priority)
        controls_layout.addWidget(btn_multiple_same)
        
        btn_mixed_priorities = QPushButton("Eventos Prioridades Mixtas")
        btn_mixed_priorities.setMinimumHeight(35)
        btn_mixed_priorities.setStyleSheet("background-color: #34495E; color: white; font-weight: bold;")
        btn_mixed_priorities.clicked.connect(self.create_mixed_priorities)
        controls_layout.addWidget(btn_mixed_priorities)
        
        btn_all_priorities = QPushButton("Todas las Prioridades")
        btn_all_priorities.setMinimumHeight(35)
        btn_all_priorities.setStyleSheet("background-color: #8E44AD; color: white; font-weight: bold;")
        btn_all_priorities.clicked.connect(self.create_all_priorities)
        controls_layout.addWidget(btn_all_priorities)
        
        controls_layout.addWidget(QLabel(""))  # Espaciador
        
        # Sección: Utilidades
        controls_layout.addWidget(QLabel("🔧 UTILIDADES:"))
        
        btn_clear_today = QPushButton("🗑️ Limpiar Hoy")
        btn_clear_today.setMinimumHeight(30)
        btn_clear_today.setStyleSheet("background-color: #E74C3C; color: white;")
        btn_clear_today.clicked.connect(self.clear_today_events)
        controls_layout.addWidget(btn_clear_today)
        
        btn_refresh = QPushButton("🔄 Actualizar")
        btn_refresh.setMinimumHeight(30)
        btn_refresh.setStyleSheet("background-color: #27AE60; color: white;")
        btn_refresh.clicked.connect(self.refresh_calendar)
        controls_layout.addWidget(btn_refresh)
        
        controls_layout.addStretch()
        
        # Información técnica
        tech_info = QLabel(
            "📐 ESPECIFICACIONES TÉCNICAS:\n\n"
            "• Área: Tercio inferior de cada día\n"
            "• Formato: Cuadrados perfectos\n"
            "• Espaciado: 2px entre cuadrados\n"
            "• Centrado: Horizontal automático\n"
            "• Tamaño: 8-20px (adaptativo)\n"
            "• Fuente: 6-10pt (proporcional)\n"
            "• Borde: 1px gris oscuro\n"
            "• Números: Siempre visibles\n"
            "• Orden: Mayor a menor prioridad"
        )
        tech_info.setAlignment(Qt.AlignLeft)
        tech_info.setStyleSheet("margin: 5px; color: #2C3E50; font-size: 8pt; background-color: #F8F9FA; padding: 8px; border-radius: 3px;")
        tech_info.setWordWrap(True)
        controls_layout.addWidget(tech_info)
        
        main_layout.addWidget(controls_widget)
        layout.addLayout(main_layout)
        
        # Área de resultados
        self.results_area = QTextEdit()
        self.results_area.setMaximumHeight(120)
        self.results_area.setPlaceholderText("Los resultados de las pruebas aparecerán aquí...")
        layout.addWidget(self.results_area)
    
    def create_single_event(self, priority):
        """Crear un solo evento con la prioridad especificada"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        priority_names = {4: "Muy Importante", 3: "Importante", 2: "Poco Importante", 1: "Normal"}
        priority_colors = {4: "#E74C3C", 3: "#E67E22", 2: "#F39C12", 1: "#27AE60"}
        
        event_data = {
            'title': f'Evento {priority_names[priority]}',
            'date': today,
            'time_start': '10:00:00',
            'time_end': '11:00:00',
            'priority': priority,
            'color': priority_colors[priority],
            'description': f'Evento único de prioridad {priority_names[priority]}'
        }
        
        event_id = self.data_manager.add_event(event_data)
        if event_id:
            self.results_area.append(f"✅ Creado 1 evento {priority_names[priority]} (debe mostrar cuadrado con '1')")
            self.refresh_calendar()
    
    def create_multiple_same_priority(self):
        """Crear múltiples eventos de la misma prioridad"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        events_data = [
            {
                'title': 'Evento Importante 1',
                'date': today,
                'time_start': '09:00:00',
                'time_end': '10:00:00',
                'priority': 3,
                'color': '#E67E22'
            },
            {
                'title': 'Evento Importante 2',
                'date': today,
                'time_start': '11:00:00',
                'time_end': '12:00:00',
                'priority': 3,
                'color': '#E67E22'
            },
            {
                'title': 'Evento Importante 3',
                'date': today,
                'time_start': '14:00:00',
                'time_end': '15:00:00',
                'priority': 3,
                'color': '#E67E22'
            }
        ]
        
        created_count = 0
        for event_data in events_data:
            event_id = self.data_manager.add_event(event_data)
            if event_id:
                created_count += 1
        
        self.results_area.append(f"✅ Creados {created_count} eventos Importantes (debe mostrar cuadrado naranja con '3')")
        self.refresh_calendar()
    
    def create_mixed_priorities(self):
        """Crear eventos con prioridades mixtas"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        events_data = [
            {'title': 'Muy Importante 1', 'priority': 4, 'color': '#E74C3C', 'time_start': '09:00:00'},
            {'title': 'Muy Importante 2', 'priority': 4, 'color': '#E74C3C', 'time_start': '10:00:00'},
            {'title': 'Normal 1', 'priority': 1, 'color': '#27AE60', 'time_start': '11:00:00'},
            {'title': 'Poco Importante 1', 'priority': 2, 'color': '#F39C12', 'time_start': '12:00:00'}
        ]
        
        created_count = 0
        for event_data in events_data:
            full_event = {
                'title': event_data['title'],
                'date': today,
                'time_start': event_data['time_start'],
                'time_end': '13:00:00',
                'priority': event_data['priority'],
                'color': event_data['color'],
                'description': f"Evento de prueba prioridad {event_data['priority']}"
            }
            
            event_id = self.data_manager.add_event(full_event)
            if event_id:
                created_count += 1
        
        self.results_area.append(f"✅ Creados {created_count} eventos mixtos (debe mostrar: rojo '2', amarillo '1', verde '1')")
        self.refresh_calendar()
    
    def create_all_priorities(self):
        """Crear eventos de todas las prioridades"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        priorities_data = [
            (4, "Muy Importante", "#E74C3C"),
            (3, "Importante", "#E67E22"),
            (2, "Poco Importante", "#F39C12"),
            (1, "Normal", "#27AE60")
        ]
        
        created_count = 0
        for priority, name, color in priorities_data:
            event_data = {
                'title': f'Evento {name}',
                'date': today,
                'time_start': f'{9 + priority}:00:00',
                'time_end': f'{10 + priority}:00:00',
                'priority': priority,
                'color': color,
                'description': f'Evento de prioridad {name}'
            }
            
            event_id = self.data_manager.add_event(event_data)
            if event_id:
                created_count += 1
        
        self.results_area.append(f"✅ Creados {created_count} eventos (debe mostrar 4 cuadrados: rojo, naranja, amarillo, verde)")
        self.refresh_calendar()
    
    def clear_today_events(self):
        """Limpiar eventos de hoy"""
        today = datetime.now().strftime("%Y-%m-%d")
        events = self.data_manager.get_events_by_date(today)
        
        deleted_count = 0
        for event in events:
            if self.data_manager.delete_event(event['id']):
                deleted_count += 1
        
        self.results_area.append(f"🗑️ Eliminados {deleted_count} eventos de hoy")
        self.refresh_calendar()
    
    def refresh_calendar(self):
        """Actualizar el calendario"""
        self.calendar.refresh_events()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestPrioritySquaresWindow()
    window.show()
    
    sys.exit(app.exec_())

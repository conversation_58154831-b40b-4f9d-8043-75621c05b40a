#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar que los relojes mantienen tamaño fijo
pero el contenedor es completamente redimensionable
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QLabel, QTextEdit, QSplitter, QPushButton, QHBoxLayout)
from PyQt5.QtCore import Qt

from world_clocks_widget import WorldClocksWidget
from data_manager import DataManager
from styles import apply_styles


class TestFixedClocksFlexibleContainerWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prueba: Relojes Fijos + Contenedor Flexible")
        self.setGeometry(100, 100, 1000, 700)
        
        # Crear data manager
        self.data_manager = DataManager()
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Título
        title = QLabel("Relojes de Tamaño Fijo + Contenedor Redimensionable")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16pt; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Instrucciones
        instructions = QLabel(
            "🎯 CONFIGURACIÓN ACTUAL:\n"
            "✅ Relojes individuales: Tamaño FIJO (220x180 a 280x250 píxeles)\n"
            "✅ Contenedor de relojes: REDIMENSIONABLE (mínimo 250x200)\n"
            "✅ Barra separadora: Sin límites artificiales\n"
            "✅ Scroll automático: Si hay muchos relojes\n\n"
            "🔧 PRUEBA:\n"
            "• Arrastra la barra separadora - el contenedor cambia de tamaño\n"
            "• Los relojes mantienen su tamaño fijo dentro del contenedor\n"
            "• Agrega más relojes para ver el scroll horizontal"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("margin: 10px; color: #2980B9; font-size: 10pt; background-color: #EBF5FB; padding: 10px; border-radius: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Crear splitter vertical
        splitter = QSplitter(Qt.Vertical)
        splitter.setHandleWidth(8)
        splitter.setChildrenCollapsible(True)
        
        # Sección superior: Simulación de eventos
        events_section = QWidget()
        events_layout = QVBoxLayout(events_section)
        
        events_title = QLabel("📅 Sección de Eventos")
        events_title.setStyleSheet("font-size: 14pt; font-weight: bold; color: #2C3E50; padding: 5px;")
        events_layout.addWidget(events_title)
        
        events_content = QTextEdit()
        events_content.setPlainText(
            "Esta sección simula la lista de eventos.\n\n"
            "Características del contenedor de relojes:\n"
            "• Los relojes individuales mantienen tamaño fijo\n"
            "• El contenedor se redimensiona con la barra separadora\n"
            "• Scroll horizontal automático si hay muchos relojes\n"
            "• Mínimo 250x200 píxeles para mostrar al menos un reloj\n\n"
            "Arrastra la barra separadora para cambiar el tamaño del contenedor."
        )
        events_layout.addWidget(events_content)
        
        # Sección inferior: Relojes mundiales
        self.world_clocks = WorldClocksWidget(self.data_manager)
        
        # Agregar secciones al splitter
        splitter.addWidget(events_section)
        splitter.addWidget(self.world_clocks)
        
        # Configurar comportamiento del splitter
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
        
        # Tamaños iniciales
        splitter.setSizes([300, 300])
        
        # Conectar señal para mostrar cambios
        splitter.splitterMoved.connect(lambda: self.show_splitter_info(splitter))
        
        # Guardar referencia
        self.test_splitter = splitter
        
        layout.addWidget(splitter)
        
        # Botones de prueba
        buttons_layout = QHBoxLayout()
        
        btn_add_clocks = QPushButton("Agregar 3 Relojes")
        btn_add_clocks.clicked.connect(self.add_multiple_clocks)
        buttons_layout.addWidget(btn_add_clocks)
        
        btn_small_container = QPushButton("Contenedor Pequeño")
        btn_small_container.clicked.connect(lambda: splitter.setSizes([500, 150]))
        buttons_layout.addWidget(btn_small_container)
        
        btn_large_container = QPushButton("Contenedor Grande")
        btn_large_container.clicked.connect(lambda: splitter.setSizes([200, 450]))
        buttons_layout.addWidget(btn_large_container)
        
        btn_info = QPushButton("Info Tamaños")
        btn_info.clicked.connect(lambda: self.show_detailed_info())
        buttons_layout.addWidget(btn_info)
        
        layout.addLayout(buttons_layout)
        
        # Información en tiempo real
        self.info_label = QLabel("Tamaños del contenedor: [300, 300]")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setStyleSheet("margin: 10px; color: #27AE60; font-size: 12pt; font-weight: bold;")
        layout.addWidget(self.info_label)
    
    def add_multiple_clocks(self):
        """Agregar múltiples relojes para probar el scroll"""
        from clock_widget import ClockWidget
        
        new_clocks_data = [
            ("Asia/Tokyo", "Tokio"),
            ("America/New_York", "Nueva York"),
            ("Australia/Sydney", "Sydney")
        ]
        
        for timezone, city in new_clocks_data:
            if len(self.world_clocks.clocks) < 6:
                new_clock = ClockWidget(timezone=timezone, city_name=city)
                self.world_clocks.add_clock_widget(new_clock)
        
        self.world_clocks.save_clocks_config()
        print(f"Relojes agregados. Total: {len(self.world_clocks.clocks)}")
    
    def show_splitter_info(self, splitter):
        """Mostrar información actual del splitter"""
        sizes = splitter.sizes()
        container_size = self.world_clocks.size()
        self.info_label.setText(
            f"Contenedor: {sizes[1]}px altura | "
            f"Tamaño real: {container_size.width()}x{container_size.height()}px | "
            f"Relojes: {len(self.world_clocks.clocks)}"
        )
        print(f"Contenedor redimensionado - Altura: {sizes[1]}px")
    
    def show_detailed_info(self):
        """Mostrar información detallada"""
        container_size = self.world_clocks.size()
        splitter_sizes = self.test_splitter.sizes()
        
        print("\n=== INFORMACIÓN DETALLADA ===")
        print(f"Tamaños del splitter: {splitter_sizes}")
        print(f"Tamaño del contenedor: {container_size.width()}x{container_size.height()}")
        print(f"Número de relojes: {len(self.world_clocks.clocks)}")
        
        for i, clock in enumerate(self.world_clocks.clocks):
            clock_size = clock.size()
            print(f"  Reloj {i+1} ({clock.city_name}): {clock_size.width()}x{clock_size.height()}")
        print("==============================\n")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # Aplicar estilos
    apply_styles(app)
    
    window = TestFixedClocksFlexibleContainerWindow()
    window.show()
    
    sys.exit(app.exec_())

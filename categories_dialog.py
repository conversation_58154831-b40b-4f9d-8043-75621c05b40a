#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
                            QPushButton, QLabel, QLineEdit, QColorDialog, QMessageBox,
                            QDialogButtonBox, QFormLayout)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QColor, QBrush, QIcon


class CategoryDialog(QDialog):
    def __init__(self, data_manager, parent=None, category=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.category = category
        
        if category:
            self.setWindowTitle("Editar categoría")
        else:
            self.setWindowTitle("Nueva categoría")
        
        self.init_ui()
        
        # Si estamos editando una categoría, rellenar los campos
        if category:
            self.fill_form()
    
    def init_ui(self):
        """Inicializar la interfaz de usuario"""
        layout = QVBoxLayout(self)
        
        form = QFormLayout()
        
        # Nombre
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Nombre de la categoría")
        form.addRow("Nombre:", self.name_edit)
        
        # Color
        color_layout = QHBoxLayout()
        self.color_preview = QLabel()
        self.color_preview.setFixedSize(20, 20)
        self.color_preview.setStyleSheet("background-color: #1E90FF; border: 1px solid gray;")
        self.current_color = QColor("#1E90FF")
        
        color_button = QPushButton("Seleccionar")
        color_button.clicked.connect(self.select_color)
        
        color_layout.addWidget(self.color_preview)
        color_layout.addWidget(color_button)
        color_layout.addStretch()
        
        form.addRow("Color:", color_layout)
        
        layout.addLayout(form)
        
        # Botones
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        
        layout.addWidget(buttons)
    
    def select_color(self):
        """Seleccionar color para la categoría"""
        color = QColorDialog.getColor(self.current_color, self, "Seleccionar color")
        if color.isValid():
            self.current_color = color
            self.color_preview.setStyleSheet(f"background-color: {color.name()}; border: 1px solid gray;")
    
    def fill_form(self):
        """Rellenar el formulario con los datos de la categoría existente"""
        if not self.category:
            return
        
        self.name_edit.setText(self.category['name'])
        
        color = self.category.get('color', '#1E90FF')
        self.current_color = QColor(color)
        self.color_preview.setStyleSheet(f"background-color: {color}; border: 1px solid gray;")
    
    def get_category_data(self):
        """Obtener los datos de la categoría desde el formulario"""
        return {
            'name': self.name_edit.text(),
            'color': self.current_color.name()
        }


class CategoriesDialog(QDialog):
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        
        self.setWindowTitle("Gestionar categorías")
        self.resize(400, 300)
        
        self.init_ui()
        self.load_categories()
    
    def init_ui(self):
        """Inicializar la interfaz de usuario"""
        layout = QVBoxLayout(self)
        
        # Lista de categorías
        self.categories_list = QListWidget()
        self.categories_list.setAlternatingRowColors(True)
        layout.addWidget(self.categories_list)
        
        # Botones
        buttons_layout = QHBoxLayout()
        
        self.btn_add = QPushButton("Nueva")
        self.btn_add.clicked.connect(self.add_category)
        buttons_layout.addWidget(self.btn_add)
        
        self.btn_edit = QPushButton("Editar")
        self.btn_edit.clicked.connect(self.edit_category)
        self.btn_edit.setEnabled(False)
        buttons_layout.addWidget(self.btn_edit)
        
        self.btn_delete = QPushButton("Eliminar")
        self.btn_delete.clicked.connect(self.delete_category)
        self.btn_delete.setEnabled(False)
        buttons_layout.addWidget(self.btn_delete)
        
        layout.addLayout(buttons_layout)
        
        # Botones de diálogo
        buttons = QDialogButtonBox(QDialogButtonBox.Close)
        buttons.rejected.connect(self.reject)
        
        layout.addWidget(buttons)
        
        # Conectar evento de selección
        self.categories_list.itemSelectionChanged.connect(self.enable_buttons)
        self.categories_list.itemDoubleClicked.connect(self.edit_category)
    
    def load_categories(self):
        """Cargar categorías en la lista"""
        self.categories_list.clear()
        
        categories = self.data_manager.get_all_categories()
        
        for category in categories:
            item = QListWidgetItem(category['name'])
            item.setData(Qt.UserRole, category['id'])
            
            # Añadir color
            color = category.get('color', '#1E90FF')
            item.setForeground(QBrush(QColor(color)))
            
            self.categories_list.addItem(item)
    
    def enable_buttons(self):
        """Habilitar o deshabilitar botones según la selección"""
        selected = len(self.categories_list.selectedItems()) > 0
        self.btn_edit.setEnabled(selected)
        self.btn_delete.setEnabled(selected)
    
    def add_category(self):
        """Añadir una nueva categoría"""
        dialog = CategoryDialog(self.data_manager, self)
        
        if dialog.exec_():
            category_data = dialog.get_category_data()
            
            category_id = self.data_manager.add_category(
                category_data['name'], 
                category_data['color']
            )
            
            if category_id:
                self.load_categories()
    
    def edit_category(self):
        """Editar la categoría seleccionada"""
        selected_items = self.categories_list.selectedItems()
        if not selected_items:
            return
        
        item = selected_items[0]
        category_id = item.data(Qt.UserRole)
        
        if not category_id:
            return
        
        category = self.data_manager.get_category(category_id)
        if not category:
            return
        
        dialog = CategoryDialog(self.data_manager, self, category)
        
        if dialog.exec_():
            category_data = dialog.get_category_data()
            
            success = self.data_manager.update_category(
                category_id,
                category_data['name'],
                category_data['color']
            )
            
            if success:
                self.load_categories()
    
    def delete_category(self):
        """Eliminar la categoría seleccionada"""
        selected_items = self.categories_list.selectedItems()
        if not selected_items:
            return
        
        item = selected_items[0]
        category_id = item.data(Qt.UserRole)
        
        if not category_id:
            return
        
        confirm = QMessageBox.question(
            self, "Confirmar eliminación", 
            "¿Está seguro de eliminar esta categoría? Los eventos y notas asociados a esta categoría se mantendrán pero ya no estarán categorizados.",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if confirm == QMessageBox.Yes:
            success = self.data_manager.delete_category(category_id)
            if success:
                self.load_categories()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuración responsive para adaptar la aplicación a diferentes resoluciones de pantalla
"""

from PyQt5.QtWidgets import QApplication

class ResponsiveConfig:
    """Clase para manejar configuración responsive"""
    
    def __init__(self):
        self.scale_factor = 1.0
        self.screen_width = 1920
        self.screen_height = 1080
        self.calculate_scale_factor()
    
    def calculate_scale_factor(self):
        """Calcular factor de escala basado en la resolución actual"""
        desktop = QApplication.desktop()
        if desktop:
            screen_rect = desktop.screenGeometry()
            self.screen_width = screen_rect.width()
            self.screen_height = screen_rect.height()
            
            # Resolución base: 1920x1080
            base_width = 1920
            base_height = 1080
            
            scale_factor_x = self.screen_width / base_width
            scale_factor_y = self.screen_height / base_height
            self.scale_factor = min(scale_factor_x, scale_factor_y)
            
            # Aplicar límites
            self.scale_factor = max(0.7, min(self.scale_factor, 2.0))
    
    def get_scaled_size(self, base_size):
        """Obtener tamaño escalado"""
        if isinstance(base_size, tuple):
            return (int(base_size[0] * self.scale_factor), int(base_size[1] * self.scale_factor))
        else:
            return int(base_size * self.scale_factor)
    
    def get_scaled_font_size(self, base_font_size):
        """Obtener tamaño de fuente escalado"""
        return max(8, int(base_font_size * self.scale_factor))
    
    def get_button_sizes(self):
        """Obtener tamaños de botones responsive"""
        return {
            'small': self.get_scaled_size((80, 45)),
            'medium': self.get_scaled_size((100, 45)),
            'large': self.get_scaled_size((120, 45)),
            'control': self.get_scaled_size((120, 45))
        }
    
    def get_font_sizes(self):
        """Obtener tamaños de fuente responsive"""
        return {
            'small': self.get_scaled_font_size(10),
            'medium': self.get_scaled_font_size(12),
            'large': self.get_scaled_font_size(14),
            'xlarge': self.get_scaled_font_size(16),
            'display': self.get_scaled_font_size(28)
        }
    
    def get_spacing_sizes(self):
        """Obtener tamaños de espaciado responsive"""
        return {
            'small': self.get_scaled_size(5),
            'medium': self.get_scaled_size(10),
            'large': self.get_scaled_size(20)
        }
    
    def get_calendar_config(self):
        """Obtener configuración responsive para el calendario"""
        font_sizes = self.get_font_sizes()
        return {
            'day_header_font_size': font_sizes['xlarge'],
            'day_cell_font_size': font_sizes['medium'],
            'navigation_font_size': font_sizes['large'],
            'min_cell_height': self.get_scaled_size(35),
            'header_padding': self.get_scaled_size(8)
        }
    
    def get_table_config(self):
        """Obtener configuración responsive para tablas"""
        font_sizes = self.get_font_sizes()
        return {
            'font_size': font_sizes['medium'],
            'header_font_size': font_sizes['medium'],
            'row_height': self.get_scaled_size(30),
            'header_height': self.get_scaled_size(35),
            'cell_padding': self.get_scaled_size(6)
        }
    
    def get_widget_config(self):
        """Obtener configuración responsive para widgets"""
        return {
            'countdown_display_font': self.get_scaled_font_size(28),
            'button_font': self.get_scaled_font_size(12),
            'label_font': self.get_scaled_font_size(12),
            'title_font': self.get_scaled_font_size(16)
        }

# Instancia global de configuración responsive
responsive_config = ResponsiveConfig()

def get_responsive_config():
    """Obtener la configuración responsive global"""
    return responsive_config

def update_responsive_config():
    """Actualizar la configuración responsive"""
    global responsive_config
    responsive_config = ResponsiveConfig()
    return responsive_config
